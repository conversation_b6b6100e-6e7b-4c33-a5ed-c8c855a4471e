import type { Timestamp } from 'firebase/firestore'
import type { User, Workspace, Profile } from './auth'

/**
 * Complete user session state for client-side management
 */
export interface UserSession {
  // Core authentication
  user: SessionUser | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Multi-tenant context
  currentWorkspace: SessionWorkspace | null
  currentProfile: SessionProfile | null
  workspaces: SessionWorkspace[]
  
  // Session metadata
  sessionId: string
  createdAt: string             // ISO timestamp
  expiresAt: string            // ISO timestamp
  lastActiveAt: string         // ISO timestamp
  
  // Firebase token information
  token: {
    idToken?: string
    accessToken?: string
    refreshToken?: string
    expirationTime?: number
  } | null
  
  // Error state
  error: string | null
}

/**
 * Simplified user data for session storage
 */
export interface SessionUser {
  id: string
  email: string
  displayName: string
  photoURL?: string
  emailVerified: boolean
  lastLoginAt?: string         // ISO timestamp
}

/**
 * Simplified workspace data for session storage
 */
export interface SessionWorkspace {
  id: string
  name: string
  slug: string
  ownerId: string
  plan: 'free' | 'starter' | 'pro' | 'enterprise'
  
  // User's role in this workspace
  userRole: string
  
  // Timestamps
  createdAt: string            // ISO timestamp
  updatedAt: string            // ISO timestamp
}

/**
 * Simplified profile data for session storage
 */
export interface SessionProfile {
  id: string
  userId: string
  workspaceId: string
  displayName: string
  avatar?: string
  role: string
  permissions: string[]
  
  // Timestamps
  createdAt: string            // ISO timestamp
  lastActiveAt?: string        // ISO timestamp
}

/**
 * Server-side session storage model (stored in HTTP-only cookies)
 */
export interface ServerSession {
  // Session identification
  sessionId: string
  userId: string
  
  // Current workspace context
  currentWorkspaceId: string
  currentProfileId: string
  
  // Security information
  ipAddress: string
  userAgent: string
  
  // Session lifecycle
  createdAt: Timestamp
  lastActiveAt: Timestamp
  expiresAt: Timestamp
  
  // Firebase token (for server-side verification)
  firebaseToken: {
    idToken: string
    expirationTime: number
  }
  
  // Additional session data
  sessionData?: Record<string, any>
}

/**
 * Session creation/update payload
 */
export interface SessionPayload {
  user: SessionUser
  currentWorkspace: SessionWorkspace
  currentProfile: SessionProfile
  workspaces: SessionWorkspace[]
  token: UserSession['token']
}

/**
 * Session validation result
 */
export interface SessionValidationResult {
  isValid: boolean
  session?: ServerSession
  user?: SessionUser
  workspace?: SessionWorkspace
  profile?: SessionProfile
  error?: string
}

/**
 * Workspace switching payload
 */
export interface WorkspaceSwitchPayload {
  workspaceId: string
  userId: string
}

/**
 * Session management utilities
 */
export interface SessionManager {
  // Session lifecycle
  createSession(payload: SessionPayload): Promise<UserSession>
  updateUserSession(updates: Partial<SessionPayload>): Promise<UserSession>
  refreshSession(): Promise<UserSession>
  destroySession(): Promise<void>
  
  // Workspace management
  switchWorkspace(workspaceId: string): Promise<UserSession>
  getCurrentSession(): UserSession | null
  
  // Session validation
  validateSession(): Promise<SessionValidationResult>
  isSessionExpired(): boolean
  
  // Event handling
  onSessionChange(callback: (session: UserSession | null) => void): () => void
  onWorkspaceChange(callback: (workspace: SessionWorkspace | null) => void): () => void
}

/**
 * Session cookie configuration
 */
export interface SessionConfig {
  cookieName: string
  maxAge: number               // Seconds (default: 7 days)
  httpOnly: boolean           // Always true for security
  secure: boolean             // True in production
  sameSite: 'lax' | 'strict' | 'none'
  domain?: string
  path: string                // Default: '/'
}

/**
 * Default session configuration
 */
export const DEFAULT_SESSION_CONFIG: SessionConfig = {
  cookieName: 'omni-session',
  maxAge: 60 * 60 * 24 * 7,   // 7 days
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax',
  path: '/'
}

/**
 * Session storage keys for client-side state
 */
export const SESSION_STORAGE_KEYS = {
  USER_SESSION: 'omni:user-session',
  WORKSPACE_PREFERENCES: 'omni:workspace-preferences',
  UI_STATE: 'omni:ui-state'
} as const

/**
 * Session event types for reactive updates
 */
export type SessionEventType = 
  | 'session-created'
  | 'session-updated'
  | 'session-destroyed'
  | 'workspace-switched'
  | 'profile-updated'
  | 'token-refreshed'
  | 'session-expired'

/**
 * Session event payload
 */
export interface SessionEvent {
  type: SessionEventType
  data: {
    session?: UserSession
    workspace?: SessionWorkspace
    profile?: SessionProfile
    error?: string
  }
  timestamp: string            // ISO timestamp
}

/**
 * Session metrics for monitoring and analytics
 */
export interface SessionMetrics {
  sessionId: string
  userId: string
  workspaceId: string
  
  // Session statistics
  duration: number             // Milliseconds
  pageViews: number
  actionsPerformed: number
  
  // Technical metrics
  userAgent: string
  ipAddress: string
  country?: string
  city?: string
  
  // Performance metrics
  loadTime: number             // Milliseconds
  errorCount: number
  
  // Timestamps
  startedAt: Timestamp
  endedAt?: Timestamp
  lastActivityAt: Timestamp
}

/**
 * Utility functions for session management
 */
export interface SessionUtils {
  // Serialization
  serializeSession(session: UserSession): string
  deserializeSession(data: string): UserSession | null
  
  // Security
  generateSessionId(): string
  hashToken(token: string): string
  verifyTokenHash(token: string, hash: string): boolean
  
  // Validation
  isValidEmail(email: string): boolean
  isSessionDataValid(session: UserSession): boolean
  
  // Time management
  formatTimestamp(timestamp: Timestamp): string
  parseTimestamp(iso: string): Timestamp
  isExpired(expiresAt: string): boolean
  
  // Workspace utilities
  canAccessWorkspace(userId: string, workspaceId: string): Promise<boolean>
  getUserWorkspaces(userId: string): Promise<SessionWorkspace[]>
  getWorkspaceProfile(userId: string, workspaceId: string): Promise<SessionProfile | null>
}