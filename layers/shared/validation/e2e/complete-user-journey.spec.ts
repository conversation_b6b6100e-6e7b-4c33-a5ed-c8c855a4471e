import { test, expect, Page } from '@playwright/test'
import { 
  createTestUser, 
  createTestWorkspace, 
  cleanupTestData 
} from '../../tests/setup/firebase-test-setup'

test.describe('Complete User Journey Validation', () => {
  let page: Page
  let testUser: any
  let testWorkspace: any

  test.beforeAll(async () => {
    // Create test data
    testUser = await createTestUser('<EMAIL>')
    testWorkspace = await createTestWorkspace('journey-test-workspace')
  })

  test.afterAll(async () => {
    await cleanupTestData()
  })

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage
    await page.goto('/')
  })

  test.describe('User Registration and Onboarding', () => {
    test('should complete user registration flow', async () => {
      // Navigate to signup page
      await page.click('text=Sign Up')
      await expect(page).toHaveURL(/\/auth\/signup/)

      // Fill registration form
      await page.fill('[data-testid="email-input"]', '<EMAIL>')
      await page.fill('[data-testid="password-input"]', 'SecurePassword123!')
      await page.fill('[data-testid="confirm-password-input"]', 'SecurePassword123!')
      await page.fill('[data-testid="display-name-input"]', 'New User')

      // Submit registration
      await page.click('[data-testid="signup-button"]')

      // Verify successful registration
      await expect(page).toHaveURL(/\/onboarding/)
      await expect(page.locator('[data-testid="welcome-message"]')).toContainText('Welcome, New User!')

      // Complete onboarding - Create workspace
      await page.fill('[data-testid="workspace-name-input"]', 'My First Workspace')
      await page.fill('[data-testid="workspace-description-input"]', 'This is my first workspace')
      await page.click('[data-testid="create-workspace-button"]')

      // Verify workspace creation
      await expect(page).toHaveURL(/\/dashboard/)
      await expect(page.locator('[data-testid="workspace-name"]')).toContainText('My First Workspace')
    })

    test('should handle registration errors gracefully', async () => {
      await page.goto('/auth/signup')

      // Test duplicate email
      await page.fill('[data-testid="email-input"]', '<EMAIL>')
      await page.fill('[data-testid="password-input"]', 'Password123!')
      await page.fill('[data-testid="confirm-password-input"]', 'Password123!')
      await page.fill('[data-testid="display-name-input"]', 'Test User')

      await page.click('[data-testid="signup-button"]')

      await expect(page.locator('[data-testid="error-message"]')).toContainText('Email already exists')

      // Test weak password
      await page.fill('[data-testid="password-input"]', 'weak')
      await page.fill('[data-testid="confirm-password-input"]', 'weak')
      await page.click('[data-testid="signup-button"]')

      await expect(page.locator('[data-testid="error-message"]')).toContainText('Password too weak')
    })
  })

  test.describe('Authentication Flow', () => {
    test('should complete login flow', async () => {
      // Navigate to login page
      await page.click('text=Login')
      await expect(page).toHaveURL(/\/auth\/login/)

      // Fill login form
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', 'TestPassword123!')

      // Submit login
      await page.click('[data-testid="login-button"]')

      // Verify successful login
      await expect(page).toHaveURL(/\/dashboard/)
      await expect(page.locator('[data-testid="user-menu"]')).toContainText(testUser.displayName)
    })

    test('should handle login errors', async () => {
      await page.goto('/auth/login')

      // Test invalid credentials
      await page.fill('[data-testid="email-input"]', '<EMAIL>')
      await page.fill('[data-testid="password-input"]', 'wrongpassword')
      await page.click('[data-testid="login-button"]')

      await expect(page.locator('[data-testid="error-message"]')).toContainText('Invalid credentials')
    })

    test('should handle password reset', async () => {
      await page.goto('/auth/login')

      // Click forgot password
      await page.click('text=Forgot Password?')
      await expect(page).toHaveURL(/\/auth\/recover/)

      // Enter email
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.click('[data-testid="reset-button"]')

      // Verify reset email sent
      await expect(page.locator('[data-testid="success-message"]')).toContainText('Reset email sent')
    })

    test('should handle session management', async () => {
      // Login first
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', 'TestPassword123!')
      await page.click('[data-testid="login-button"]')

      // Verify session persistence
      await page.reload()
      await expect(page).toHaveURL(/\/dashboard/)
      await expect(page.locator('[data-testid="user-menu"]')).toContainText(testUser.displayName)

      // Test logout
      await page.click('[data-testid="user-menu"]')
      await page.click('[data-testid="logout-button"]')

      // Verify logout
      await expect(page).toHaveURL(/\/auth\/login/)
      await expect(page.locator('[data-testid="user-menu"]')).not.toBeVisible()
    })
  })

  test.describe('Workspace Management', () => {
    test.beforeEach(async () => {
      // Login before each test
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', 'TestPassword123!')
      await page.click('[data-testid="login-button"]')
    })

    test('should create new workspace', async () => {
      // Navigate to workspace creation
      await page.click('[data-testid="workspace-menu"]')
      await page.click('[data-testid="create-workspace-button"]')

      // Fill workspace form
      await page.fill('[data-testid="workspace-name-input"]', 'New Test Workspace')
      await page.fill('[data-testid="workspace-description-input"]', 'A new workspace for testing')
      await page.click('[data-testid="create-button"]')

      // Verify workspace created
      await expect(page.locator('[data-testid="workspace-name"]')).toContainText('New Test Workspace')
      await expect(page.locator('[data-testid="workspace-description"]')).toContainText('A new workspace for testing')
    })

    test('should switch between workspaces', async () => {
      // Create second workspace
      await page.click('[data-testid="workspace-menu"]')
      await page.click('[data-testid="create-workspace-button"]')
      await page.fill('[data-testid="workspace-name-input"]', 'Second Workspace')
      await page.click('[data-testid="create-button"]')

      // Verify current workspace
      await expect(page.locator('[data-testid="workspace-name"]')).toContainText('Second Workspace')

      // Switch to first workspace
      await page.click('[data-testid="workspace-menu"]')
      await page.click('[data-testid="workspace-item"]:has-text("My First Workspace")')

      // Verify workspace switched
      await expect(page.locator('[data-testid="workspace-name"]')).toContainText('My First Workspace')
    })

    test('should manage workspace settings', async () => {
      // Navigate to workspace settings
      await page.click('[data-testid="workspace-menu"]')
      await page.click('[data-testid="workspace-settings"]')

      // Update workspace settings
      await page.fill('[data-testid="workspace-name-input"]', 'Updated Workspace Name')
      await page.check('[data-testid="allow-public-signup"]')
      await page.selectOption('[data-testid="default-role-select"]', 'member')
      await page.click('[data-testid="save-settings-button"]')

      // Verify settings updated
      await expect(page.locator('[data-testid="success-message"]')).toContainText('Settings updated')
      await expect(page.locator('[data-testid="workspace-name"]')).toContainText('Updated Workspace Name')
    })

    test('should manage team members', async () => {
      // Navigate to team management
      await page.click('[data-testid="workspace-menu"]')
      await page.click('[data-testid="team-management"]')

      // Invite new member
      await page.click('[data-testid="invite-member-button"]')
      await page.fill('[data-testid="member-email-input"]', '<EMAIL>')
      await page.selectOption('[data-testid="member-role-select"]', 'member')
      await page.click('[data-testid="send-invite-button"]')

      // Verify invitation sent
      await expect(page.locator('[data-testid="success-message"]')).toContainText('Invitation sent')
      await expect(page.locator('[data-testid="pending-invitations"]')).toContainText('<EMAIL>')
    })
  })

  test.describe('Data Operations', () => {
    test.beforeEach(async () => {
      // Login and ensure we're in test workspace
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', 'TestPassword123!')
      await page.click('[data-testid="login-button"]')
    })

    test('should create and manage documents', async () => {
      // Navigate to documents
      await page.click('[data-testid="documents-link"]')

      // Create new document
      await page.click('[data-testid="create-document-button"]')
      await page.fill('[data-testid="document-title-input"]', 'Test Document')
      await page.fill('[data-testid="document-content-input"]', 'This is a test document with some content')
      await page.click('[data-testid="save-document-button"]')

      // Verify document created
      await expect(page.locator('[data-testid="document-title"]')).toContainText('Test Document')
      await expect(page.locator('[data-testid="document-content"]')).toContainText('This is a test document')

      // Edit document
      await page.click('[data-testid="edit-document-button"]')
      await page.fill('[data-testid="document-title-input"]', 'Updated Test Document')
      await page.click('[data-testid="save-document-button"]')

      // Verify document updated
      await expect(page.locator('[data-testid="document-title"]')).toContainText('Updated Test Document')
    })

    test('should search and filter documents', async () => {
      // Navigate to documents
      await page.click('[data-testid="documents-link"]')

      // Create multiple documents for testing
      const documents = [
        { title: 'Project Alpha', content: 'Details about project alpha' },
        { title: 'Project Beta', content: 'Details about project beta' },
        { title: 'Meeting Notes', content: 'Notes from the team meeting' }
      ]

      for (const doc of documents) {
        await page.click('[data-testid="create-document-button"]')
        await page.fill('[data-testid="document-title-input"]', doc.title)
        await page.fill('[data-testid="document-content-input"]', doc.content)
        await page.click('[data-testid="save-document-button"]')
        await page.click('[data-testid="back-to-documents"]')
      }

      // Test search functionality
      await page.fill('[data-testid="search-input"]', 'project')
      await page.click('[data-testid="search-button"]')

      // Verify search results
      await expect(page.locator('[data-testid="search-results"]')).toContainText('Project Alpha')
      await expect(page.locator('[data-testid="search-results"]')).toContainText('Project Beta')
      await expect(page.locator('[data-testid="search-results"]')).not.toContainText('Meeting Notes')

      // Test filters
      await page.selectOption('[data-testid="filter-by-type"]', 'documents')
      await page.click('[data-testid="apply-filter-button"]')

      // Verify filtered results
      await expect(page.locator('[data-testid="document-list"]')).toContainText('Project Alpha')
    })

    test('should handle file uploads', async () => {
      // Navigate to files
      await page.click('[data-testid="files-link"]')

      // Upload file
      const fileInput = page.locator('[data-testid="file-input"]')
      await fileInput.setInputFiles('test-file.pdf')

      // Verify file uploaded
      await expect(page.locator('[data-testid="file-list"]')).toContainText('test-file.pdf')
      await expect(page.locator('[data-testid="file-status"]')).toContainText('Uploaded successfully')
    })
  })

  test.describe('Settings and Preferences', () => {
    test.beforeEach(async () => {
      // Login before each test
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', 'TestPassword123!')
      await page.click('[data-testid="login-button"]')
    })

    test('should manage user profile', async () => {
      // Navigate to profile settings
      await page.click('[data-testid="user-menu"]')
      await page.click('[data-testid="profile-settings"]')

      // Update profile information
      await page.fill('[data-testid="display-name-input"]', 'Updated Display Name')
      await page.fill('[data-testid="bio-input"]', 'Updated bio information')
      await page.click('[data-testid="save-profile-button"]')

      // Verify profile updated
      await expect(page.locator('[data-testid="success-message"]')).toContainText('Profile updated')
      await expect(page.locator('[data-testid="display-name"]')).toContainText('Updated Display Name')
    })

    test('should manage user preferences', async () => {
      // Navigate to preferences
      await page.click('[data-testid="user-menu"]')
      await page.click('[data-testid="preferences"]')

      // Update preferences
      await page.selectOption('[data-testid="theme-select"]', 'dark')
      await page.selectOption('[data-testid="language-select"]', 'es')
      await page.check('[data-testid="email-notifications"]')
      await page.click('[data-testid="save-preferences-button"]')

      // Verify preferences updated
      await expect(page.locator('[data-testid="success-message"]')).toContainText('Preferences updated')
      await expect(page.locator('body')).toHaveClass(/dark-theme/)
    })

    test('should change password', async () => {
      // Navigate to security settings
      await page.click('[data-testid="user-menu"]')
      await page.click('[data-testid="security-settings"]')

      // Change password
      await page.fill('[data-testid="current-password-input"]', 'TestPassword123!')
      await page.fill('[data-testid="new-password-input"]', 'NewPassword123!')
      await page.fill('[data-testid="confirm-password-input"]', 'NewPassword123!')
      await page.click('[data-testid="change-password-button"]')

      // Verify password changed
      await expect(page.locator('[data-testid="success-message"]')).toContainText('Password changed')

      // Test login with new password
      await page.click('[data-testid="logout-button"]')
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', 'NewPassword123!')
      await page.click('[data-testid="login-button"]')

      await expect(page).toHaveURL(/\/dashboard/)
    })
  })

  test.describe('Error Handling and Edge Cases', () => {
    test('should handle network errors gracefully', async () => {
      // Login first
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', 'TestPassword123!')
      await page.click('[data-testid="login-button"]')

      // Simulate network error
      await page.route('**/api/**', route => route.abort())

      // Try to create document (should fail gracefully)
      await page.click('[data-testid="documents-link"]')
      await page.click('[data-testid="create-document-button"]')
      await page.fill('[data-testid="document-title-input"]', 'Network Error Test')
      await page.click('[data-testid="save-document-button"]')

      // Verify error handling
      await expect(page.locator('[data-testid="error-message"]')).toContainText('Network error')
      await expect(page.locator('[data-testid="retry-button"]')).toBeVisible()
    })

    test('should handle session expiration', async () => {
      // Login first
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', 'TestPassword123!')
      await page.click('[data-testid="login-button"]')

      // Simulate session expiration
      await page.evaluate(() => {
        localStorage.removeItem('firebase-auth-token')
        sessionStorage.clear()
      })

      // Try to access protected resource
      await page.click('[data-testid="documents-link"]')

      // Verify redirect to login
      await expect(page).toHaveURL(/\/auth\/login/)
      await expect(page.locator('[data-testid="error-message"]')).toContainText('Session expired')
    })

    test('should handle concurrent operations', async () => {
      // Login first
      await page.goto('/auth/login')
      await page.fill('[data-testid="email-input"]', testUser.email)
      await page.fill('[data-testid="password-input"]', 'TestPassword123!')
      await page.click('[data-testid="login-button"]')

      // Open multiple tabs
      const page2 = await page.context().newPage()
      await page2.goto('/dashboard')

      // Perform concurrent operations
      await Promise.all([
        page.click('[data-testid="create-document-button"]'),
        page2.click('[data-testid="create-document-button"]')
      ])

      // Verify both operations work
      await expect(page.locator('[data-testid="document-form"]')).toBeVisible()
      await expect(page2.locator('[data-testid="document-form"]')).toBeVisible()
    })
  })
})