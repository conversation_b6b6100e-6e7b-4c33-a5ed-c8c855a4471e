import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { 
  initializeFirebaseEmulators, 
  cleanupFirebaseEmulators,
  createTestUser,
  createTestWorkspace,
  createTestTenant 
} from '../../tests/setup/firebase-test-setup'
import { useAuth } from '../../composables/useAuth'
import { useFirestore } from '../../composables/useFirestore'
import { useVectorSearch } from '../../composables/useVectorSearch'
import { useDataApi } from '../../composables/useDataApi'
import { useSecurity } from '../../composables/useSecurity'

interface SecurityTestResult {
  testName: string
  passed: boolean
  vulnerabilities: string[]
  recommendations: string[]
  severity: 'low' | 'medium' | 'high' | 'critical'
}

class SecurityAuditor {
  private results: SecurityTestResult[] = []
  private authService: any
  private firestoreService: any
  private vectorSearchService: any
  private dataApiService: any
  private securityService: any

  constructor() {
    this.authService = useAuth()
    this.firestoreService = useFirestore('security-test')
    this.vectorSearchService = useVectorSearch()
    this.dataApiService = useDataApi()
    this.securityService = useSecurity()
  }

  private addResult(result: SecurityTestResult) {
    this.results.push(result)
  }

  async auditAuthentication(): Promise<SecurityTestResult[]> {
    const results: SecurityTestResult[] = []

    // Test 1: Password Policy Enforcement
    try {
      await this.authService.signUp('<EMAIL>', 'weak', 'Weak User')
      results.push({
        testName: 'Password Policy Enforcement',
        passed: false,
        vulnerabilities: ['Weak passwords accepted'],
        recommendations: ['Implement strong password requirements'],
        severity: 'high'
      })
    } catch (error) {
      results.push({
        testName: 'Password Policy Enforcement',
        passed: true,
        vulnerabilities: [],
        recommendations: [],
        severity: 'low'
      })
    }

    // Test 2: Brute Force Protection
    const bruteForceTries = []
    for (let i = 0; i < 10; i++) {
      try {
        await this.authService.signIn('<EMAIL>', 'wrongpassword')
        bruteForceTries.push(true)
      } catch (error) {
        bruteForceTries.push(false)
      }
    }

    const successfulBruteForce = bruteForceTries.filter(success => success).length
    results.push({
      testName: 'Brute Force Protection',
      passed: successfulBruteForce === 0,
      vulnerabilities: successfulBruteForce > 0 ? ['Brute force attacks possible'] : [],
      recommendations: successfulBruteForce > 0 ? ['Implement rate limiting and account lockout'] : [],
      severity: successfulBruteForce > 0 ? 'critical' : 'low'
    })

    // Test 3: Session Management
    const user = await this.authService.signUp('<EMAIL>', 'Password123!', 'Session User')
    const token = await user.user.getIdToken()
    
    // Test token validation
    const tokenValid = await this.securityService.validateToken(token)
    results.push({
      testName: 'Session Token Validation',
      passed: tokenValid,
      vulnerabilities: !tokenValid ? ['Invalid token accepted'] : [],
      recommendations: !tokenValid ? ['Implement proper token validation'] : [],
      severity: !tokenValid ? 'critical' : 'low'
    })

    // Test 4: Token Expiration
    const expiredToken = await this.securityService.generateExpiredToken(user.user.uid)
    try {
      await this.securityService.validateToken(expiredToken)
      results.push({
        testName: 'Token Expiration',
        passed: false,
        vulnerabilities: ['Expired tokens accepted'],
        recommendations: ['Implement proper token expiration'],
        severity: 'high'
      })
    } catch (error) {
      results.push({
        testName: 'Token Expiration',
        passed: true,
        vulnerabilities: [],
        recommendations: [],
        severity: 'low'
      })
    }

    return results
  }

  async auditDataAccess(): Promise<SecurityTestResult[]> {
    const results: SecurityTestResult[] = []

    // Create test tenants
    const tenant1 = await createTestTenant('<EMAIL>')
    const tenant2 = await createTestTenant('<EMAIL>')

    // Create document in tenant1
    const document = await this.dataApiService.create({
      title: 'Private Document',
      content: 'This is private content',
      workspaceId: tenant1.workspace.id,
      createdBy: tenant1.user.uid
    })

    // Test 1: Cross-tenant Data Access
    try {
      await this.dataApiService.read(document.id, {
        workspaceId: tenant2.workspace.id,
        userId: tenant2.user.uid
      })
      results.push({
        testName: 'Cross-tenant Data Access',
        passed: false,
        vulnerabilities: ['Cross-tenant data access allowed'],
        recommendations: ['Implement proper data isolation'],
        severity: 'critical'
      })
    } catch (error) {
      results.push({
        testName: 'Cross-tenant Data Access',
        passed: true,
        vulnerabilities: [],
        recommendations: [],
        severity: 'low'
      })
    }

    // Test 2: Unauthorized Data Modification
    try {
      await this.dataApiService.update(document.id, {
        title: 'Hacked Document',
        content: 'Unauthorized modification'
      }, {
        workspaceId: tenant2.workspace.id,
        userId: tenant2.user.uid
      })
      results.push({
        testName: 'Unauthorized Data Modification',
        passed: false,
        vulnerabilities: ['Unauthorized data modification allowed'],
        recommendations: ['Implement proper authorization checks'],
        severity: 'critical'
      })
    } catch (error) {
      results.push({
        testName: 'Unauthorized Data Modification',
        passed: true,
        vulnerabilities: [],
        recommendations: [],
        severity: 'low'
      })
    }

    // Test 3: Data Deletion Protection
    try {
      await this.dataApiService.delete(document.id, {
        workspaceId: tenant2.workspace.id,
        userId: tenant2.user.uid
      })
      results.push({
        testName: 'Data Deletion Protection',
        passed: false,
        vulnerabilities: ['Unauthorized data deletion allowed'],
        recommendations: ['Implement proper deletion authorization'],
        severity: 'critical'
      })
    } catch (error) {
      results.push({
        testName: 'Data Deletion Protection',
        passed: true,
        vulnerabilities: [],
        recommendations: [],
        severity: 'low'
      })
    }

    // Test 4: Search Result Isolation
    const searchResults = await this.vectorSearchService.search({
      query: 'private',
      workspaceId: tenant2.workspace.id,
      userId: tenant2.user.uid
    })

    const containsPrivateData = searchResults.results.some(result => 
      result.id === document.id || result.content.includes('private content')
    )

    results.push({
      testName: 'Search Result Isolation',
      passed: !containsPrivateData,
      vulnerabilities: containsPrivateData ? ['Search results leak cross-tenant data'] : [],
      recommendations: containsPrivateData ? ['Implement proper search filtering'] : [],
      severity: containsPrivateData ? 'high' : 'low'
    })

    return results
  }

  async auditInputValidation(): Promise<SecurityTestResult[]> {
    const results: SecurityTestResult[] = []

    // Test 1: SQL Injection Prevention
    const sqlInjectionPayloads = [
      "'; DROP TABLE users; --",
      "' OR '1'='1",
      "' UNION SELECT * FROM users --",
      "'; INSERT INTO users VALUES ('hacker', 'password'); --"
    ]

    let sqlInjectionVulnerable = false
    for (const payload of sqlInjectionPayloads) {
      try {
        await this.dataApiService.create({
          title: payload,
          content: 'Test content',
          workspaceId: 'test-workspace',
          createdBy: 'test-user'
        })
        // If no error, check if malicious content was stored
        const stored = await this.dataApiService.read('test-id')
        if (stored && stored.title === payload) {
          sqlInjectionVulnerable = true
          break
        }
      } catch (error) {
        // Good - input validation caught the payload
      }
    }

    results.push({
      testName: 'SQL Injection Prevention',
      passed: !sqlInjectionVulnerable,
      vulnerabilities: sqlInjectionVulnerable ? ['SQL injection possible'] : [],
      recommendations: sqlInjectionVulnerable ? ['Implement parameterized queries'] : [],
      severity: sqlInjectionVulnerable ? 'critical' : 'low'
    })

    // Test 2: XSS Prevention
    const xssPayloads = [
      '<script>alert("XSS")</script>',
      '<img src="x" onerror="alert(1)">',
      'javascript:alert("XSS")',
      '<svg onload="alert(1)">'
    ]

    let xssVulnerable = false
    for (const payload of xssPayloads) {
      try {
        await this.dataApiService.create({
          title: 'XSS Test',
          content: payload,
          workspaceId: 'test-workspace',
          createdBy: 'test-user'
        })
        // Check if malicious script was stored unsanitized
        const stored = await this.dataApiService.read('test-id')
        if (stored && stored.content === payload) {
          xssVulnerable = true
          break
        }
      } catch (error) {
        // Good - input validation caught the payload
      }
    }

    results.push({
      testName: 'XSS Prevention',
      passed: !xssVulnerable,
      vulnerabilities: xssVulnerable ? ['XSS attacks possible'] : [],
      recommendations: xssVulnerable ? ['Implement input sanitization'] : [],
      severity: xssVulnerable ? 'high' : 'low'
    })

    // Test 3: Command Injection Prevention
    const commandInjectionPayloads = [
      '; ls -la',
      '| cat /etc/passwd',
      '&& rm -rf /',
      '`whoami`'
    ]

    let commandInjectionVulnerable = false
    for (const payload of commandInjectionPayloads) {
      try {
        await this.dataApiService.create({
          title: 'Command Test',
          content: payload,
          workspaceId: 'test-workspace',
          createdBy: 'test-user'
        })
        // Check if command was executed (would be hard to detect)
        commandInjectionVulnerable = false // Assume safe for now
      } catch (error) {
        // Good - input validation caught the payload
      }
    }

    results.push({
      testName: 'Command Injection Prevention',
      passed: !commandInjectionVulnerable,
      vulnerabilities: commandInjectionVulnerable ? ['Command injection possible'] : [],
      recommendations: commandInjectionVulnerable ? ['Implement command sanitization'] : [],
      severity: commandInjectionVulnerable ? 'critical' : 'low'
    })

    // Test 4: File Upload Validation
    const maliciousFiles = [
      { name: 'malicious.exe', type: 'application/exe' },
      { name: 'script.php', type: 'application/php' },
      { name: 'payload.js', type: 'application/javascript' },
      { name: 'shell.sh', type: 'application/sh' }
    ]

    let fileUploadVulnerable = false
    for (const file of maliciousFiles) {
      try {
        await this.dataApiService.uploadFile(file, 'test-workspace')
        fileUploadVulnerable = true
        break
      } catch (error) {
        // Good - file type validation caught the malicious file
      }
    }

    results.push({
      testName: 'File Upload Validation',
      passed: !fileUploadVulnerable,
      vulnerabilities: fileUploadVulnerable ? ['Malicious file uploads allowed'] : [],
      recommendations: fileUploadVulnerable ? ['Implement file type validation'] : [],
      severity: fileUploadVulnerable ? 'high' : 'low'
    })

    return results
  }

  async auditRoleBasedAccess(): Promise<SecurityTestResult[]> {
    const results: SecurityTestResult[] = []

    // Create test workspace and users
    const owner = await createTestTenant('<EMAIL>')
    const admin = await this.authService.signUp('<EMAIL>', 'Password123!', 'Admin User')
    const member = await this.authService.signUp('<EMAIL>', 'Password123!', 'Member User')
    const viewer = await this.authService.signUp('<EMAIL>', 'Password123!', 'Viewer User')

    // Add users to workspace with different roles
    await this.authService.addWorkspaceMember(owner.workspace.id, admin.user.uid, 'admin')
    await this.authService.addWorkspaceMember(owner.workspace.id, member.user.uid, 'member')
    await this.authService.addWorkspaceMember(owner.workspace.id, viewer.user.uid, 'viewer')

    // Test 1: Viewer Permission Restrictions
    let viewerCanCreate = false
    try {
      await this.dataApiService.create({
        title: 'Viewer Document',
        content: 'This should not be allowed',
        workspaceId: owner.workspace.id,
        createdBy: viewer.user.uid
      })
      viewerCanCreate = true
    } catch (error) {
      viewerCanCreate = false
    }

    results.push({
      testName: 'Viewer Permission Restrictions',
      passed: !viewerCanCreate,
      vulnerabilities: viewerCanCreate ? ['Viewer can create documents'] : [],
      recommendations: viewerCanCreate ? ['Restrict viewer permissions'] : [],
      severity: viewerCanCreate ? 'medium' : 'low'
    })

    // Test 2: Member Permission Validation
    let memberCanManageUsers = false
    try {
      await this.authService.addWorkspaceMember(
        owner.workspace.id,
        '<EMAIL>',
        'member',
        { actingUserId: member.user.uid }
      )
      memberCanManageUsers = true
    } catch (error) {
      memberCanManageUsers = false
    }

    results.push({
      testName: 'Member Permission Validation',
      passed: !memberCanManageUsers,
      vulnerabilities: memberCanManageUsers ? ['Member can manage users'] : [],
      recommendations: memberCanManageUsers ? ['Restrict member permissions'] : [],
      severity: memberCanManageUsers ? 'medium' : 'low'
    })

    // Test 3: Admin Permission Validation
    let adminCanDeleteWorkspace = false
    try {
      await this.authService.deleteWorkspace(owner.workspace.id, {
        actingUserId: admin.user.uid
      })
      adminCanDeleteWorkspace = true
    } catch (error) {
      adminCanDeleteWorkspace = false
    }

    results.push({
      testName: 'Admin Permission Validation',
      passed: !adminCanDeleteWorkspace,
      vulnerabilities: adminCanDeleteWorkspace ? ['Admin can delete workspace'] : [],
      recommendations: adminCanDeleteWorkspace ? ['Restrict admin permissions'] : [],
      severity: adminCanDeleteWorkspace ? 'high' : 'low'
    })

    // Test 4: Role Escalation Prevention
    let roleEscalationPossible = false
    try {
      await this.authService.updateUserRole(
        owner.workspace.id,
        member.user.uid,
        'owner',
        { actingUserId: member.user.uid }
      )
      roleEscalationPossible = true
    } catch (error) {
      roleEscalationPossible = false
    }

    results.push({
      testName: 'Role Escalation Prevention',
      passed: !roleEscalationPossible,
      vulnerabilities: roleEscalationPossible ? ['Role escalation possible'] : [],
      recommendations: roleEscalationPossible ? ['Prevent role escalation'] : [],
      severity: roleEscalationPossible ? 'critical' : 'low'
    })

    return results
  }

  async auditDataEncryption(): Promise<SecurityTestResult[]> {
    const results: SecurityTestResult[] = []

    // Test 1: Data at Rest Encryption
    const document = await this.dataApiService.create({
      title: 'Encryption Test',
      content: 'This content should be encrypted',
      workspaceId: 'test-workspace',
      createdBy: 'test-user'
    })

    const rawData = await this.securityService.getRawStorageData(document.id)
    const isEncrypted = !rawData.includes('This content should be encrypted')

    results.push({
      testName: 'Data at Rest Encryption',
      passed: isEncrypted,
      vulnerabilities: !isEncrypted ? ['Data stored in plaintext'] : [],
      recommendations: !isEncrypted ? ['Implement database encryption'] : [],
      severity: !isEncrypted ? 'high' : 'low'
    })

    // Test 2: Data in Transit Encryption
    const tlsCheck = await this.securityService.checkTLSConfiguration()
    results.push({
      testName: 'Data in Transit Encryption',
      passed: tlsCheck.isSecure,
      vulnerabilities: !tlsCheck.isSecure ? ['Insecure data transmission'] : [],
      recommendations: !tlsCheck.isSecure ? ['Implement TLS encryption'] : [],
      severity: !tlsCheck.isSecure ? 'critical' : 'low'
    })

    // Test 3: Password Hashing
    const user = await this.authService.signUp('<EMAIL>', 'Password123!', 'Hash Test')
    const passwordHash = await this.securityService.getPasswordHash(user.user.uid)
    const isHashed = passwordHash !== 'Password123!' && passwordHash.length > 20

    results.push({
      testName: 'Password Hashing',
      passed: isHashed,
      vulnerabilities: !isHashed ? ['Passwords stored in plaintext'] : [],
      recommendations: !isHashed ? ['Implement password hashing'] : [],
      severity: !isHashed ? 'critical' : 'low'
    })

    // Test 4: API Key Security
    const apiKeys = await this.securityService.getAPIKeys()
    const exposedKeys = apiKeys.filter(key => key.isExposed)

    results.push({
      testName: 'API Key Security',
      passed: exposedKeys.length === 0,
      vulnerabilities: exposedKeys.length > 0 ? ['API keys exposed'] : [],
      recommendations: exposedKeys.length > 0 ? ['Secure API keys'] : [],
      severity: exposedKeys.length > 0 ? 'high' : 'low'
    })

    return results
  }

  async auditRateLimiting(): Promise<SecurityTestResult[]> {
    const results: SecurityTestResult[] = []

    // Test 1: API Rate Limiting
    const apiRequests = []
    for (let i = 0; i < 100; i++) {
      apiRequests.push(
        this.dataApiService.create({
          title: `Rate Limit Test ${i}`,
          content: 'Testing rate limiting',
          workspaceId: 'test-workspace',
          createdBy: 'test-user'
        }).catch(error => error)
      )
    }

    const responses = await Promise.all(apiRequests)
    const rateLimitedRequests = responses.filter(response => 
      response.code === 'rate-limit-exceeded'
    ).length

    results.push({
      testName: 'API Rate Limiting',
      passed: rateLimitedRequests > 0,
      vulnerabilities: rateLimitedRequests === 0 ? ['No rate limiting implemented'] : [],
      recommendations: rateLimitedRequests === 0 ? ['Implement API rate limiting'] : [],
      severity: rateLimitedRequests === 0 ? 'medium' : 'low'
    })

    // Test 2: Authentication Rate Limiting
    const authAttempts = []
    for (let i = 0; i < 20; i++) {
      authAttempts.push(
        this.authService.signIn('<EMAIL>', 'wrongpassword').catch(error => error)
      )
    }

    const authResponses = await Promise.all(authAttempts)
    const authRateLimited = authResponses.filter(response => 
      response.code === 'auth/too-many-requests'
    ).length

    results.push({
      testName: 'Authentication Rate Limiting',
      passed: authRateLimited > 0,
      vulnerabilities: authRateLimited === 0 ? ['No auth rate limiting'] : [],
      recommendations: authRateLimited === 0 ? ['Implement auth rate limiting'] : [],
      severity: authRateLimited === 0 ? 'high' : 'low'
    })

    return results
  }

  generateSecurityReport(): string {
    let report = '\n=== Security Audit Report ===\n\n'
    
    const severityCounts = {
      critical: 0,
      high: 0,
      medium: 0,
      low: 0
    }

    const failedTests = this.results.filter(result => !result.passed)
    const passedTests = this.results.filter(result => result.passed)

    report += `Total Tests: ${this.results.length}\n`
    report += `Passed Tests: ${passedTests.length}\n`
    report += `Failed Tests: ${failedTests.length}\n`
    report += `Success Rate: ${((passedTests.length / this.results.length) * 100).toFixed(2)}%\n\n`

    if (failedTests.length > 0) {
      report += '=== SECURITY VULNERABILITIES ===\n\n'
      
      failedTests.forEach(test => {
        severityCounts[test.severity]++
        report += `❌ ${test.testName} (${test.severity.toUpperCase()})\n`
        test.vulnerabilities.forEach(vuln => {
          report += `   - ${vuln}\n`
        })
        test.recommendations.forEach(rec => {
          report += `   ✓ ${rec}\n`
        })
        report += '\n'
      })
    }

    report += '=== SEVERITY BREAKDOWN ===\n\n'
    report += `🔴 Critical: ${severityCounts.critical}\n`
    report += `🟠 High: ${severityCounts.high}\n`
    report += `🟡 Medium: ${severityCounts.medium}\n`
    report += `🟢 Low: ${severityCounts.low}\n\n`

    // Security score calculation
    const criticalWeight = 10
    const highWeight = 5
    const mediumWeight = 2
    const lowWeight = 1

    const totalWeight = (severityCounts.critical * criticalWeight) +
                       (severityCounts.high * highWeight) +
                       (severityCounts.medium * mediumWeight) +
                       (severityCounts.low * lowWeight)

    const maxPossibleScore = this.results.length * 10
    const securityScore = Math.max(0, ((maxPossibleScore - totalWeight) / maxPossibleScore) * 100)

    report += `=== SECURITY SCORE: ${securityScore.toFixed(2)}/100 ===\n\n`

    if (securityScore >= 90) {
      report += '✅ EXCELLENT - System is production ready\n'
    } else if (securityScore >= 75) {
      report += '⚠️ GOOD - Minor security improvements needed\n'
    } else if (securityScore >= 50) {
      report += '🚨 FAIR - Significant security issues must be addressed\n'
    } else {
      report += '❌ POOR - System is NOT production ready\n'
    }

    return report
  }

  getResults(): SecurityTestResult[] {
    return this.results
  }
}

describe('Security Validation', () => {
  let auditor: SecurityAuditor

  beforeAll(async () => {
    await initializeFirebaseEmulators()
    auditor = new SecurityAuditor()
  })

  afterAll(async () => {
    console.log(auditor.generateSecurityReport())
    await cleanupFirebaseEmulators()
  })

  describe('Authentication Security', () => {
    it('should pass authentication security audit', async () => {
      const results = await auditor.auditAuthentication()
      results.forEach(result => {
        auditor.addResult(result)
        if (result.severity === 'critical' || result.severity === 'high') {
          expect(result.passed).toBe(true)
        }
      })
    })
  })

  describe('Data Access Security', () => {
    it('should pass data access security audit', async () => {
      const results = await auditor.auditDataAccess()
      results.forEach(result => {
        auditor.addResult(result)
        if (result.severity === 'critical') {
          expect(result.passed).toBe(true)
        }
      })
    })
  })

  describe('Input Validation Security', () => {
    it('should pass input validation security audit', async () => {
      const results = await auditor.auditInputValidation()
      results.forEach(result => {
        auditor.addResult(result)
        if (result.severity === 'critical' || result.severity === 'high') {
          expect(result.passed).toBe(true)
        }
      })
    })
  })

  describe('Role-Based Access Control', () => {
    it('should pass RBAC security audit', async () => {
      const results = await auditor.auditRoleBasedAccess()
      results.forEach(result => {
        auditor.addResult(result)
        if (result.severity === 'critical') {
          expect(result.passed).toBe(true)
        }
      })
    })
  })

  describe('Data Encryption Security', () => {
    it('should pass data encryption security audit', async () => {
      const results = await auditor.auditDataEncryption()
      results.forEach(result => {
        auditor.addResult(result)
        if (result.severity === 'critical') {
          expect(result.passed).toBe(true)
        }
      })
    })
  })

  describe('Rate Limiting Security', () => {
    it('should pass rate limiting security audit', async () => {
      const results = await auditor.auditRateLimiting()
      results.forEach(result => {
        auditor.addResult(result)
        if (result.severity === 'high') {
          expect(result.passed).toBe(true)
        }
      })
    })
  })

  describe('Overall Security Score', () => {
    it('should achieve acceptable security score', async () => {
      const results = auditor.getResults()
      const criticalFailures = results.filter(r => !r.passed && r.severity === 'critical').length
      const highFailures = results.filter(r => !r.passed && r.severity === 'high').length
      
      expect(criticalFailures).toBe(0)
      expect(highFailures).toBeLessThan(2)
      
      const securityScore = results.filter(r => r.passed).length / results.length * 100
      expect(securityScore).toBeGreaterThan(85)
    })
  })
})