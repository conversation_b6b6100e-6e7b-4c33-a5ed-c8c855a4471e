import { describe, it, expect, beforeAll, afterAll } from 'vitest'
import { performance } from 'perf_hooks'
import { 
  initializeFirebaseEmulators, 
  cleanupFirebaseEmulators,
  createTestUser,
  createTestWorkspace 
} from '../../tests/setup/firebase-test-setup'
import { useAuth } from '../../composables/useAuth'
import { useFirestore } from '../../composables/useFirestore'
import { useVectorSearch } from '../../composables/useVectorSearch'
import { useDataApi } from '../../composables/useDataApi'

interface PerformanceMetrics {
  operation: string
  duration: number
  success: boolean
  timestamp: number
}

interface LoadTestResults {
  totalOperations: number
  successfulOperations: number
  failedOperations: number
  averageResponseTime: number
  minResponseTime: number
  maxResponseTime: number
  throughput: number
  errorRate: number
}

class PerformanceValidator {
  private metrics: PerformanceMetrics[] = []
  private authService: any
  private firestoreService: any
  private vectorSearchService: any
  private dataApiService: any

  constructor() {
    this.authService = useAuth()
    this.firestoreService = useFirestore('performance-test')
    this.vectorSearchService = useVectorSearch()
    this.dataApiService = useDataApi()
  }

  async measureOperation<T>(
    operation: string,
    fn: () => Promise<T>
  ): Promise<{ result: T; duration: number }> {
    const start = performance.now()
    let success = true
    let result: T

    try {
      result = await fn()
    } catch (error) {
      success = false
      throw error
    } finally {
      const duration = performance.now() - start
      this.metrics.push({
        operation,
        duration,
        success,
        timestamp: Date.now()
      })
    }

    return { result, duration }
  }

  async runConcurrentOperations<T>(
    operation: string,
    fn: () => Promise<T>,
    concurrency: number
  ): Promise<LoadTestResults> {
    const promises = Array(concurrency).fill(null).map(() => 
      this.measureOperation(operation, fn).catch(error => ({ error, duration: 0 }))
    )

    const results = await Promise.allSettled(promises)
    const successful = results.filter(r => r.status === 'fulfilled' && !('error' in r.value))
    const failed = results.filter(r => r.status === 'rejected' || ('error' in r.value))

    const durations = successful
      .map(r => r.status === 'fulfilled' ? r.value.duration : 0)
      .filter(d => d > 0)

    return {
      totalOperations: concurrency,
      successfulOperations: successful.length,
      failedOperations: failed.length,
      averageResponseTime: durations.reduce((a, b) => a + b, 0) / durations.length,
      minResponseTime: Math.min(...durations),
      maxResponseTime: Math.max(...durations),
      throughput: successful.length / (Math.max(...durations) / 1000),
      errorRate: (failed.length / concurrency) * 100
    }
  }

  getMetrics(): PerformanceMetrics[] {
    return this.metrics
  }

  clearMetrics(): void {
    this.metrics = []
  }

  generateReport(): string {
    const operationStats = this.metrics.reduce((acc, metric) => {
      if (!acc[metric.operation]) {
        acc[metric.operation] = {
          count: 0,
          totalDuration: 0,
          successCount: 0,
          failureCount: 0,
          minDuration: Infinity,
          maxDuration: 0
        }
      }

      const stats = acc[metric.operation]
      stats.count++
      stats.totalDuration += metric.duration
      stats.minDuration = Math.min(stats.minDuration, metric.duration)
      stats.maxDuration = Math.max(stats.maxDuration, metric.duration)

      if (metric.success) {
        stats.successCount++
      } else {
        stats.failureCount++
      }

      return acc
    }, {} as Record<string, any>)

    let report = '\n=== Performance Validation Report ===\n\n'
    
    for (const [operation, stats] of Object.entries(operationStats)) {
      const avgDuration = stats.totalDuration / stats.count
      const successRate = (stats.successCount / stats.count) * 100
      
      report += `${operation}:\n`
      report += `  Total Operations: ${stats.count}\n`
      report += `  Success Rate: ${successRate.toFixed(2)}%\n`
      report += `  Average Duration: ${avgDuration.toFixed(2)}ms\n`
      report += `  Min Duration: ${stats.minDuration.toFixed(2)}ms\n`
      report += `  Max Duration: ${stats.maxDuration.toFixed(2)}ms\n`
      report += `  Throughput: ${(stats.count / (stats.totalDuration / 1000)).toFixed(2)} ops/sec\n\n`
    }

    return report
  }
}

describe('Performance Validation', () => {
  let validator: PerformanceValidator
  let testUser: any
  let testWorkspace: any

  beforeAll(async () => {
    await initializeFirebaseEmulators()
    validator = new PerformanceValidator()
    testUser = await createTestUser('<EMAIL>')
    testWorkspace = await createTestWorkspace('performance-test-workspace')
  })

  afterAll(async () => {
    console.log(validator.generateReport())
    await cleanupFirebaseEmulators()
  })

  describe('Authentication Performance', () => {
    it('should handle authentication requests within SLA', async () => {
      const { duration } = await validator.measureOperation(
        'user-signup',
        () => validator.authService.signUp(
          '<EMAIL>',
          'Password123!',
          'Performance Test User'
        )
      )

      expect(duration).toBeLessThan(200) // 200ms SLA
    })

    it('should handle concurrent authentication requests', async () => {
      const results = await validator.runConcurrentOperations(
        'concurrent-signup',
        () => validator.authService.signUp(
          `perf-test-${Date.now()}-${Math.random()}@example.com`,
          'Password123!',
          'Concurrent Test User'
        ),
        20
      )

      expect(results.successfulOperations).toBeGreaterThan(15) // 75% success rate
      expect(results.averageResponseTime).toBeLessThan(500) // 500ms average
      expect(results.errorRate).toBeLessThan(25) // < 25% error rate
    })

    it('should handle login requests under load', async () => {
      // First create test users
      const testUsers = await Promise.all(
        Array(10).fill(null).map((_, i) => 
          validator.authService.signUp(
            `load-test-${i}@example.com`,
            'Password123!',
            `Load Test User ${i}`
          )
        )
      )

      // Test concurrent logins
      const results = await validator.runConcurrentOperations(
        'concurrent-login',
        () => {
          const randomUser = testUsers[Math.floor(Math.random() * testUsers.length)]
          return validator.authService.signIn(
            randomUser.user.email,
            'Password123!'
          )
        },
        50
      )

      expect(results.successfulOperations).toBeGreaterThan(40) // 80% success rate
      expect(results.averageResponseTime).toBeLessThan(300) // 300ms average
      expect(results.errorRate).toBeLessThan(20) // < 20% error rate
    })
  })

  describe('Data Operations Performance', () => {
    it('should handle document creation within SLA', async () => {
      const { duration } = await validator.measureOperation(
        'document-create',
        () => validator.dataApiService.create({
          title: 'Performance Test Document',
          content: 'This is a performance test document with some content',
          workspaceId: testWorkspace.id,
          createdBy: testUser.uid
        })
      )

      expect(duration).toBeLessThan(300) // 300ms SLA
    })

    it('should handle bulk document operations', async () => {
      const results = await validator.runConcurrentOperations(
        'bulk-document-create',
        () => validator.dataApiService.create({
          title: `Bulk Document ${Date.now()}`,
          content: 'This is a bulk document creation test with various content to simulate real usage patterns',
          workspaceId: testWorkspace.id,
          createdBy: testUser.uid
        }),
        100
      )

      expect(results.successfulOperations).toBeGreaterThan(80) // 80% success rate
      expect(results.averageResponseTime).toBeLessThan(1000) // 1s average
      expect(results.throughput).toBeGreaterThan(50) // 50 ops/sec
    })

    it('should handle document queries efficiently', async () => {
      // Create test documents first
      await Promise.all(
        Array(50).fill(null).map((_, i) => 
          validator.dataApiService.create({
            title: `Query Test Document ${i}`,
            content: `Content for query test document ${i}`,
            workspaceId: testWorkspace.id,
            createdBy: testUser.uid
          })
        )
      )

      const results = await validator.runConcurrentOperations(
        'document-query',
        () => validator.firestoreService.list({
          workspaceId: testWorkspace.id,
          limit: 10
        }),
        30
      )

      expect(results.successfulOperations).toBeGreaterThan(25) // 85% success rate
      expect(results.averageResponseTime).toBeLessThan(200) // 200ms average
    })

    it('should handle document updates efficiently', async () => {
      // Create a test document
      const testDoc = await validator.dataApiService.create({
        title: 'Update Test Document',
        content: 'Original content',
        workspaceId: testWorkspace.id,
        createdBy: testUser.uid
      })

      const results = await validator.runConcurrentOperations(
        'document-update',
        () => validator.dataApiService.update(testDoc.id, {
          title: `Updated Document ${Date.now()}`,
          content: 'Updated content'
        }),
        20
      )

      expect(results.successfulOperations).toBeGreaterThan(15) // 75% success rate
      expect(results.averageResponseTime).toBeLessThan(400) // 400ms average
    })
  })

  describe('Vector Search Performance', () => {
    beforeAll(async () => {
      // Create documents for search testing
      const searchDocs = [
        'Machine learning algorithms for data processing',
        'Natural language processing techniques',
        'Computer vision and image recognition',
        'Deep learning neural networks',
        'Artificial intelligence applications',
        'Data science and analytics',
        'Software engineering best practices',
        'Web development frameworks',
        'Database optimization strategies',
        'Cloud computing architectures'
      ]

      await Promise.all(
        searchDocs.map((content, i) => 
          validator.dataApiService.create({
            title: `Search Test Document ${i}`,
            content,
            workspaceId: testWorkspace.id,
            createdBy: testUser.uid
          })
        )
      )
    })

    it('should handle search requests within SLA', async () => {
      const { duration } = await validator.measureOperation(
        'vector-search',
        () => validator.vectorSearchService.search({
          query: 'machine learning',
          workspaceId: testWorkspace.id,
          limit: 10
        })
      )

      expect(duration).toBeLessThan(500) // 500ms SLA
    })

    it('should handle concurrent search requests', async () => {
      const searchQueries = [
        'machine learning',
        'data processing',
        'neural networks',
        'computer vision',
        'natural language',
        'software engineering',
        'web development',
        'database optimization',
        'cloud computing',
        'artificial intelligence'
      ]

      const results = await validator.runConcurrentOperations(
        'concurrent-search',
        () => {
          const randomQuery = searchQueries[Math.floor(Math.random() * searchQueries.length)]
          return validator.vectorSearchService.search({
            query: randomQuery,
            workspaceId: testWorkspace.id,
            limit: 5
          })
        },
        25
      )

      expect(results.successfulOperations).toBeGreaterThan(20) // 80% success rate
      expect(results.averageResponseTime).toBeLessThan(800) // 800ms average
      expect(results.errorRate).toBeLessThan(20) // < 20% error rate
    })

    it('should handle complex search queries efficiently', async () => {
      const complexQueries = [
        'machine learning algorithms for natural language processing',
        'deep learning neural networks in computer vision applications',
        'data science techniques for software engineering optimization',
        'cloud computing architectures for scalable web development',
        'artificial intelligence applications in database management'
      ]

      const results = await validator.runConcurrentOperations(
        'complex-search',
        () => {
          const randomQuery = complexQueries[Math.floor(Math.random() * complexQueries.length)]
          return validator.vectorSearchService.search({
            query: randomQuery,
            workspaceId: testWorkspace.id,
            limit: 10
          })
        },
        15
      )

      expect(results.successfulOperations).toBeGreaterThan(12) // 80% success rate
      expect(results.averageResponseTime).toBeLessThan(1200) // 1.2s average
    })
  })

  describe('Workspace Operations Performance', () => {
    it('should handle workspace creation within SLA', async () => {
      const { duration } = await validator.measureOperation(
        'workspace-create',
        () => validator.authService.createWorkspace({
          name: 'Performance Test Workspace',
          description: 'Workspace for performance testing',
          settings: {
            allowPublicSignup: false,
            defaultRole: 'member'
          }
        })
      )

      expect(duration).toBeLessThan(400) // 400ms SLA
    })

    it('should handle workspace switching efficiently', async () => {
      // Create multiple workspaces
      const workspaces = await Promise.all(
        Array(5).fill(null).map((_, i) => 
          validator.authService.createWorkspace({
            name: `Switch Test Workspace ${i}`,
            description: `Workspace ${i} for switch testing`
          })
        )
      )

      const results = await validator.runConcurrentOperations(
        'workspace-switch',
        () => {
          const randomWorkspace = workspaces[Math.floor(Math.random() * workspaces.length)]
          return validator.authService.switchWorkspace(randomWorkspace.id)
        },
        20
      )

      expect(results.successfulOperations).toBeGreaterThan(15) // 75% success rate
      expect(results.averageResponseTime).toBeLessThan(250) // 250ms average
    })
  })

  describe('System Integration Performance', () => {
    it('should handle complete user workflows within acceptable time', async () => {
      const { duration } = await validator.measureOperation(
        'complete-workflow',
        async () => {
          // Simulate complete user workflow
          const user = await validator.authService.signUp(
            `workflow-${Date.now()}@example.com`,
            'Password123!',
            'Workflow Test User'
          )

          const workspace = await validator.authService.createWorkspace({
            name: 'Workflow Test Workspace',
            description: 'Complete workflow test'
          })

          const document = await validator.dataApiService.create({
            title: 'Workflow Test Document',
            content: 'This is a complete workflow test document',
            workspaceId: workspace.id,
            createdBy: user.user.uid
          })

          const searchResult = await validator.vectorSearchService.search({
            query: 'workflow test',
            workspaceId: workspace.id,
            limit: 5
          })

          return { user, workspace, document, searchResult }
        }
      )

      expect(duration).toBeLessThan(2000) // 2s for complete workflow
    })

    it('should maintain performance under sustained load', async () => {
      const loadTestDuration = 30000 // 30 seconds
      const startTime = Date.now()
      const operations = []

      // Generate continuous load for 30 seconds
      while (Date.now() - startTime < loadTestDuration) {
        operations.push(
          validator.measureOperation(
            'sustained-load',
            async () => {
              const operation = Math.random()
              
              if (operation < 0.3) {
                // 30% authentication operations
                return validator.authService.signIn(testUser.email, 'Password123!')
              } else if (operation < 0.6) {
                // 30% data operations
                return validator.dataApiService.create({
                  title: `Load Test ${Date.now()}`,
                  content: 'Sustained load test content',
                  workspaceId: testWorkspace.id,
                  createdBy: testUser.uid
                })
              } else {
                // 40% search operations
                return validator.vectorSearchService.search({
                  query: 'load test',
                  workspaceId: testWorkspace.id,
                  limit: 5
                })
              }
            }
          )
        )

        // Add small delay to simulate realistic usage
        await new Promise(resolve => setTimeout(resolve, 100))
      }

      const results = await Promise.allSettled(operations)
      const successful = results.filter(r => r.status === 'fulfilled').length
      const failed = results.filter(r => r.status === 'rejected').length

      expect(successful).toBeGreaterThan(failed) // More successes than failures
      expect(successful / (successful + failed)).toBeGreaterThan(0.8) // > 80% success rate

      console.log(`Sustained load test: ${successful} successful, ${failed} failed operations`)
    })
  })

  describe('Memory and Resource Usage', () => {
    it('should not leak memory during operations', async () => {
      const initialMemory = process.memoryUsage()
      
      // Perform many operations
      await Promise.all(
        Array(100).fill(null).map((_, i) => 
          validator.dataApiService.create({
            title: `Memory Test ${i}`,
            content: `Memory test document ${i}`,
            workspaceId: testWorkspace.id,
            createdBy: testUser.uid
          })
        )
      )

      // Force garbage collection if available
      if (global.gc) {
        global.gc()
      }

      const finalMemory = process.memoryUsage()
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed
      
      // Memory increase should be reasonable (< 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024)
    })

    it('should handle large data operations efficiently', async () => {
      const largeContent = 'A'.repeat(10000) // 10KB content
      
      const results = await validator.runConcurrentOperations(
        'large-document-create',
        () => validator.dataApiService.create({
          title: 'Large Document Test',
          content: largeContent,
          workspaceId: testWorkspace.id,
          createdBy: testUser.uid
        }),
        10
      )

      expect(results.successfulOperations).toBeGreaterThan(7) // 70% success rate
      expect(results.averageResponseTime).toBeLessThan(2000) // 2s average
    })
  })
})