import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    name: 'Phase 6.4 Validation Suite',
    environment: 'happy-dom',
    setupFiles: ['../tests/setup/vitest.setup.ts'],
    globalSetup: ['../tests/setup/firebase-test-setup.ts'],
    testTimeout: 30000,
    hookTimeout: 10000,
    teardownTimeout: 10000,
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true
      }
    },
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './coverage',
      exclude: [
        'node_modules/**',
        'coverage/**',
        'dist/**',
        '**/*.config.*',
        '**/*.test.*',
        '**/*.spec.*'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    reporters: ['default', 'json', 'html'],
    outputFile: {
      json: './validation/report/test-results.json',
      html: './validation/report/test-results.html'
    }
  }
})