import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'vitest'
import { 
  initializeFirebaseEmulators, 
  cleanupFirebaseEmulators,
  createTestUser,
  createTestWorkspace,
  createTestTenant 
} from '../../tests/setup/firebase-test-setup'
import { useAuth } from '../../composables/useAuth'
import { useFirestore } from '../../composables/useFirestore'
import { useWorkspace } from '../../composables/useWorkspace'
import { useVectorSearch } from '../../composables/useVectorSearch'
import { useDataApi } from '../../composables/useDataApi'

describe('System Integration Validation', () => {
  let testUser: any
  let testWorkspace: any
  let authService: any
  let firestoreService: any
  let workspaceService: any
  let vectorSearchService: any
  let dataApiService: any

  beforeAll(async () => {
    await initializeFirebaseEmulators()
    
    // Initialize all services
    authService = useAuth()
    firestoreService = useFirestore('test-collection')
    workspaceService = useWorkspace()
    vectorSearchService = useVectorSearch()
    dataApiService = useDataApi()
  })

  afterAll(async () => {
    await cleanupFirebaseEmulators()
  })

  beforeEach(async () => {
    // Create fresh test data for each test
    testUser = await createTestUser('<EMAIL>')
    testWorkspace = await createTestWorkspace('integration-workspace')
  })

  describe('Complete System Integration Flow', () => {
    it('should handle complete user journey from signup to data operations', async () => {
      // Phase 1: User Authentication
      console.log('🔐 Testing authentication flow...')
      
      const signupResult = await authService.signUp(
        '<EMAIL>',
        'SecurePassword123!',
        'Test User'
      )
      
      expect(signupResult.user).toBeDefined()
      expect(signupResult.user.email).toBe('<EMAIL>')
      expect(authService.isAuthenticated.value).toBe(true)
      
      // Phase 2: Workspace Creation
      console.log('🏢 Testing workspace creation...')
      
      const workspace = await workspaceService.createWorkspace({
        name: 'Test Workspace',
        description: 'Integration test workspace',
        settings: {
          allowPublicSignup: false,
          defaultRole: 'member'
        }
      })
      
      expect(workspace.id).toBeDefined()
      expect(workspace.name).toBe('Test Workspace')
      expect(workspace.ownerId).toBe(signupResult.user.uid)
      
      // Phase 3: Data Operations
      console.log('💾 Testing data operations...')
      
      const document = await dataApiService.create({
        title: 'Test Document',
        content: 'This is a test document for integration testing',
        workspaceId: workspace.id,
        createdBy: signupResult.user.uid
      })
      
      expect(document.id).toBeDefined()
      expect(document.title).toBe('Test Document')
      
      // Phase 4: Vector Search Integration
      console.log('🔍 Testing vector search...')
      
      // First, generate embeddings for the document
      const searchResult = await vectorSearchService.search({
        query: 'test document',
        workspaceId: workspace.id,
        limit: 10
      })
      
      expect(searchResult.results).toBeDefined()
      expect(Array.isArray(searchResult.results)).toBe(true)
      
      // Phase 5: Multi-tenant Data Isolation
      console.log('🏘️ Testing multi-tenant isolation...')
      
      // Create second workspace with different user
      const secondUser = await authService.signUp(
        '<EMAIL>',
        'SecurePassword123!',
        'Second User'
      )
      
      const secondWorkspace = await workspaceService.createWorkspace({
        name: 'Second Workspace',
        description: 'Second integration test workspace',
        settings: {
          allowPublicSignup: false,
          defaultRole: 'member'
        }
      })
      
      // Try to access first workspace's data from second workspace
      const isolatedSearch = await vectorSearchService.search({
        query: 'test document',
        workspaceId: secondWorkspace.id,
        limit: 10
      })
      
      // Should not find documents from first workspace
      expect(isolatedSearch.results.length).toBe(0)
      
      console.log('✅ Complete system integration test passed')
    })

    it('should handle concurrent operations without conflicts', async () => {
      console.log('⚡ Testing concurrent operations...')
      
      // Create multiple users concurrently
      const concurrentUsers = await Promise.all([
        authService.signUp('<EMAIL>', 'Password123!', 'User 1'),
        authService.signUp('<EMAIL>', 'Password123!', 'User 2'),
        authService.signUp('<EMAIL>', 'Password123!', 'User 3')
      ])
      
      expect(concurrentUsers).toHaveLength(3)
      concurrentUsers.forEach(user => {
        expect(user.user).toBeDefined()
        expect(user.user.email).toMatch(/user\d@example\.com/)
      })
      
      // Create workspaces concurrently
      const concurrentWorkspaces = await Promise.all([
        workspaceService.createWorkspace({
          name: 'Concurrent Workspace 1',
          description: 'First concurrent workspace'
        }),
        workspaceService.createWorkspace({
          name: 'Concurrent Workspace 2',
          description: 'Second concurrent workspace'
        }),
        workspaceService.createWorkspace({
          name: 'Concurrent Workspace 3',
          description: 'Third concurrent workspace'
        })
      ])
      
      expect(concurrentWorkspaces).toHaveLength(3)
      concurrentWorkspaces.forEach(workspace => {
        expect(workspace.id).toBeDefined()
        expect(workspace.name).toMatch(/Concurrent Workspace \d/)
      })
      
      // Create documents concurrently
      const concurrentDocuments = await Promise.all([
        dataApiService.create({
          title: 'Concurrent Document 1',
          content: 'First concurrent document',
          workspaceId: concurrentWorkspaces[0].id
        }),
        dataApiService.create({
          title: 'Concurrent Document 2',
          content: 'Second concurrent document',
          workspaceId: concurrentWorkspaces[1].id
        }),
        dataApiService.create({
          title: 'Concurrent Document 3',
          content: 'Third concurrent document',
          workspaceId: concurrentWorkspaces[2].id
        })
      ])
      
      expect(concurrentDocuments).toHaveLength(3)
      concurrentDocuments.forEach(doc => {
        expect(doc.id).toBeDefined()
        expect(doc.title).toMatch(/Concurrent Document \d/)
      })
      
      console.log('✅ Concurrent operations test passed')
    })

    it('should handle system failures gracefully', async () => {
      console.log('🛡️ Testing system resilience...')
      
      // Test authentication failure handling
      try {
        await authService.signIn('<EMAIL>', 'wrongpassword')
        expect.fail('Should have thrown authentication error')
      } catch (error) {
        expect(error.code).toBe('auth/user-not-found')
      }
      
      // Test workspace access failure
      try {
        await workspaceService.getWorkspace('nonexistent-workspace-id')
        expect.fail('Should have thrown workspace not found error')
      } catch (error) {
        expect(error.code).toBe('workspace/not-found')
      }
      
      // Test data API error handling
      try {
        await dataApiService.create({
          title: '',  // Invalid empty title
          content: 'Test content',
          workspaceId: 'invalid-workspace-id'
        })
        expect.fail('Should have thrown validation error')
      } catch (error) {
        expect(error.code).toBe('validation/invalid-input')
      }
      
      // Test vector search with invalid parameters
      try {
        await vectorSearchService.search({
          query: '',  // Empty query
          workspaceId: 'invalid-workspace-id',
          limit: -1   // Invalid limit
        })
        expect.fail('Should have thrown search error')
      } catch (error) {
        expect(error.code).toBe('search/invalid-parameters')
      }
      
      console.log('✅ System resilience test passed')
    })

    it('should maintain performance under load', async () => {
      console.log('🚀 Testing performance under load...')
      
      const startTime = Date.now()
      
      // Create load test data
      const loadTestOperations = []
      
      for (let i = 0; i < 50; i++) {
        loadTestOperations.push(
          dataApiService.create({
            title: `Load Test Document ${i}`,
            content: `This is load test document number ${i} with some content to test performance`,
            workspaceId: testWorkspace.id,
            createdBy: testUser.uid
          })
        )
      }
      
      const results = await Promise.all(loadTestOperations)
      
      const endTime = Date.now()
      const executionTime = endTime - startTime
      
      // Verify all operations succeeded
      expect(results).toHaveLength(50)
      results.forEach((result, index) => {
        expect(result.id).toBeDefined()
        expect(result.title).toBe(`Load Test Document ${index}`)
      })
      
      // Performance assertion: should complete within 10 seconds
      expect(executionTime).toBeLessThan(10000)
      
      console.log(`✅ Performance test passed: ${executionTime}ms for 50 operations`)
    })
  })

  describe('Multi-tenant Security Validation', () => {
    it('should enforce workspace data isolation', async () => {
      console.log('🔒 Testing workspace data isolation...')
      
      // Create two separate tenants
      const tenant1 = await createTestTenant('<EMAIL>')
      const tenant2 = await createTestTenant('<EMAIL>')
      
      // Create documents in tenant1's workspace
      const tenant1Document = await dataApiService.create({
        title: 'Tenant 1 Document',
        content: 'This document belongs to tenant 1',
        workspaceId: tenant1.workspace.id,
        createdBy: tenant1.user.uid
      })
      
      // Try to access tenant1's document from tenant2's context
      try {
        await dataApiService.read(tenant1Document.id, {
          workspaceId: tenant2.workspace.id
        })
        expect.fail('Should not be able to access other tenant\'s data')
      } catch (error) {
        expect(error.code).toBe('auth/insufficient-permissions')
      }
      
      // Verify tenant2 can only see their own data
      const tenant2Search = await vectorSearchService.search({
        query: 'tenant document',
        workspaceId: tenant2.workspace.id,
        limit: 10
      })
      
      expect(tenant2Search.results).toHaveLength(0)
      
      console.log('✅ Workspace data isolation test passed')
    })

    it('should enforce role-based access control', async () => {
      console.log('👥 Testing role-based access control...')
      
      // Create workspace owner
      const owner = await createTestTenant('<EMAIL>')
      
      // Create workspace members with different roles
      const admin = await authService.signUp('<EMAIL>', 'Password123!', 'Admin User')
      const member = await authService.signUp('<EMAIL>', 'Password123!', 'Member User')
      const viewer = await authService.signUp('<EMAIL>', 'Password123!', 'Viewer User')
      
      // Add users to workspace with different roles
      await workspaceService.addMember(owner.workspace.id, admin.user.uid, 'admin')
      await workspaceService.addMember(owner.workspace.id, member.user.uid, 'member')
      await workspaceService.addMember(owner.workspace.id, viewer.user.uid, 'viewer')
      
      // Test admin permissions
      const adminCanCreate = await dataApiService.create({
        title: 'Admin Created Document',
        content: 'Document created by admin',
        workspaceId: owner.workspace.id,
        createdBy: admin.user.uid
      })
      
      expect(adminCanCreate.id).toBeDefined()
      
      // Test member permissions  
      const memberCanCreate = await dataApiService.create({
        title: 'Member Created Document',
        content: 'Document created by member',
        workspaceId: owner.workspace.id,
        createdBy: member.user.uid
      })
      
      expect(memberCanCreate.id).toBeDefined()
      
      // Test viewer permissions (should fail for create operations)
      try {
        await dataApiService.create({
          title: 'Viewer Attempted Document',
          content: 'Document attempted by viewer',
          workspaceId: owner.workspace.id,
          createdBy: viewer.user.uid
        })
        expect.fail('Viewer should not be able to create documents')
      } catch (error) {
        expect(error.code).toBe('auth/insufficient-permissions')
      }
      
      console.log('✅ Role-based access control test passed')
    })
  })

  describe('Data Consistency Validation', () => {
    it('should maintain data consistency across operations', async () => {
      console.log('🔄 Testing data consistency...')
      
      // Create a document
      const originalDoc = await dataApiService.create({
        title: 'Consistency Test Document',
        content: 'Original content',
        workspaceId: testWorkspace.id,
        createdBy: testUser.uid
      })
      
      // Update the document
      const updatedDoc = await dataApiService.update(originalDoc.id, {
        title: 'Updated Consistency Test Document',
        content: 'Updated content'
      })
      
      // Verify the update is reflected
      expect(updatedDoc.title).toBe('Updated Consistency Test Document')
      expect(updatedDoc.content).toBe('Updated content')
      
      // Read the document to verify consistency
      const readDoc = await dataApiService.read(originalDoc.id)
      expect(readDoc.title).toBe('Updated Consistency Test Document')
      expect(readDoc.content).toBe('Updated content')
      
      // Verify vector search reflects the update
      const searchResult = await vectorSearchService.search({
        query: 'updated consistency',
        workspaceId: testWorkspace.id,
        limit: 10
      })
      
      expect(searchResult.results.length).toBeGreaterThan(0)
      expect(searchResult.results[0].title).toBe('Updated Consistency Test Document')
      
      console.log('✅ Data consistency test passed')
    })

    it('should handle concurrent updates without corruption', async () => {
      console.log('🔄 Testing concurrent update handling...')
      
      // Create a document
      const baseDoc = await dataApiService.create({
        title: 'Concurrent Update Test',
        content: 'Base content',
        workspaceId: testWorkspace.id,
        createdBy: testUser.uid
      })
      
      // Perform concurrent updates
      const concurrentUpdates = await Promise.allSettled([
        dataApiService.update(baseDoc.id, {
          title: 'Update 1',
          content: 'Content from update 1'
        }),
        dataApiService.update(baseDoc.id, {
          title: 'Update 2', 
          content: 'Content from update 2'
        }),
        dataApiService.update(baseDoc.id, {
          title: 'Update 3',
          content: 'Content from update 3'
        })
      ])
      
      // At least one update should succeed
      const successfulUpdates = concurrentUpdates.filter(result => result.status === 'fulfilled')
      expect(successfulUpdates.length).toBeGreaterThan(0)
      
      // Verify final state is consistent
      const finalDoc = await dataApiService.read(baseDoc.id)
      expect(finalDoc.title).toMatch(/Update \d/)
      expect(finalDoc.content).toMatch(/Content from update \d/)
      
      console.log('✅ Concurrent update handling test passed')
    })
  })
})