/**
 * @fileoverview Enterprise Error Handling System for Multi-tenant Applications
 * 
 * This module provides comprehensive error handling capabilities for the PIB-METHOD
 * multi-tenant authentication system. Designed to handle Firebase, network, and
 * application errors with proper classification, user-friendly messaging, and
 * detailed logging.
 * 
 * Key Features:
 * - Standardized error format across the application
 * - Firebase Auth and Firestore error translation
 * - HTTP status code mapping for API responses
 * - User-friendly error messages with security in mind
 * - Comprehensive error logging and monitoring
 * - Type-safe error handling with TypeScript
 * 
 * Security Considerations:
 * - Sensitive information is filtered from user-facing messages
 * - Error details are logged securely without exposing credentials
 * - Error codes follow standard conventions to prevent information leakage
 * - Stack traces are only included in development environments
 * 
 * Integration:
 * - Works seamlessly with Firebase Auth and Firestore
 * - Supports toast notifications and user feedback
 * - Integrates with monitoring and alerting systems
 * - Provides consistent error experience across all modules
 * 
 * <AUTHOR> Development Team
 * @version 1.0.0
 * @since 2024
 */

/**
 * Standardized application error interface
 * 
 * Provides consistent error structure across the entire application
 * with all necessary metadata for proper handling and debugging.
 * 
 * @interface AppError
 * @example
 * ```typescript
 * const error: AppError = {
 *   code: 'auth/user-not-found',
 *   message: 'No account found with this email address',
 *   details: originalFirebaseError,
 *   timestamp: new Date(),
 *   statusCode: 401
 * }
 * ```
 */
export interface AppError {
  /** Unique error code for programmatic handling */
  code: string
  
  /** User-friendly error message */
  message: string
  
  /** Original error object or additional details */
  details?: any
  
  /** When the error occurred */
  timestamp: Date
  
  /** HTTP status code for API responses */
  statusCode?: number
}

/**
 * Centralized error handling service
 * 
 * Provides comprehensive error processing including Firebase error translation,
 * user-friendly messaging, and proper HTTP status code mapping. Designed for
 * multi-tenant applications with security and usability in mind.
 * 
 * @class ErrorHandler
 * @example
 * ```typescript
 * try {
 *   await signInUser(email, password)
 * } catch (error) {
 *   const appError = ErrorHandler.handle(error)
 *   ErrorHandler.toast(appError) // Show user notification
 *   ErrorHandler.log(appError)   // Log for debugging
 * }
 * ```
 */
export class ErrorHandler {
  /**
   * Process and standardize any error into AppError format
   * 
   * Main entry point for error handling. Analyzes the error type,
   * applies appropriate transformations, and returns a standardized
   * AppError with user-friendly messaging and proper status codes.
   * 
   * @static
   * @param {any} error - Original error object (Firebase, network, or generic)
   * @param {string} [customMessage] - Optional custom message to override default
   * @returns {AppError} Standardized application error
   * 
   * @example
   * ```typescript
   * // Handle Firebase Auth error
   * try {
   *   await signInWithEmailAndPassword(auth, email, password)
   * } catch (firebaseError) {
   *   const appError = ErrorHandler.handle(firebaseError)
   *   console.log(appError.message) // "Incorrect password"
   *   console.log(appError.statusCode) // 401
   * }
   * 
   * // Handle with custom message
   * const customError = ErrorHandler.handle(error, 'Login failed. Please try again.')
   * ```
   */
  static handle(error: any, customMessage?: string): AppError {
    const timestamp = new Date()
    
    // Firebase Auth errors
    if (error.code?.startsWith('auth/')) {
      return {
        code: error.code,
        message: customMessage || this.getAuthErrorMessage(error.code),
        details: error,
        timestamp,
        statusCode: this.getStatusCodeForError(error.code)
      }
    }
    
    // Firebase Firestore errors
    if (error.code?.startsWith('firestore/')) {
      return {
        code: error.code,
        message: customMessage || this.getFirestoreErrorMessage(error.code),
        details: error,
        timestamp,
        statusCode: this.getStatusCodeForError(error.code)
      }
    }
    
    // Network errors
    if (error.code === 'NETWORK_ERROR') {
      return {
        code: 'network-error',
        message: customMessage || 'Network connection failed. Please check your internet connection.',
        details: error,
        timestamp,
        statusCode: 503
      }
    }
    
    // Generic error
    return {
      code: 'unknown-error',
      message: customMessage || error.message || 'An unexpected error occurred',
      details: error,
      timestamp,
      statusCode: 500
    }
  }
  
  /**
   * Translate Firebase Auth error codes to user-friendly messages
   * 
   * Converts Firebase Auth error codes into clear, actionable messages
   * that users can understand. Balances helpfulness with security by
   * not exposing sensitive system information.
   * 
   * @private
   * @static
   * @param {string} code - Firebase Auth error code
   * @returns {string} User-friendly error message
   * 
   * @example
   * ```typescript
   * // Internal usage only
   * const message = ErrorHandler.getAuthErrorMessage('auth/user-not-found')
   * console.log(message) // "No account found with this email address"
   * ```
   */
  private static getAuthErrorMessage(code: string): string {
    const messages: Record<string, string> = {
      'auth/user-not-found': 'No account found with this email address',
      'auth/wrong-password': 'Incorrect password',
      'auth/email-already-in-use': 'An account with this email already exists',
      'auth/weak-password': 'Password should be at least 6 characters',
      'auth/invalid-email': 'Invalid email address',
      'auth/user-disabled': 'This account has been disabled',
      'auth/too-many-requests': 'Too many failed attempts. Please try again later',
      'auth/network-request-failed': 'Network error. Please check your connection',
      'auth/popup-closed-by-user': 'Sign-in popup was closed',
      'auth/cancelled-popup-request': 'Sign-in cancelled',
      'auth/popup-blocked': 'Sign-in popup was blocked by the browser',
      'auth/missing-email': 'Email address is required for password reset',
      'auth/invalid-continue-uri': 'Invalid password reset link',
      'auth/unauthorized-continue-uri': 'Unauthorized password reset link'
    }
    
    return messages[code] || 'Authentication error occurred'
  }
  
  /**
   * Translate Firestore error codes to user-friendly messages
   * 
   * Converts Firestore database error codes into clear messages
   * that help users understand what went wrong without exposing
   * internal system details or security-sensitive information.
   * 
   * @private
   * @static
   * @param {string} code - Firestore error code
   * @returns {string} User-friendly error message
   * 
   * @example
   * ```typescript
   * // Internal usage only
   * const message = ErrorHandler.getFirestoreErrorMessage('firestore/permission-denied')
   * console.log(message) // "You do not have permission to access this data"
   * ```
   */
  private static getFirestoreErrorMessage(code: string): string {
    const messages: Record<string, string> = {
      'firestore/permission-denied': 'You do not have permission to access this data',
      'firestore/not-found': 'The requested document was not found',
      'firestore/already-exists': 'The document already exists',
      'firestore/failed-precondition': 'Operation failed due to invalid conditions',
      'firestore/aborted': 'Operation was aborted due to a conflict',
      'firestore/out-of-range': 'Operation was attempted past the valid range',
      'firestore/unimplemented': 'Operation is not implemented or supported',
      'firestore/internal': 'Internal server error',
      'firestore/unavailable': 'Service is currently unavailable',
      'firestore/data-loss': 'Unrecoverable data loss or corruption',
      'firestore/unauthenticated': 'Authentication required for this operation'
    }
    
    return messages[code] || 'Database error occurred'
  }
  
  /**
   * Display user-friendly error notification
   * 
   * Shows error message to users through the application's notification
   * system. In production, this would integrate with toast libraries
   * like vue-toastification or similar notification components.
   * 
   * @static
   * @param {AppError} error - Application error to display
   * 
   * @example
   * ```typescript
   * try {
   *   await performUserAction()
   * } catch (err) {
   *   const appError = ErrorHandler.handle(err)
   *   ErrorHandler.toast(appError) // Shows user-friendly notification
   * }
   * ```
   */
  static toast(error: AppError) {
    // This would integrate with your toast notification system
    // In production: useToast().error(error.message)
    console.error(`[${error.code}] ${error.message}`, error.details)
  }
  
  /**
   * Log error details for debugging and monitoring
   * 
   * Records comprehensive error information for development debugging
   * and production monitoring. In production environments, this would
   * integrate with services like Sentry, LogRocket, or custom logging.
   * 
   * @static
   * @param {AppError} error - Application error to log
   * 
   * @example
   * ```typescript
   * try {
   *   await criticalOperation()
   * } catch (err) {
   *   const appError = ErrorHandler.handle(err)
   *   ErrorHandler.log(appError) // Logs for debugging/monitoring
   *   
   *   // Still show user-friendly message
   *   ErrorHandler.toast(appError)
   * }
   * ```
   */
  static log(error: AppError) {
    // This would integrate with your logging service
    // In production: logger.error(error) or Sentry.captureException(error)
    console.error('Application Error:', {
      code: error.code,
      message: error.message,
      timestamp: error.timestamp,
      details: error.details
    })
  }

  /**
   * Map error codes to appropriate HTTP status codes
   * 
   * Provides proper HTTP status codes for API responses based on
   * error types. Ensures consistent status code usage across the
   * application and proper client-side error handling.
   * 
   * @private
   * @static
   * @param {string} code - Error code (Firebase or custom)
   * @returns {number} Appropriate HTTP status code
   * 
   * @example
   * ```typescript
   * // Internal usage only
   * const status = ErrorHandler.getStatusCodeForError('auth/user-not-found')
   * console.log(status) // 401
   * 
   * const firestoreStatus = ErrorHandler.getStatusCodeForError('firestore/permission-denied')
   * console.log(firestoreStatus) // 403
   * ```
   */
  private static getStatusCodeForError(code: string): number {
    // Firebase Auth error status codes
    if (code.startsWith('auth/')) {
      const authStatusCodes: Record<string, number> = {
        'auth/user-not-found': 401,
        'auth/wrong-password': 401,
        'auth/email-already-in-use': 409,
        'auth/weak-password': 400,
        'auth/invalid-email': 400,
        'auth/user-disabled': 403,
        'auth/too-many-requests': 429,
        'auth/network-request-failed': 503,
        'auth/popup-closed-by-user': 400,
        'auth/cancelled-popup-request': 400,
        'auth/popup-blocked': 400,
        'auth/invalid-credential': 401,
        'auth/operation-not-allowed': 403,
        'auth/requires-recent-login': 401
      }
      return authStatusCodes[code] || 400
    }
    
    // Firebase Firestore error status codes
    if (code.startsWith('firestore/')) {
      const firestoreStatusCodes: Record<string, number> = {
        'firestore/permission-denied': 403,
        'firestore/not-found': 404,
        'firestore/already-exists': 409,
        'firestore/failed-precondition': 412,
        'firestore/aborted': 409,
        'firestore/out-of-range': 400,
        'firestore/unimplemented': 501,
        'firestore/internal': 500,
        'firestore/unavailable': 503,
        'firestore/data-loss': 500,
        'firestore/unauthenticated': 401
      }
      return firestoreStatusCodes[code] || 500
    }
    
    return 500
  }
}