<script setup lang="ts">
import type { SessionWorkspace } from '~/types/session'
import { toTypedSchema } from '@vee-validate/zod'
import { Field, useForm } from 'vee-validate'
import { z } from 'zod'

const VALIDATION_TEXT = {
  EMAIL_REQUIRED: 'A valid email is required',
  PASSWORD_REQUIRED: 'A password is required',
}

// This is the Zod schema for the form input
// It's used to define the shape that the form data will have
const zodSchema = z.object({
  email: z.string().email(VALIDATION_TEXT.EMAIL_REQUIRED),
  password: z.string().min(1, VALIDATION_TEXT.PASSWORD_REQUIRED),
  trustDevice: z.boolean(),
})

// Zod has a great infer method that will
// infer the shape of the schema into a TypeScript type
type FormInput = z.infer<typeof zodSchema>

const validationSchema = toTypedSchema(zodSchema)
const initialValues = {
  email: '',
  password: '',
  trustDevice: false,
} satisfies FormInput

const {
  handleSubmit,
  isSubmitting,
  setFieldError,
} = useForm({
  validationSchema,
  initialValues,
})

const router = useRouter()
const toaster = useNuiToasts()

// Use new authentication system
const { signIn, signInWithGoogle, logout, workspaces, isLoading, error } = useAuth()

// Workspace selection modal state
const showWorkspaceSelection = ref(false)
const availableWorkspaces = ref<SessionWorkspace[]>([])

// Handle form submission with real authentication
const onSubmit = handleSubmit(async (values) => {
  try {
    // Sign in with email/password
    const userCredential = await signIn(values.email, values.password)

    // Check if user has multiple workspaces
    if (workspaces.value.length > 1) {
      // Show workspace selection modal
      availableWorkspaces.value = workspaces.value
      showWorkspaceSelection.value = true
    }
    else if (workspaces.value.length === 1) {
      // Single workspace - redirect directly
      await handleSuccessfulAuth()
    }
    else {
      // No workspaces - shouldn't happen for existing users
      console.warn('User has no workspaces')
      await handleSuccessfulAuth()
    }
  }
  catch (error: any) {
    // Handle authentication errors
    if (error.code === 'auth/user-not-found') {
      setFieldError('email', 'No account found with this email')
    }
    else if (error.code === 'auth/wrong-password') {
      setFieldError('password', 'Incorrect password')
    }
    else if (error.code === 'auth/invalid-email') {
      setFieldError('email', 'Invalid email address')
    }
    else if (error.code === 'auth/too-many-requests') {
      setFieldError('password', 'Too many failed attempts. Please try again later.')
    }
    else {
      setFieldError('password', error.message || 'Authentication failed')
    }
  }
})

// Handle Google OAuth sign in
async function handleGoogleSignIn() {
  try {
    const userCredential = await signInWithGoogle()

    // Check if user has multiple workspaces
    if (workspaces.value.length > 1) {
      // Show workspace selection modal
      availableWorkspaces.value = workspaces.value
      showWorkspaceSelection.value = true
    }
    else if (workspaces.value.length === 1) {
      // Single workspace - redirect directly
      await handleSuccessfulAuth()
    }
    else {
      // No workspaces - shouldn't happen for existing users
      console.warn('User has no workspaces')
      await handleSuccessfulAuth()
    }
  }
  catch (error: any) {
    toaster.add({
      title: 'Error',
      description: error.message || 'Google sign in failed',
      icon: 'ph:warning-circle-fill',
      progress: true,
    })
  }
}

// Handle successful authentication
async function handleSuccessfulAuth() {
  toaster.add({
    title: 'Success',
    description: 'Welcome back!',
    icon: 'ph:user-circle-fill',
    progress: true,
  })

  // Redirect to dashboard
  await router.push('/dashboards')
}

// Handle workspace selection
async function handleWorkspaceSelected(workspaceId: string) {
  try {
    // The workspace should already be selected by the auth system
    // Just close the modal and redirect
    showWorkspaceSelection.value = false
    await handleSuccessfulAuth()
  }
  catch (error: any) {
    toaster.add({
      title: 'Error',
      description: error.message || 'Failed to select workspace',
      icon: 'ph:warning-circle-fill',
      progress: true,
    })
  }
}

// Handle workspace creation from modal
function handleCreateWorkspace() {
  showWorkspaceSelection.value = false
  router.push('/auth/create-workspace')
}

// Handle logout from workspace selection
async function handleLogout() {
  try {
    showWorkspaceSelection.value = false
    await logout()
    toaster.add({
      title: 'Signed Out',
      description: 'You have been signed out successfully',
      icon: 'ph:sign-out-fill',
      progress: true,
    })
  }
  catch (error: any) {
    console.error('Logout error:', error)
  }
}
</script>

<template>
  <div
    class="bg-muted-100 dark:bg-muted-900 relative min-h-screen w-full overflow-hidden px-4 dark:[--color-input-default-bg:var(--color-muted-950)]"
  >
    <div
      class="mx-auto flex h-16 w-full max-w-6xl items-center justify-between px-4"
    >
      <NuxtLink
        to="/dashboards"
        class="text-muted-400 hover:text-primary-500 dark:text-muted-700 dark:hover:text-primary-500 transition-colors duration-300"
      >
        <TairoLogo class="size-10" />
      </NuxtLink>
      <div>
        <BaseThemeToggle />
      </div>
    </div>
    <div class="flex w-full items-center justify-center">
      <div class="relative mx-auto w-full max-w-2xl">
        <!-- Form -->
        <div class="me-auto ms-auto mt-4 w-full">
          <form
            method="POST"
            action=""
            class="me-auto ms-auto mt-4 w-full max-w-md"
            novalidate
            @submit.prevent="onSubmit"
          >
            <div class="text-center">
              <BaseHeading
                as="h2"
                size="3xl"
                weight="medium"
              >
                Welcome back!
              </BaseHeading>
              <BaseParagraph size="sm" class="text-muted-400 mb-6">
                Login with social media or your credentials
              </BaseParagraph>
            </div>
            <div class="px-8 py-4">
              <div class="mb-4 space-y-4">
                <Field
                  v-slot="{ field, errorMessage, handleChange, handleBlur }"
                  name="email"
                >
                  <BaseField
                    v-slot="{ inputAttrs, inputRef }"
                    label="Email address"
                    :state="errorMessage ? 'error' : 'idle'"
                    :error="errorMessage"
                    :disabled="isSubmitting"
                    required
                  >
                    <BaseInput
                      :ref="inputRef"
                      v-bind="inputAttrs"
                      :model-value="field.value"
                      autocomplete="email"
                      @update:model-value="handleChange"
                      @blur="handleBlur"
                    />
                  </BaseField>
                </Field>

                <Field
                  v-slot="{ field, errorMessage, handleChange, handleBlur }"
                  name="password"
                >
                  <BaseField
                    v-slot="{ inputAttrs, inputRef }"
                    label="Password"
                    :state="errorMessage ? 'error' : 'idle'"
                    :error="errorMessage"
                    :disabled="isSubmitting"
                    required
                  >
                    <BaseInput
                      :ref="inputRef"
                      v-bind="inputAttrs"
                      :model-value="field.value"
                      type="password"
                      autocomplete="current-password"
                      rounded="lg"
                      @update:model-value="handleChange"
                      @blur="handleBlur"
                    />
                  </BaseField>
                </Field>
              </div>
              <div class="mb-6">
                <div class="mt-6 flex items-center justify-between">
                  <Field
                    v-slot="{ field, handleChange, handleBlur }"
                    name="trustDevice"
                  >
                    <BaseCheckbox
                      :model-value="field.value"
                      :disabled="isSubmitting"
                      label="Trust this device for 60 days"
                      variant="default"
                      @update:model-value="handleChange"
                      @blur="handleBlur"
                    />
                  </Field>
                </div>
              </div>
              <div class="mb-6">
                <BaseButton
                  :disabled="isSubmitting"
                  :loading="isSubmitting"
                  type="submit"
                  variant="primary"
                  class="h-12! w-full"
                >
                  Sign In
                </BaseButton>
              </div>
              <div class="mb-6 grid gap-0 sm:grid-cols-3">
                <hr
                  class="border-muted-200 dark:border-muted-700 mt-3 hidden border-t sm:block"
                >
                <span
                  class="bg-muted-100 dark:bg-muted-900 text-muted-400 relative top-0.5 text-center font-sans text-sm"
                >
                  Or continue with
                </span>
                <hr
                  class="border-muted-200 dark:border-muted-700 mt-3 hidden border-t sm:block"
                >
              </div>
              <!-- Social signup -->
              <div class="grid grid-cols-3 gap-2">
                <button
                  type="button"
                  class="bg-muted-200 dark:bg-muted-700 dark:hover:bg-muted-600 text-muted-600 dark:text-muted-400 focus-visible:nui-focus relative inline-flex w-full items-center justify-center rounded-md cursor-pointer px-0 py-3 text-center text-sm font-semibold shadow-xs transition-all duration-300 hover:bg-white"
                  :disabled="isLoading"
                  @click="handleGoogleSignIn"
                >
                  <div v-if="isLoading" class="animate-spin">
                    <Icon name="ph:circle-notch" class="size-5" />
                  </div>
                  <Icon v-else name="fa6-brands:google" class="size-5" />
                </button>
                <button
                  type="button"
                  class="bg-muted-200 dark:bg-muted-700 dark:hover:bg-muted-600 text-muted-600 dark:text-muted-400 focus-visible:nui-focus relative inline-flex w-full items-center justify-center rounded-md cursor-pointer px-0 py-3 text-center text-sm font-semibold shadow-xs transition-all duration-300 hover:bg-white"
                >
                  <Icon name="fa6-brands:twitter" class="size-5" />
                </button>
                <button
                  type="button"
                  class="bg-muted-200 dark:bg-muted-700 dark:hover:bg-muted-600 text-muted-600 dark:text-muted-400 focus-visible:nui-focus relative inline-flex w-full items-center justify-center rounded-md cursor-pointer px-0 py-3 text-center text-sm font-semibold shadow-xs transition-all duration-300 hover:bg-white"
                >
                  <Icon name="fa6-brands:linkedin-in" class="size-5" />
                </button>
              </div>

              <!-- No account link -->
              <p
                class="text-muted-400 mt-4 flex justify-between font-sans text-sm leading-5"
              >
                <span>Don't have an account?</span>
                <NuxtLink
                  to="/auth/signup-2"
                  class="text-primary-600 hover:text-primary-500 font-medium underline-offset-4 transition duration-150 ease-in-out hover:underline"
                >
                  Sign Up
                </NuxtLink>
              </p>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Workspace Selection Modal -->
  <WorkspaceSelectionModal
    v-model="showWorkspaceSelection"
    :workspaces="availableWorkspaces"
    @workspace-selected="handleWorkspaceSelected"
    @create-workspace="handleCreateWorkspace"
    @logout="handleLogout"
  />
</template>
