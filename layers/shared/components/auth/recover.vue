<script setup lang="ts">
/**
 * Password Recovery Component - Firebase-powered password reset
 * 
 * This component provides a secure password reset interface with:
 * - Email validation using Zod schema
 * - Firebase Authentication integration
 * - Security-focused error handling (prevents email enumeration)
 * - Responsive design with success/error states
 * - Form validation with VeeValidate
 * 
 * Features:
 * - Secure password reset email sending
 * - Anti-enumeration protection (always shows success for unknown emails)
 * - Real-time form validation
 * - Loading states and error handling
 * - Firebase Auth error code handling
 * - Network error resilience
 * 
 * Security considerations:
 * - Doesn't reveal whether email exists (prevents enumeration attacks)
 * - Rate limiting protection via Firebase
 * - Network error handling for reliability
 * 
 * @component
 * @example
 * ```vue
 * <template>
 *   <RecoverPassword />
 * </template>
 * ```
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
import { toTypedSchema } from '@vee-validate/zod'
import { Field, useForm } from 'vee-validate'
import { z } from 'zod'

/**
 * Validation messages for form fields
 * Centralized error messages for consistent UX
 */
const VALIDATION_TEXT = {
  EMAIL_REQUIRED: 'A valid email is required',
}

/**
 * Zod schema for email validation
 * Simple schema requiring valid email format
 * 
 * @example
 * ```typescript
 * const formData: FormInput = {
 *   email: '<EMAIL>'
 * }
 * ```
 */
const zodSchema = z.object({
  email: z.string().email(VALIDATION_TEXT.EMAIL_REQUIRED),
})

/**
 * TypeScript type inferred from Zod schema
 * Provides type safety for form data
 */
type FormInput = z.infer<typeof zodSchema>

/**
 * VeeValidate schema and initial form values
 * Converts Zod schema to VeeValidate format
 */
const validationSchema = toTypedSchema(zodSchema)
const initialValues = {
  email: '',
} satisfies FormInput

/**
 * VeeValidate form composition
 * Provides form handling, validation, and submission state
 */
const { handleSubmit, isSubmitting, setFieldError } = useForm({
  validationSchema,
  initialValues,
})

/**
 * Component state for UI feedback
 * Manages success/error states and messages
 */
const success = ref(false)
const errorMessage = ref('')

/**
 * Firebase authentication functionality
 * Gets password reset function from auth composable
 */
const { sendPasswordReset } = useAuth()

/**
 * Handle password reset form submission
 * 
 * Security-focused implementation:
 * 1. Sends password reset email via Firebase
 * 2. Always shows success for unknown emails (prevents enumeration)
 * 3. Handles specific Firebase error codes
 * 4. Provides user-friendly error messages
 * 
 * @param values - Form data containing email address
 */
const onSubmit = handleSubmit(async (values) => {
  // Clear any previous errors before processing
  errorMessage.value = ''

  try {
    // Send password reset email using Firebase Auth
    await sendPasswordReset(values.email)
    
    // Display success message
    success.value = true
  } catch (error: any) {
    // Handle specific Firebase auth errors with security considerations
    if (error.code === 'auth/user-not-found') {
      // SECURITY: Don't reveal if email exists or not
      // Show success message to prevent email enumeration attacks
      success.value = true
    } else if (error.code === 'auth/invalid-email') {
      // Invalid email format - show field-specific error
      setFieldError('email', 'Please enter a valid email address')
    } else if (error.code === 'auth/too-many-requests') {
      // Rate limiting - show generic error
      errorMessage.value = 'Too many password reset attempts. Please try again later.'
    } else if (error.code === 'auth/network-request-failed') {
      // Network issues - show connectivity error
      errorMessage.value = 'Network error. Please check your connection and try again.'
    } else {
      // Generic error message for other cases
      errorMessage.value = 'Unable to send password reset email. Please try again.'
    }
  }
})
</script>

<template>
  <div
    class="bg-muted-100 dark:bg-muted-900 relative min-h-screen w-full overflow-hidden px-4 dark:[--color-input-default-bg:var(--color-muted-950)]"
  >
    <div
      class="mx-auto flex h-16 w-full max-w-6xl items-center justify-between px-4"
    >
      <NuxtLink
        to="/dashboards"
        class="text-muted-400 hover:text-primary-500 dark:text-muted-700 dark:hover:text-primary-500 transition-colors duration-300"
      >
        <TairoLogo class="size-10" />
      </NuxtLink>
      <div>
        <BaseThemeToggle />
      </div>
    </div>
    <div class="flex w-full items-center justify-center">
      <div class="relative mx-auto w-full max-w-2xl">
        <!-- Form -->
        <div class="me-auto ms-auto mt-4 w-full">
          <div class="me-auto ms-auto mt-4 w-full max-w-md">
            <div class="text-center">
              <BaseHeading
                as="h2"
                size="3xl"
                weight="medium"
              >
                Recover Password
              </BaseHeading>
              <BaseParagraph size="sm" class="text-muted-400 mb-6">
                Follow the instuctions sent to your email address
              </BaseParagraph>
            </div>
            <Transition
              mode="out-in"
              enter-active-class="transition-all duration-300 ease-out"
              enter-from-class="scale-0 opacity-0"
              enter-to-class="scale-100 opacity-100"
              leave-active-class="transition-all duration-75 ease-in"
              leave-from-class="scale-100 opacity-100"
              leave-to-class="scale-0 opacity-0"
            >
              <div v-if="success" class="px-8 py-4">
                <div class="mb-4 space-y-4">
                  <BaseMessage class="p-6" :closable="false">
                    <p class="text-base">
                      An email has been sent to you with instructions on how to
                      reset your password.
                    </p>

                    <small class="block pt-2">
                      If you don't receive an email, please check your spam
                      folder. If you still don't receive an email, that means
                      you don't have an account
                    </small>
                  </BaseMessage>
                </div>
              </div>
              <form
                v-else
                method="POST"
                action=""
                class="px-8 py-4"
                novalidate
                @submit.prevent="onSubmit"
              >
                <!-- Error message display -->
                <div v-if="errorMessage" class="mb-4">
                  <BaseMessage class="p-4" type="error" :closable="false">
                    <p class="text-sm">{{ errorMessage }}</p>
                  </BaseMessage>
                </div>
                
                <div class="mb-4 space-y-4">
                  <Field
                    v-slot="{ field, errorMessage, handleChange, handleBlur }"
                    name="email"
                  >
                    <BaseField
                      v-slot="{ inputAttrs, inputRef }"
                      label="Email address"
                      :state="errorMessage ? 'error' : 'idle'"
                      :error="errorMessage"
                      :disabled="isSubmitting"
                      required
                    >
                      <BaseInput
                        :ref="inputRef"
                        v-bind="inputAttrs"
                        :model-value="field.value"
                        autocomplete="email"
                        @update:model-value="handleChange"
                        @blur="handleBlur"
                      />
                    </BaseField>
                  </Field>
                </div>

                <div class="mb-6">
                  <BaseButton
                    :disabled="isSubmitting"
                    :loading="isSubmitting"
                    type="submit"
                    variant="primary"
                    class="h-12! w-full"
                  >
                    Recover Password
                  </BaseButton>
                </div>
                <!-- No account link -->
                <p
                  class="text-muted-400 mt-4 flex justify-between font-sans text-sm leading-5"
                >
                  <span>False alert?</span>
                  <NuxtLink
                    to="/auth/login-1"
                    class="text-primary-600 hover:text-primary-500 font-medium underline-offset-4 transition duration-150 ease-in-out hover:underline"
                  >
                    Sign in
                  </NuxtLink>
                </p>
              </form>
            </Transition>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
