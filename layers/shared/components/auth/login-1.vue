<script setup lang="ts">
import type { SessionWorkspace } from '~/types/session'
import { toTypedSchema } from '@vee-validate/zod'
import { Field, useForm } from 'vee-validate'
import { z } from 'zod'

const VALIDATION_TEXT = {
  EMAIL_REQUIRED: 'A valid email is required',
  PASSWORD_REQUIRED: 'A password is required',
}

// This is the Zod schema for the form input
// It's used to define the shape that the form data will have
const zodSchema = z.object({
  email: z.string().email(VALIDATION_TEXT.EMAIL_REQUIRED),
  password: z.string().min(1, VALIDATION_TEXT.PASSWORD_REQUIRED),
  trustDevice: z.boolean(),
})

// Zod has a great infer method that will
// infer the shape of the schema into a TypeScript type
type FormInput = z.infer<typeof zodSchema>

const validationSchema = toTypedSchema(zodSchema)
const initialValues = {
  email: '',
  password: '',
  trustDevice: false,
} satisfies FormInput

const {
  handleSubmit,
  isSubmitting,
  setFieldError,
} = useForm({
  validationSchema,
  initialValues,
})

const router = useRouter()
const toaster = useNuiToasts()

// Use new authentication system
const { signIn, signInWithGoogle, logout, workspaces, isLoading, error } = useAuth()

// Workspace selection modal state
const showWorkspaceSelection = ref(false)
const availableWorkspaces = ref<SessionWorkspace[]>([])

// Handle form submission with real authentication
const onSubmit = handleSubmit(async (values) => {
  try {
    // Sign in with email/password
    const userCredential = await signIn(values.email, values.password)

    // Check if user has multiple workspaces
    if (workspaces.value.length > 1) {
      // Show workspace selection modal
      availableWorkspaces.value = workspaces.value
      showWorkspaceSelection.value = true
    }
    else if (workspaces.value.length === 1) {
      // Single workspace - redirect directly
      await handleSuccessfulAuth()
    }
    else {
      // No workspaces - shouldn't happen for existing users
      console.warn('User has no workspaces')
      await handleSuccessfulAuth()
    }
  }
  catch (error: any) {
    // Handle authentication errors
    if (error.code === 'auth/user-not-found') {
      setFieldError('email', 'No account found with this email')
    }
    else if (error.code === 'auth/wrong-password') {
      setFieldError('password', 'Incorrect password')
    }
    else if (error.code === 'auth/invalid-email') {
      setFieldError('email', 'Invalid email address')
    }
    else if (error.code === 'auth/too-many-requests') {
      setFieldError('password', 'Too many failed attempts. Please try again later.')
    }
    else {
      setFieldError('password', error.message || 'Authentication failed')
    }
  }
})

// Handle Google OAuth sign in
async function handleGoogleSignIn() {
  try {
    const userCredential = await signInWithGoogle()

    // Check if user has multiple workspaces
    if (workspaces.value.length > 1) {
      // Show workspace selection modal
      availableWorkspaces.value = workspaces.value
      showWorkspaceSelection.value = true
    }
    else if (workspaces.value.length === 1) {
      // Single workspace - redirect directly
      await handleSuccessfulAuth()
    }
    else {
      // No workspaces - shouldn't happen for existing users
      console.warn('User has no workspaces')
      await handleSuccessfulAuth()
    }
  }
  catch (error: any) {
    toaster.add({
      title: 'Error',
      description: error.message || 'Google sign in failed',
      icon: 'ph:warning-circle-fill',
      progress: true,
    })
  }
}

// Handle successful authentication
async function handleSuccessfulAuth() {
  toaster.add({
    title: 'Success',
    description: 'Welcome back!',
    icon: 'ph:user-circle-fill',
    progress: true,
  })

  // Redirect to dashboard
  await router.push('/dashboards')
}

// Handle workspace selection
async function handleWorkspaceSelected(workspaceId: string) {
  try {
    // The workspace should already be selected by the auth system
    // Just close the modal and redirect
    showWorkspaceSelection.value = false
    await handleSuccessfulAuth()
  }
  catch (error: any) {
    toaster.add({
      title: 'Error',
      description: error.message || 'Failed to select workspace',
      icon: 'ph:warning-circle-fill',
      progress: true,
    })
  }
}

// Handle workspace creation from modal
function handleCreateWorkspace() {
  showWorkspaceSelection.value = false
  router.push('/auth/create-workspace')
}

// Handle logout from workspace selection
async function handleLogout() {
  try {
    showWorkspaceSelection.value = false
    await logout()
    toaster.add({
      title: 'Signed Out',
      description: 'You have been signed out successfully',
      icon: 'ph:sign-out-fill',
      progress: true,
    })
  }
  catch (error: any) {
    console.error('Logout error:', error)
  }
}
</script>

<template>
  <div class="dark:bg-muted-800 flex min-h-screen bg-white">
    <div
      class="bg-muted-100 dark:bg-muted-900 relative hidden w-0 flex-1 items-center justify-center lg:flex lg:w-3/5"
    >
      <div
        class="mx-auto flex size-full max-w-4xl items-center justify-center"
      >
        <!-- Media image -->
        <img
          class="mx-auto max-w-xl"
          src="/img/illustrations/station.svg"
          alt=""
          width="619"
          height="594"
        >
      </div>
    </div>
    <div
      class="relative flex flex-1 flex-col justify-center px-6 py-12 lg:w-2/5 lg:flex-none"
    >
      <div class="dark:bg-muted-800 relative mx-auto w-full max-w-sm bg-white">
        <!-- Nav -->
        <div class="flex w-full items-center justify-between">
          <NuxtLink
            to="/dashboards"
            class="text-muted-400 hover:text-primary-500 flex items-center gap-2 font-sans font-medium transition-colors duration-300"
          >
            <Icon name="gg:arrow-long-left" class="size-5" />
            <span>Back to Home</span>
          </NuxtLink>
          <!-- Theme button -->
          <BaseThemeToggle />
        </div>
        <div>
          <BaseHeading
            as="h2"
            size="3xl"
            lead="relaxed"
            weight="medium"
            class="mt-6"
          >
            Welcome back.
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-400 mb-6">
            Login with social media or your credentials
          </BaseParagraph>
          <!-- Social Sign Up Buttons -->
          <div class="flex flex-wrap justify-between gap-4">
            <!-- Google button -->
            <button
              type="button"
              class="dark:bg-muted-700 text-muted-800 border-muted-300 dark:border-muted-600 focus-visible:nui-focus relative inline-flex grow items-center justify-center gap-2 rounded-xl border bg-white px-6 py-4 dark:text-white"
              :disabled="isLoading"
              @click="handleGoogleSignIn"
            >
              <div v-if="isLoading" class="animate-spin">
                <Icon name="ph:circle-notch" class="size-5" />
              </div>
              <Icon v-else name="logos:google-icon" class="size-5" />
              <div>Login with Google</div>
            </button>
            <!-- Twitter button -->
            <button
              class="bg-muted-200 dark:bg-muted-700 hover:bg-muted-100 dark:hover:bg-muted-600 text-muted-600 dark:text-muted-400 focus-visible:nui-focus w-[calc(50%_-_0.5rem)] cursor-pointer rounded-xl px-5 py-4 text-center transition-colors duration-300 md:w-auto"
            >
              <Icon name="fa6-brands:x-twitter" class="mx-auto size-4" />
            </button>
            <!-- Linkedin button -->
            <button
              class="bg-muted-200 dark:bg-muted-700 hover:bg-muted-100 dark:hover:bg-muted-600 text-muted-600 dark:text-muted-400 focus-visible:nui-focus w-[calc(50%_-_0.5rem)] cursor-pointer rounded-xl px-5 py-4 text-center transition-colors duration-300 md:w-auto"
            >
              <Icon name="fa6-brands:linkedin-in" class="mx-auto size-4" />
            </button>
          </div>
          <!-- 'or' divider -->
          <div class="flex-100 mt-8 flex items-center">
            <hr
              class="border-muted-200 dark:border-muted-700 flex-auto border-t-2"
            >
            <span class="text-muted-400 px-4 font-sans font-light"> OR </span>
            <hr
              class="border-muted-200 dark:border-muted-700 flex-auto border-t-2"
            >
          </div>
        </div>

        <!-- Form section -->
        <div class="mt-6">
          <div class="mt-5">
            <!-- Form -->
            <form
              method="POST"
              action=""
              class="mt-6"
              novalidate
              @submit.prevent="onSubmit"
            >
              <div class="space-y-4">
                <Field
                  v-slot="{ field, errorMessage, handleChange, handleBlur }"
                  name="email"
                >
                  <BaseField
                    v-slot="{ inputAttrs, inputRef }"
                    label="Email address"
                    :state="errorMessage ? 'error' : 'idle'"
                    :error="errorMessage"
                    :disabled="isSubmitting"
                    required
                  >
                    <BaseInput
                      :ref="inputRef"
                      v-bind="inputAttrs"
                      :model-value="field.value"
                      autocomplete="email"
                      rounded="lg"
                      @update:model-value="handleChange"
                      @blur="handleBlur"
                    />
                  </BaseField>
                </Field>

                <Field
                  v-slot="{ field, errorMessage, handleChange, handleBlur }"
                  name="password"
                >
                  <BaseField
                    v-slot="{ inputAttrs, inputRef }"
                    label="Password"
                    :state="errorMessage ? 'error' : 'idle'"
                    :error="errorMessage"
                    :disabled="isSubmitting"
                    required
                  >
                    <BaseInput
                      :ref="inputRef"
                      v-bind="inputAttrs"
                      :model-value="field.value"
                      type="password"
                      autocomplete="current-password"
                      rounded="lg"
                      @update:model-value="handleChange"
                      @blur="handleBlur"
                    />
                  </BaseField>
                </Field>
              </div>

              <!-- Remember -->
              <div class="mt-6 flex items-center justify-between">
                <Field
                  v-slot="{ field, handleChange, handleBlur }"
                  name="trustDevice"
                >
                  <BaseCheckbox
                    :model-value="field.value"
                    :disabled="isSubmitting"
                    label="Trust for 60 days"
                    variant="default"
                    @update:model-value="handleChange"
                    @blur="handleBlur"
                  />
                </Field>

                <div class="text-xs leading-5">
                  <NuxtLink
                    to="/auth/recover"
                    class="text-primary-600 hover:text-primary-500 font-sans font-medium underline-offset-4 transition duration-150 ease-in-out hover:underline"
                  >
                    Forgot your password?
                  </NuxtLink>
                </div>
              </div>

              <!-- Submit -->
              <div class="mt-6">
                <div class="block w-full rounded-md shadow-xs">
                  <BaseButton
                    :disabled="isSubmitting"
                    :loading="isSubmitting"
                    type="submit"
                    variant="primary"
                    rounded="lg"
                    class="h-11! w-full"
                  >
                    Sign in
                  </BaseButton>
                </div>
              </div>
            </form>

            <!-- No account link -->
            <p
              class="text-muted-400 mt-4 flex justify-between font-sans text-xs leading-5"
            >
              <span>Don't have an account?</span>
              <NuxtLink
                to="/auth/signup-2"
                class="text-primary-600 hover:text-primary-500 font-medium underline-offset-4 transition duration-150 ease-in-out hover:underline"
              >
                start your 14-day free trial
              </NuxtLink>
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Workspace Selection Modal -->
  <WorkspaceSelectionModal
    v-model="showWorkspaceSelection"
    :workspaces="availableWorkspaces"
    @workspace-selected="handleWorkspaceSelected"
    @create-workspace="handleCreateWorkspace"
    @logout="handleLogout"
  />
</template>
