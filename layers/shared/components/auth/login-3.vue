<script setup lang="ts">
import type { SessionWorkspace } from '~/types/session'
import { toTypedSchema } from '@vee-validate/zod'
import { Field, useForm } from 'vee-validate'
import { z } from 'zod'

const VALIDATION_TEXT = {
  EMAIL_REQUIRED: 'A valid email is required',
  PASSWORD_REQUIRED: 'A password is required',
}

// This is the Zod schema for the form input
// It's used to define the shape that the form data will have
const zodSchema = z.object({
  email: z.string().email(VALIDATION_TEXT.EMAIL_REQUIRED),
  password: z.string().min(1, VALIDATION_TEXT.PASSWORD_REQUIRED),
  trustDevice: z.boolean(),
})

// Zod has a great infer method that will
// infer the shape of the schema into a TypeScript type
type FormInput = z.infer<typeof zodSchema>

const validationSchema = toTypedSchema(zodSchema)
const initialValues = {
  email: '',
  password: '',
  trustDevice: false,
} satisfies FormInput

const {
  handleSubmit,
  isSubmitting,
  setFieldError,
} = useForm({
  validationSchema,
  initialValues,
})

const router = useRouter()
const toaster = useNuiToasts()

// Use new authentication system
const { signIn, signInWithGoogle, logout, workspaces, isLoading, error } = useAuth()

// Workspace selection modal state
const showWorkspaceSelection = ref(false)
const availableWorkspaces = ref<SessionWorkspace[]>([])

// Handle form submission with real authentication
const onSubmit = handleSubmit(async (values) => {
  try {
    // Sign in with email/password
    const userCredential = await signIn(values.email, values.password)

    // Check if user has multiple workspaces
    if (workspaces.value.length > 1) {
      // Show workspace selection modal
      availableWorkspaces.value = workspaces.value
      showWorkspaceSelection.value = true
    }
    else if (workspaces.value.length === 1) {
      // Single workspace - redirect directly
      await handleSuccessfulAuth()
    }
    else {
      // No workspaces - shouldn't happen for existing users
      console.warn('User has no workspaces')
      await handleSuccessfulAuth()
    }
  }
  catch (error: any) {
    // Handle authentication errors
    if (error.code === 'auth/user-not-found') {
      setFieldError('email', 'No account found with this email')
    }
    else if (error.code === 'auth/wrong-password') {
      setFieldError('password', 'Incorrect password')
    }
    else if (error.code === 'auth/invalid-email') {
      setFieldError('email', 'Invalid email address')
    }
    else if (error.code === 'auth/too-many-requests') {
      setFieldError('password', 'Too many failed attempts. Please try again later.')
    }
    else {
      setFieldError('password', error.message || 'Authentication failed')
    }
  }
})

// Handle Google OAuth sign in
async function handleGoogleSignIn() {
  try {
    const userCredential = await signInWithGoogle()

    // Check if user has multiple workspaces
    if (workspaces.value.length > 1) {
      // Show workspace selection modal
      availableWorkspaces.value = workspaces.value
      showWorkspaceSelection.value = true
    }
    else if (workspaces.value.length === 1) {
      // Single workspace - redirect directly
      await handleSuccessfulAuth()
    }
    else {
      // No workspaces - shouldn't happen for existing users
      console.warn('User has no workspaces')
      await handleSuccessfulAuth()
    }
  }
  catch (error: any) {
    toaster.add({
      title: 'Error',
      description: error.message || 'Google sign in failed',
      icon: 'ph:warning-circle-fill',
      progress: true,
    })
  }
}

// Handle successful authentication
async function handleSuccessfulAuth() {
  toaster.add({
    title: 'Success',
    description: 'Welcome back!',
    icon: 'ph:user-circle-fill',
    progress: true,
  })

  // Redirect to dashboard
  await router.push('/dashboards')
}

// Handle workspace selection
async function handleWorkspaceSelected(workspaceId: string) {
  try {
    // The workspace should already be selected by the auth system
    // Just close the modal and redirect
    showWorkspaceSelection.value = false
    await handleSuccessfulAuth()
  }
  catch (error: any) {
    toaster.add({
      title: 'Error',
      description: error.message || 'Failed to select workspace',
      icon: 'ph:warning-circle-fill',
      progress: true,
    })
  }
}

// Handle workspace creation from modal
function handleCreateWorkspace() {
  showWorkspaceSelection.value = false
  router.push('/auth/create-workspace')
}

// Handle logout from workspace selection
async function handleLogout() {
  try {
    showWorkspaceSelection.value = false
    await logout()
    toaster.add({
      title: 'Signed Out',
      description: 'You have been signed out successfully',
      icon: 'ph:sign-out-fill',
      progress: true,
    })
  }
  catch (error: any) {
    console.error('Logout error:', error)
  }
}
</script>

<template>
  <div class="flex h-screen w-full flex-col items-center md:flex-row">
    <div
      class="bg-muted-100 dark:bg-muted-900 hidden h-screen w-full md:w-1/2 lg:block xl:w-2/3"
    >
      <div
        class="mx-auto flex size-full max-w-4xl items-center justify-center"
      >
        <!-- Media image -->
        <img
          class="mx-auto max-w-xl"
          src="/img/illustrations/people.svg"
          alt=""
          width="1200"
          height="996"
        >
      </div>
    </div>

    <div
      class="dark:bg-muted-800 flex h-screen w-full items-center justify-center bg-white px-6 md:mx-auto md:w-1/2 md:max-w-md lg:max-w-full lg:px-16 xl:w-1/3 xl:px-12"
    >
      <div
        class="mx-auto flex size-full max-w-xs flex-col items-center justify-between py-8"
      >
        <div class="mx-auto flex w-full max-w-xs items-center justify-between">
          <NuxtLink
            to="/dashboards"
            class="text-muted-400 hover:text-primary-500 dark:text-muted-700 dark:hover:text-primary-500 transition-colors duration-300"
          >
            <TairoLogo class="size-10" />
          </NuxtLink>
          <div>
            <BaseThemeToggle />
          </div>
        </div>
        <div class="w-full">
          <BaseHeading
            as="h2"
            size="3xl"
            weight="medium"
          >
            Welcome back!
          </BaseHeading>
          <BaseParagraph size="sm" class="text-muted-400 mb-6">
            Enter your account credentials to sign in
          </BaseParagraph>

          <form
            method="POST"
            action=""
            class="mt-6"
            novalidate
            @submit.prevent="onSubmit"
          >
            <div class="space-y-4">
              <Field
                v-slot="{ field, errorMessage, handleChange, handleBlur }"
                name="email"
              >
                <BaseField
                  v-slot="{ inputAttrs, inputRef }"
                  label="Email address"
                  :state="errorMessage ? 'error' : 'idle'"
                  :error="errorMessage"
                  :disabled="isSubmitting"
                  required
                >
                  <TairoInput
                    :ref="inputRef"
                    v-bind="inputAttrs"
                    :model-value="field.value"
                    autocomplete="email"
                    rounded="md"
                    icon="solar:user-rounded-linear"
                    @update:model-value="handleChange"
                    @blur="handleBlur"
                  />
                </BaseField>
              </Field>
              <Field
                v-slot="{ field, errorMessage, handleChange, handleBlur }"
                name="password"
              >
                <BaseField
                  v-slot="{ inputAttrs, inputRef }"
                  label="Password"
                  :state="errorMessage ? 'error' : 'idle'"
                  :error="errorMessage"
                  :disabled="isSubmitting"
                  required
                >
                  <TairoInput
                    :ref="inputRef"
                    v-bind="inputAttrs"
                    :model-value="field.value"
                    type="password"
                    icon="solar:lock-keyhole-linear"
                    autocomplete="current-password"
                    @update:model-value="handleChange"
                    @blur="handleBlur"
                  />
                </BaseField>
              </Field>
            </div>

            <!-- Remember -->
            <div class="mt-6 flex items-center justify-between">
              <Field
                v-slot="{ field, handleChange, handleBlur }"
                name="trustDevice"
              >
                <BaseCheckbox
                  :model-value="field.value"
                  :disabled="isSubmitting"
                  label="Trust for 60 days"
                  variant="default"
                  @update:model-value="handleChange"
                  @blur="handleBlur"
                />
              </Field>

              <div class="text-sm leading-5">
                <NuxtLink
                  to="/auth/recover"
                  class="text-primary-600 hover:text-primary-500 font-sans text-xs font-medium underline-offset-4 transition duration-150 ease-in-out hover:underline"
                >
                  Forgot your password?
                </NuxtLink>
              </div>
            </div>

            <!-- Submit -->
            <div class="mt-6">
              <div class="block w-full rounded-md shadow-xs">
                <BaseButton
                  :disabled="isSubmitting"
                  :loading="isSubmitting"
                  type="submit"
                  variant="primary"
                  class="h-11! w-full"
                >
                  Sign in
                </BaseButton>
              </div>
            </div>
          </form>

          <hr
            class="border-muted-200 dark:border-muted-700 my-6 w-full border-t"
          >

          <BaseButton
            class="h-11! w-full"
            :disabled="isLoading"
            @click="handleGoogleSignIn"
          >
            <div v-if="isLoading" class="animate-spin me-1">
              <Icon name="ph:circle-notch" class="size-4" />
            </div>
            <Icon v-else name="logos:google-icon" class="me-1 size-4" />
            <span>Sign In with Google</span>
          </BaseButton>

          <!-- No account link -->
          <p
            class="text-muted-400 mt-4 flex justify-between font-sans text-xs leading-5"
          >
            <span>Don't have an account?</span>
            <NuxtLink
              to="/auth/signup-1"
              class="text-primary-600 hover:text-primary-500 font-medium underline-offset-4 transition duration-150 ease-in-out hover:underline"
            >
              Create Account
            </NuxtLink>
          </p>
        </div>
        <div class="text-center">
          <BaseText size="xs" class="text-muted-400">
            © {{ new Date().getFullYear() }} Tairo. All rights reserved.
          </BaseText>
        </div>
      </div>
    </div>
  </div>

  <!-- Workspace Selection Modal -->
  <WorkspaceSelectionModal
    v-model="showWorkspaceSelection"
    :workspaces="availableWorkspaces"
    @workspace-selected="handleWorkspaceSelected"
    @create-workspace="handleCreateWorkspace"
    @logout="handleLogout"
  />
</template>
