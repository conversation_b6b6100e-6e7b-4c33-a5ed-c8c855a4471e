import { LRUCache } from 'lru-cache'
import { createHash, randomUUID } from 'crypto'
import { 
  defineE<PERSON><PERSON><PERSON><PERSON>, 
  setHead<PERSON>, 
  removeR<PERSON>po<PERSON><PERSON>eader, 
  create<PERSON><PERSON>r,
  getHeader,
  getC<PERSON>ie,
  getRequest<PERSON>,
  readBody,
  getMethod,
  getQ<PERSON>y
} from 'h3'
import { auditLogger } from '../utils/audit-logger'
import { <PERSON>rror<PERSON>and<PERSON> } from '../../utils/error-handler'

/**
 * Enhanced Security Middleware with CSRF Protection, Rate Limiting, and Request Validation
 * 
 * Comprehensive security middleware providing multiple layers of protection:
 * - CSRF token validation for state-changing operations
 * - Rate limiting with IP blocking for abuse prevention
 * - Request validation (size, structure, methods)
 * - Attack pattern detection (XSS, SQL injection, path traversal)
 * - Security headers (HSTS, CSP, XSS protection)
 * - Bot detection and suspicious activity monitoring
 * - Audit logging integration for security events
 * 
 * @example
 * ```typescript
 * // Middleware is applied globally to all API routes
 * // Access security context in handlers:
 * export default defineEventHandler((event) => {
 *   const security = event.context.security
 *   console.log('Request ID:', security.requestId)
 *   console.log('Risk score:', security.patterns)
 * })
 * ```
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

/**
 * Security configuration object with all protection settings
 * 
 * Centralized configuration for all security features:
 * - Rate limiting thresholds and windows
 * - CSRF protection settings
 * - Request validation limits
 * - Security headers configuration
 * 
 * @example
 * ```typescript
 * // Rate limiting config
 * const rateLimits = SECURITY_CONFIG.rateLimiting
 * if (requestCount > rateLimits.maxRequests) {
 *   // Block request
 * }
 * ```
 */
const SECURITY_CONFIG = {
  // Rate limiting
  rateLimiting: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    maxAuthRequests: 5,
    maxUploadRequests: 10,
    blockDuration: 60 * 60 * 1000, // 1 hour
  },
  
  // CSRF protection
  csrf: {
    tokenLength: 32,
    maxAge: 60 * 60 * 1000, // 1 hour
    cookieName: 'omni-csrf-token',
    headerName: 'X-CSRF-Token',
    excludePaths: ['/api/auth/signin', '/api/auth/signup', '/api/health'],
  },
  
  // Request validation
  validation: {
    maxBodySize: 10 * 1024 * 1024, // 10MB
    maxHeaderSize: 8 * 1024, // 8KB
    maxUrlLength: 2048,
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  },
  
  // Security headers
  headers: {
    // HSTS
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
    
    // XSS Protection
    'X-XSS-Protection': '1; mode=block',
    'X-Content-Type-Options': 'nosniff',
    
    // Frame Options
    'X-Frame-Options': 'SAMEORIGIN',
    
    // Content Security Policy
    'Content-Security-Policy': [
      "default-src 'self'",
      "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://apis.google.com",
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https: blob:",
      "connect-src 'self' https://firebaseapp.com https://firebaseio.com wss://*.firebaseio.com https://identitytoolkit.googleapis.com",
      "frame-src 'self' https://omni.firebaseapp.com",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "frame-ancestors 'self'",
      "upgrade-insecure-requests"
    ].join('; '),
    
    // Referrer Policy
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    
    // Permissions Policy
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()'
  }
}

/**
 * LRU caches for security features with TTL support
 * 
 * Efficient in-memory caching for security-related data:
 * - rateLimitCache: Tracks request counts per IP/endpoint
 * - blockedIPCache: Stores blocked IPs with expiration
 * - csrfTokenCache: Manages CSRF tokens per session
 * - requestFingerprints: Bot detection via request patterns
 * 
 * @example
 * ```typescript
 * // Check rate limit
 * const current = rateLimitCache.get(`${ip}:${endpoint}`) || 0
 * if (current >= maxRequests) {
 *   // Rate limit exceeded
 * }
 * ```
 */
const rateLimitCache = new LRUCache<string, number>({
  max: 10000,
  ttl: SECURITY_CONFIG.rateLimiting.windowMs
})

const blockedIPCache = new LRUCache<string, boolean>({
  max: 1000,
  ttl: SECURITY_CONFIG.rateLimiting.blockDuration
})

const csrfTokenCache = new LRUCache<string, string>({
  max: 10000,
  ttl: SECURITY_CONFIG.csrf.maxAge
})

const requestFingerprints = new LRUCache<string, number>({
  max: 10000,
  ttl: 60 * 1000 // 1 minute
})

/**
 * Generate cryptographically secure CSRF token
 * 
 * Creates a unique token combining:
 * - UUID for uniqueness
 * - SHA-256 hash of random data
 * - Timestamp-based entropy
 * 
 * @returns Secure CSRF token string
 * 
 * @example
 * ```typescript
 * const token = generateCSRFToken()
 * csrfTokenCache.set(sessionId, token)
 * response.headers['X-CSRF-Token'] = token
 * ```
 */
function generateCSRFToken(): string {
  return randomUUID() + createHash('sha256').update(Math.random().toString()).digest('hex').substring(0, 16)
}

/**
 * Validate CSRF token against stored session token
 * 
 * Compares provided token with cached token for the session:
 * - Constant-time comparison to prevent timing attacks
 * - Token expiration handling
 * - Session validation
 * 
 * @param sessionId - Session identifier
 * @param token - CSRF token to validate
 * @returns True if token is valid, false otherwise
 * 
 * @example
 * ```typescript
 * const sessionId = getCookie(event, 'omni-session')
 * const token = getHeader(event, 'X-CSRF-Token')
 * 
 * if (!validateCSRFToken(sessionId, token)) {
 *   throw createError({ statusCode: 403, statusMessage: 'Invalid CSRF token' })
 * }
 * ```
 */
function validateCSRFToken(sessionId: string, token: string): boolean {
  const storedToken = csrfTokenCache.get(sessionId)
  return storedToken === token
}

/**
 * Get client fingerprint for advanced bot detection and tracking
 * 
 * Creates a unique fingerprint combining:
 * - IP address
 * - User agent string
 * - Accept headers (language, encoding)
 * - SHA-256 hash for anonymization
 * 
 * Used for:
 * - Bot detection (rapid requests from same fingerprint)
 * - Session tracking
 * - Suspicious activity correlation
 * 
 * @param event - H3 event object
 * @returns SHA-256 hash representing client fingerprint
 * 
 * @example
 * ```typescript
 * const fingerprint = getClientFingerprint(event)
 * const requestCount = requestFingerprints.get(fingerprint) || 0
 * 
 * if (requestCount > 10) {
 *   // Potential bot activity
 * }
 * ```
 */
function getClientFingerprint(event: any): string {
  const ip = getRequestIP(event) || 'unknown'
  const userAgent = getHeader(event, 'user-agent') || ''
  const acceptLanguage = getHeader(event, 'accept-language') || ''
  const acceptEncoding = getHeader(event, 'accept-encoding') || ''
  
  return createHash('sha256')
    .update(ip + userAgent + acceptLanguage + acceptEncoding)
    .digest('hex')
}

/**
 * Detect suspicious patterns and attack attempts
 * 
 * Analyzes requests for common attack patterns:
 * - XSS attempts (script tags, javascript:, event handlers)
 * - SQL injection (union select, or 1=1, drop table)
 * - Path traversal (../../../, /etc/passwd)
 * - Bot-like behavior (missing/short user agent, rapid requests)
 * 
 * @param event - H3 event object
 * @returns Object with suspicion status and detected patterns
 * 
 * @example
 * ```typescript
 * const check = detectSuspiciousPatterns(event)
 * if (check.suspicious) {
 *   console.log('Detected patterns:', check.patterns)
 *   await auditLogger.logEvent('security.suspicious_activity', { ... })
 * }
 * ```
 */
function detectSuspiciousPatterns(event: any): { suspicious: boolean; patterns: string[] } {
  const patterns: string[] = []
  const url = event.node.req.url || ''
  const userAgent = getHeader(event, 'user-agent') || ''
  
  // Common attack patterns
  const attackPatterns = [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /union\s+select/gi,
    /or\s+1\s*=\s*1/gi,
    /drop\s+table/gi,
    /insert\s+into/gi,
    /update\s+.*\s+set/gi,
    /\.\.\/\.\.\//gi,
    /etc\/passwd/gi,
    /proc\/self\/environ/gi
  ]
  
  // Check URL for attacks
  attackPatterns.forEach(pattern => {
    if (pattern.test(url)) {
      patterns.push('malicious_url')
    }
  })
  
  // Check for bot-like behavior
  if (!userAgent || userAgent.length < 20) {
    patterns.push('suspicious_user_agent')
  }
  
  // Check for rapid requests (bot detection)
  const fingerprint = getClientFingerprint(event)
  const requestCount = requestFingerprints.get(fingerprint) || 0
  requestFingerprints.set(fingerprint, requestCount + 1)
  
  if (requestCount > 10) {
    patterns.push('rapid_requests')
  }
  
  return {
    suspicious: patterns.length > 0,
    patterns
  }
}

/**
 * Rate limiting logic with adaptive thresholds
 * 
 * Implements sophisticated rate limiting:
 * - Different limits for different endpoint types
 * - IP-based tracking with automatic blocking
 * - Time window sliding calculation
 * - Endpoint-specific thresholds (auth, upload, general)
 * 
 * @param event - H3 event object
 * @returns Rate limit status with remaining requests and reset time
 * 
 * @example
 * ```typescript
 * const rateLimit = checkRateLimit(event)
 * if (!rateLimit.allowed) {
 *   setHeaders(event, {
 *     'X-RateLimit-Remaining': '0',
 *     'X-RateLimit-Reset': new Date(rateLimit.resetTime).toISOString()
 *   })
 *   throw createError({ statusCode: 429 })
 * }
 * ```
 */
function checkRateLimit(event: any): { allowed: boolean; remaining: number; resetTime: number } {
  const ip = getRequestIP(event) || 'unknown'
  const url = event.node.req.url || ''
  const method = getMethod(event)
  
  // Check if IP is blocked
  if (blockedIPCache.has(ip)) {
    return { allowed: false, remaining: 0, resetTime: Date.now() + SECURITY_CONFIG.rateLimiting.blockDuration }
  }
  
  // Determine rate limit based on endpoint
  let maxRequests = SECURITY_CONFIG.rateLimiting.maxRequests
  if (url.includes('/api/auth/')) {
    maxRequests = SECURITY_CONFIG.rateLimiting.maxAuthRequests
  } else if (url.includes('/upload') || method === 'POST') {
    maxRequests = SECURITY_CONFIG.rateLimiting.maxUploadRequests
  }
  
  const key = `${ip}:${url.split('?')[0]}`
  const current = rateLimitCache.get(key) || 0
  
  if (current >= maxRequests) {
    // Block IP after multiple violations
    blockedIPCache.set(ip, true)
    
    // Log security event
    auditLogger.logEvent('security.rate_limit_exceeded', {
      event,
      metadata: { ip, url, method, current, maxRequests }
    })
    
    return { allowed: false, remaining: 0, resetTime: Date.now() + SECURITY_CONFIG.rateLimiting.windowMs }
  }
  
  rateLimitCache.set(key, current + 1)
  
  return {
    allowed: true,
    remaining: maxRequests - current - 1,
    resetTime: Date.now() + SECURITY_CONFIG.rateLimiting.windowMs
  }
}

/**
 * Validate request structure and size limits
 * 
 * Validates incoming requests against security policies:
 * - URL length limits (prevents buffer overflow)
 * - HTTP method allowlist
 * - Content length validation
 * - Header size limits
 * 
 * @param event - H3 event object
 * @returns Validation result with error details
 * 
 * @example
 * ```typescript
 * const validation = validateRequest(event)
 * if (!validation.valid) {
 *   console.log('Validation errors:', validation.errors)
 *   throw createError({ statusCode: 400, data: { errors: validation.errors } })
 * }
 * ```
 */
function validateRequest(event: any): { valid: boolean; errors: string[] } {
  const errors: string[] = []
  const url = event.node.req.url || ''
  const method = getMethod(event)
  const contentLength = parseInt(getHeader(event, 'content-length') || '0')
  
  // Check URL length
  if (url.length > SECURITY_CONFIG.validation.maxUrlLength) {
    errors.push('url_too_long')
  }
  
  // Check method
  if (!SECURITY_CONFIG.validation.allowedMethods.includes(method)) {
    errors.push('invalid_method')
  }
  
  // Check content length
  if (contentLength > SECURITY_CONFIG.validation.maxBodySize) {
    errors.push('body_too_large')
  }
  
  // Check headers size
  const headers = event.node.req.headers
  const headerSize = JSON.stringify(headers).length
  if (headerSize > SECURITY_CONFIG.validation.maxHeaderSize) {
    errors.push('headers_too_large')
  }
  
  return {
    valid: errors.length === 0,
    errors
  }
}

/**
 * Enhanced security middleware - main request processing pipeline
 * 
 * Processes all incoming requests through security layers:
 * 1. Security headers application
 * 2. Request structure validation
 * 3. Suspicious pattern detection
 * 4. Rate limiting enforcement
 * 5. CSRF protection for state-changing operations
 * 6. Security context creation
 * 7. Audit logging for critical endpoints
 * 
 * Each layer can block the request if security violations are detected.
 * Successful requests receive security context for downstream handlers.
 * 
 * @param event - H3 event object
 * @returns Promise that resolves when security processing is complete
 * 
 * @example
 * ```typescript
 * // Middleware runs automatically for all requests
 * // Access security context in route handlers:
 * export default defineEventHandler((event) => {
 *   const { requestId, fingerprint, rateLimit } = event.context.security
 *   
 *   // Use security context for additional validation
 *   if (rateLimit.remaining < 10) {
 *     console.warn('Rate limit nearly exceeded')
 *   }
 * })
 * ```
 */
export default defineEventHandler(async (event) => {
  const startTime = Date.now()
  const requestId = randomUUID()
  
  try {
    // Set request ID for tracking
    event.context.requestId = requestId
    
    // 1. Set comprehensive security headers
    // Includes HSTS, CSP, XSS protection, frame options, etc.
    setHeaders(event, SECURITY_CONFIG.headers)
    
    // Remove sensitive headers that could reveal server information
    removeResponseHeader(event, 'X-Powered-By')
    removeResponseHeader(event, 'Server')
    
    // 2. Validate request structure against security policies
    const validation = validateRequest(event)
    if (!validation.valid) {
      await auditLogger.logEvent('security.suspicious_activity', {
        event,
        metadata: { 
          activity: 'invalid_request',
          errors: validation.errors 
        }
      })
      
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid request structure',
        data: { errors: validation.errors }
      })
    }
    
    // 3. Check for suspicious patterns and attack attempts
    const suspiciousCheck = detectSuspiciousPatterns(event)
    if (suspiciousCheck.suspicious) {
      await auditLogger.logEvent('security.suspicious_activity', {
        event,
        metadata: { 
          activity: 'malicious_patterns',
          patterns: suspiciousCheck.patterns 
        }
      })
      
      // Block obvious attacks
      if (suspiciousCheck.patterns.includes('malicious_url')) {
        const ip = getRequestIP(event) || 'unknown'
        blockedIPCache.set(ip, true)
        
        throw createError({
          statusCode: 403,
          statusMessage: 'Suspicious activity detected'
        })
      }
    }
    
    // 4. Apply rate limiting with IP blocking
    const rateLimitResult = checkRateLimit(event)
    if (!rateLimitResult.allowed) {
      setHeaders(event, {
        'X-RateLimit-Limit': '0',
        'X-RateLimit-Remaining': '0',
        'X-RateLimit-Reset': new Date(rateLimitResult.resetTime).toISOString()
      })
      
      throw createError({
        statusCode: 429,
        statusMessage: 'Rate limit exceeded'
      })
    }
    
    // Set rate limit headers
    setHeaders(event, {
      'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
      'X-RateLimit-Reset': new Date(rateLimitResult.resetTime).toISOString()
    })
    
    // 5. CSRF protection for state-changing operations
    // Only applies to POST, PUT, DELETE, PATCH requests
    const method = getMethod(event)
    const url = event.node.req.url || ''
    
    if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
      const isExcluded = SECURITY_CONFIG.csrf.excludePaths.some(path => url.includes(path))
      
      if (!isExcluded) {
        const sessionId = getCookie(event, 'omni-session')
        const csrfToken = getHeader(event, SECURITY_CONFIG.csrf.headerName) || 
                         getQuery(event).csrf as string
        
        if (!sessionId || !csrfToken || !validateCSRFToken(sessionId, csrfToken)) {
          await auditLogger.logEvent('security.csrf_detected', {
            event,
            metadata: { 
              sessionId: sessionId ? 'present' : 'missing',
              csrfToken: csrfToken ? 'present' : 'missing'
            }
          })
          
          throw createError({
            statusCode: 403,
            statusMessage: 'CSRF token validation failed'
          })
        }
      }
    }
    
    // 6. Generate CSRF token for authenticated users on GET requests
    const sessionId = getCookie(event, 'omni-session')
    if (sessionId && method === 'GET') {
      const csrfToken = generateCSRFToken()
      csrfTokenCache.set(sessionId, csrfToken)
      
      setHeaders(event, {
        'X-CSRF-Token': csrfToken
      })
    }
    
    // 7. Add comprehensive security context to event
    // Available to all downstream handlers
    event.context.security = {
      requestId,
      fingerprint: getClientFingerprint(event),
      rateLimit: {
        remaining: rateLimitResult.remaining,
        resetTime: rateLimitResult.resetTime
      },
      suspicious: suspiciousCheck.suspicious,
      patterns: suspiciousCheck.patterns,
      startTime
    }
    
    // Log successful security validation for high-value endpoints
    // Helps with compliance and security monitoring
    if (url.includes('/api/auth/') || url.includes('/api/data/')) {
      await auditLogger.logEvent('security.request_validated', {
        event,
        metadata: {
          method,
          url: url.split('?')[0],
          fingerprint: getClientFingerprint(event),
          responseTime: Date.now() - startTime
        }
      })
    }
    
  } catch (error) {
    // Log security errors for debugging and monitoring
    console.error('🔒 [Security] Middleware error:', error)
    
    // Re-throw the error to be handled by the global error handler
    // This ensures proper error responses while maintaining security
    throw error
  }
})