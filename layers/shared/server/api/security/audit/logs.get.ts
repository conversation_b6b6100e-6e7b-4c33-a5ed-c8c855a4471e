import { validateSession } from '~/server/utils/session'
import { auditLogger } from '~/server/utils/audit-logger'
import { checkPermission } from '~/server/utils/rbac'
import { <PERSON>rror<PERSON>and<PERSON> } from '../../../../utils/error-handler'

/**
 * Get audit logs for workspace (requires audit.read permission)
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔍 [Audit API] Getting audit logs')
    
    // Validate session
    const validation = await validateSession(event)
    if (!validation.isValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Session validation failed'
      })
    }
    
    const { user, workspace } = validation
    
    // Check if user has permission to view audit logs
    const permissionResult = await checkPermission(
      user?.id!, 
      workspace?.id!, 
      'analytics.read'
    )
    
    if (!permissionResult.hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Insufficient permissions to view audit logs'
      })
    }
    
    // Get query parameters
    const query = getQuery(event)
    const {
      eventType,
      severity,
      userId,
      startDate,
      endDate,
      limit = 50,
      offset = 0,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = query
    
    // Build filter object
    const filters: any = {
      workspaceId: workspace?.id,
      limit: Math.min(parseInt(limit as string), 100), // Max 100 results
      offset: parseInt(offset as string),
      sortBy: sortBy as string,
      sortOrder: sortOrder as 'asc' | 'desc'
    }
    
    if (eventType) filters.eventType = eventType
    if (severity) filters.severity = severity
    if (userId) filters.userId = userId
    if (startDate) filters.startDate = new Date(startDate as string)
    if (endDate) filters.endDate = new Date(endDate as string)
    
    // Query audit logs
    const auditResults = await auditLogger.queryLogs(filters)
    
    // Log audit access
    await auditLogger.logEvent('audit.logs_accessed', {
      event,
      userId: user?.id,
      workspaceId: workspace?.id,
      resource: 'audit_logs',
      metadata: {
        filters,
        resultCount: auditResults.entries.length,
        hasMore: auditResults.hasMore,
        accessedBy: user?.id
      }
    })
    
    return {
      success: true,
      data: {
        entries: auditResults.entries.map(entry => ({
          id: entry.id,
          eventType: entry.eventType,
          severity: entry.severity,
          category: entry.category,
          action: entry.action,
          resource: entry.resource,
          resourceId: entry.resourceId,
          userId: entry.userId,
          profileId: entry.profileId,
          ipAddress: entry.ipAddress,
          userAgent: entry.userAgent,
          createdAt: entry.createdAt,
          metadata: entry.metadata,
          changeDetails: entry.changeDetails
        })),
        pagination: {
          total: auditResults.total,
          limit: filters.limit,
          offset: filters.offset,
          hasMore: auditResults.hasMore,
          nextOffset: auditResults.nextOffset
        }
      }
    }
    
  } catch (error) {
    console.error('🔍 [Audit API] Error getting audit logs:', error)
    
    // Log error
    await auditLogger.logEvent('system.error', {
      event,
      metadata: {
        endpoint: '/api/security/audit/logs',
        error: error.message
      }
    })
    
    throw ErrorHandler.handle(error, 'Failed to get audit logs')
  }
})