import { validateSession } from '~/server/utils/session'
import { auditLogger } from '~/server/utils/audit-logger'
import { checkPermission } from '~/server/utils/rbac'
import { <PERSON>rror<PERSON>and<PERSON> } from '../../../../utils/error-handler'

/**
 * Get audit statistics for workspace (requires analytics.read permission)
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔍 [Audit API] Getting audit statistics')
    
    // Validate session
    const validation = await validateSession(event)
    if (!validation.isValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Session validation failed'
      })
    }
    
    const { user, workspace } = validation
    
    // Check if user has permission to view audit statistics
    const permissionResult = await checkPermission(
      user?.id!, 
      workspace?.id!, 
      'analytics.read'
    )
    
    if (!permissionResult.hasPermission) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Insufficient permissions to view audit statistics'
      })
    }
    
    // Get query parameters
    const query = getQuery(event)
    const days = parseInt(query.days as string) || 30
    
    // Validate days parameter
    if (days < 1 || days > 365) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Days parameter must be between 1 and 365'
      })
    }
    
    // Get audit statistics
    const stats = await auditLogger.getStatistics(workspace?.id!, days)
    
    // Calculate additional metrics
    const now = new Date()
    const startDate = new Date(now.getTime() - (days * 24 * 60 * 60 * 1000))
    
    // Calculate average events per day
    const avgEventsPerDay = stats.totalEvents / days
    
    // Calculate security risk score based on event types
    let securityRiskScore = 0
    const riskWeights = {
      'auth.login_failed': 2,
      'auth.access_denied': 3,
      'security.suspicious_activity': 5,
      'security.rate_limit_exceeded': 4,
      'security.csrf_detected': 4,
      'security.ip_blocked': 3,
      'auth.privilege_escalation': 8,
      'security.sql_injection_attempt': 10,
      'security.xss_attempt': 6
    }
    
    Object.entries(stats.eventsByType).forEach(([eventType, count]) => {
      const weight = riskWeights[eventType as keyof typeof riskWeights] || 1
      securityRiskScore += count * weight
    })
    
    // Normalize risk score to 0-100 scale
    const normalizedRiskScore = Math.min(100, Math.round(securityRiskScore / Math.max(1, stats.totalEvents) * 10))
    
    // Determine risk level
    let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low'
    if (normalizedRiskScore >= 75) riskLevel = 'critical'
    else if (normalizedRiskScore >= 50) riskLevel = 'high'
    else if (normalizedRiskScore >= 25) riskLevel = 'medium'
    
    // Log statistics access
    await auditLogger.logEvent('audit.stats_accessed', {
      event,
      userId: user?.id,
      workspaceId: workspace?.id,
      resource: 'audit_statistics',
      metadata: {
        days,
        totalEvents: stats.totalEvents,
        riskScore: normalizedRiskScore,
        riskLevel,
        accessedBy: user?.id
      }
    })
    
    return {
      success: true,
      data: {
        period: {
          days,
          startDate: startDate.toISOString(),
          endDate: now.toISOString()
        },
        overview: {
          totalEvents: stats.totalEvents,
          avgEventsPerDay: Math.round(avgEventsPerDay * 100) / 100,
          securityRiskScore: normalizedRiskScore,
          riskLevel
        },
        eventsByType: stats.eventsByType,
        eventsBySeverity: stats.eventsBySeverity,
        topUsers: stats.topUsers,
        riskTrends: stats.riskTrends,
        securityMetrics: {
          failedLogins: stats.eventsByType['auth.login_failed'] || 0,
          accessDenials: stats.eventsByType['auth.access_denied'] || 0,
          suspiciousActivity: stats.eventsByType['security.suspicious_activity'] || 0,
          rateLimitExceeded: stats.eventsByType['security.rate_limit_exceeded'] || 0,
          csrfAttempts: stats.eventsByType['security.csrf_detected'] || 0,
          privilegeEscalations: stats.eventsByType['auth.privilege_escalation'] || 0
        },
        recommendations: generateSecurityRecommendations(stats, normalizedRiskScore)
      }
    }
    
  } catch (error) {
    console.error('🔍 [Audit API] Error getting audit statistics:', error)
    
    // Log error
    await auditLogger.logEvent('system.error', {
      event,
      metadata: {
        endpoint: '/api/security/audit/stats',
        error: error.message
      }
    })
    
    throw ErrorHandler.handle(error, 'Failed to get audit statistics')
  }
})

/**
 * Generate security recommendations based on audit data
 */
function generateSecurityRecommendations(stats: any, riskScore: number): Array<{
  type: 'warning' | 'error' | 'info';
  title: string;
  description: string;
  action: string;
}> {
  const recommendations = []
  
  // High number of failed logins
  const failedLogins = stats.eventsByType['auth.login_failed'] || 0
  if (failedLogins > 50) {
    recommendations.push({
      type: 'warning' as const,
      title: 'High Number of Failed Logins',
      description: `${failedLogins} failed login attempts detected. This may indicate brute force attacks.`,
      action: 'Consider implementing account lockout policies or IP blocking.'
    })
  }
  
  // Suspicious activity
  const suspiciousActivity = stats.eventsByType['security.suspicious_activity'] || 0
  if (suspiciousActivity > 10) {
    recommendations.push({
      type: 'error' as const,
      title: 'Suspicious Activity Detected',
      description: `${suspiciousActivity} suspicious activity events detected.`,
      action: 'Review security logs and consider strengthening security measures.'
    })
  }
  
  // Rate limiting exceeded
  const rateLimitExceeded = stats.eventsByType['security.rate_limit_exceeded'] || 0
  if (rateLimitExceeded > 20) {
    recommendations.push({
      type: 'warning' as const,
      title: 'Frequent Rate Limit Violations',
      description: `${rateLimitExceeded} rate limit violations detected.`,
      action: 'Consider adjusting rate limits or implementing progressive delays.'
    })
  }
  
  // CSRF attempts
  const csrfAttempts = stats.eventsByType['security.csrf_detected'] || 0
  if (csrfAttempts > 5) {
    recommendations.push({
      type: 'error' as const,
      title: 'CSRF Attacks Detected',
      description: `${csrfAttempts} CSRF attack attempts detected.`,
      action: 'Ensure all forms have proper CSRF protection and tokens are validated.'
    })
  }
  
  // Privilege escalation attempts
  const privilegeEscalations = stats.eventsByType['auth.privilege_escalation'] || 0
  if (privilegeEscalations > 0) {
    recommendations.push({
      type: 'error' as const,
      title: 'Privilege Escalation Attempts',
      description: `${privilegeEscalations} privilege escalation attempts detected.`,
      action: 'Review role assignments and permission grants immediately.'
    })
  }
  
  // High risk score
  if (riskScore >= 75) {
    recommendations.push({
      type: 'error' as const,
      title: 'High Security Risk Score',
      description: 'Your workspace has a high security risk score based on recent activity.',
      action: 'Implement additional security measures and review access controls.'
    })
  }
  
  // Low activity (possible reconnaissance)
  if (stats.totalEvents < 10) {
    recommendations.push({
      type: 'info' as const,
      title: 'Low Activity Detected',
      description: 'Very low activity detected, which may indicate reconnaissance.',
      action: 'Monitor for unusual patterns and ensure logging is properly configured.'
    })
  }
  
  return recommendations
}