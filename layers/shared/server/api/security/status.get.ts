import { validateSession } from '~/server/utils/session'
import { auditLogger } from '~/server/utils/audit-logger'
import { <PERSON><PERSON>r<PERSON><PERSON><PERSON> } from '../../../utils/error-handler'

/**
 * Get security status for current user
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔒 [Security API] Getting security status')
    
    // Validate session
    const validation = await validateSession(event)
    if (!validation.isValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Session validation failed'
      })
    }
    
    const { user, workspace, profile } = validation
    
    // Get CSRF token from security context
    const csrfToken = event.context.security?.csrfToken || 
                     getHeader(event, 'X-CSRF-Token') || 
                     null
    
    // Get rate limit status
    const rateLimitRemaining = event.context.security?.rateLimit?.remaining || 100
    const rateLimitReset = event.context.security?.rateLimit?.resetTime || null
    
    // Determine risk level based on security context
    let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low'
    const threats: any[] = []
    
    // Check for suspicious activity
    if (event.context.security?.suspicious) {
      riskLevel = 'medium'
      threats.push({
        id: 'suspicious_activity',
        type: 'suspicious',
        severity: 'medium',
        message: 'Suspicious activity detected',
        timestamp: new Date(),
        resolved: false,
        patterns: event.context.security.patterns
      })
    }
    
    // Check rate limit status
    if (rateLimitRemaining < 10) {
      riskLevel = 'high'
      threats.push({
        id: 'rate_limit_warning',
        type: 'rateLimit',
        severity: 'medium',
        message: 'Rate limit threshold approaching',
        timestamp: new Date(),
        resolved: false,
        remaining: rateLimitRemaining
      })
    }
    
    // Check session validity
    const sessionValid = Boolean(validation.session && user && workspace && profile)
    if (!sessionValid) {
      riskLevel = 'critical'
      threats.push({
        id: 'session_invalid',
        type: 'session',
        severity: 'critical',
        message: 'Session validation failed',
        timestamp: new Date(),
        resolved: false
      })
    }
    
    // Check CSRF token
    if (!csrfToken) {
      threats.push({
        id: 'csrf_missing',
        type: 'csrf',
        severity: 'low',
        message: 'CSRF token not present',
        timestamp: new Date(),
        resolved: false
      })
    }
    
    const securityStatus = {
      isSecure: riskLevel === 'low',
      riskLevel,
      threats,
      sessionValid,
      csrfToken,
      rateLimitRemaining,
      rateLimitReset: rateLimitReset ? new Date(rateLimitReset).toISOString() : null,
      lastCheck: new Date().toISOString(),
      user: {
        id: user?.id,
        role: profile?.role,
        permissions: profile?.permissions || []
      },
      workspace: {
        id: workspace?.id,
        name: workspace?.name
      }
    }
    
    // Log security status check
    await auditLogger.logEvent('security.status_checked', {
      event,
      userId: user?.id,
      workspaceId: workspace?.id,
      metadata: {
        riskLevel,
        threatCount: threats.length,
        sessionValid,
        csrfPresent: Boolean(csrfToken),
        rateLimitRemaining
      }
    })
    
    return securityStatus
    
  } catch (error) {
    console.error('🔒 [Security API] Error getting security status:', error)
    
    // Log error
    await auditLogger.logEvent('system.error', {
      event,
      metadata: {
        endpoint: '/api/security/status',
        error: error.message
      }
    })
    
    throw ErrorHandler.handle(error, 'Failed to get security status')
  }
})