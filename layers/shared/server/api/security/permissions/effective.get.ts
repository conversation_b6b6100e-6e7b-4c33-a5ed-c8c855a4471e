import { validateSession } from '~/server/utils/session'
import { getUserProfile, getPermissionsForRole } from '~/server/utils/rbac'
import { auditLogger } from '~/server/utils/audit-logger'
import { <PERSON>rror<PERSON>and<PERSON> } from '../../../../utils/error-handler'

/**
 * Get user's effective permissions (role + custom permissions)
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔒 [Security API] Getting effective permissions')
    
    // Validate session
    const validation = await validateSession(event)
    if (!validation.isValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Session validation failed'
      })
    }
    
    const { user, workspace } = validation
    
    // Get workspace ID from query
    const query = getQuery(event)
    const targetWorkspaceId = query.workspaceId as string || workspace?.id
    
    if (!targetWorkspaceId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Workspace ID is required'
      })
    }
    
    // Get user profile
    const profile = await getUserProfile(user?.id!, targetWorkspaceId)
    if (!profile) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User profile not found in workspace'
      })
    }
    
    // Get role permissions
    const rolePermissions = getPermissionsForRole(profile.role)
    
    // Get custom permissions
    const customPermissions = profile.permissions || []
    
    // Combine all permissions
    const allPermissions = [...new Set([...rolePermissions, ...customPermissions])]
    
    // Log permissions access
    await auditLogger.logEvent('auth.permissions_accessed', {
      event,
      userId: user?.id,
      workspaceId: targetWorkspaceId,
      resource: 'permissions',
      metadata: {
        role: profile.role,
        rolePermissionCount: rolePermissions.length,
        customPermissionCount: customPermissions.length,
        totalPermissions: allPermissions.length,
        permissions: allPermissions
      }
    })
    
    return {
      role: profile.role,
      permissions: allPermissions,
      rolePermissions,
      customPermissions,
      profile: {
        id: profile.id,
        displayName: profile.displayName,
        avatar: profile.avatar,
        lastActiveAt: profile.lastActiveAt
      },
      workspace: {
        id: targetWorkspaceId,
        name: workspace?.name
      }
    }
    
  } catch (error) {
    console.error('🔒 [Security API] Error getting effective permissions:', error)
    
    // Log error
    await auditLogger.logEvent('system.error', {
      event,
      metadata: {
        endpoint: '/api/security/permissions/effective',
        error: error.message
      }
    })
    
    throw ErrorHandler.handle(error, 'Failed to get effective permissions')
  }
})