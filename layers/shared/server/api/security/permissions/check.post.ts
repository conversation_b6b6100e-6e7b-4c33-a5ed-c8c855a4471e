import { validateSession } from '~/server/utils/session'
import { checkPermission } from '~/server/utils/rbac'
import { auditLogger } from '~/server/utils/audit-logger'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../../utils/error-handler'
import type { Permission } from '~/types/auth'

/**
 * Check specific permission for current user
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔒 [Security API] Checking permission')
    
    // Validate session
    const validation = await validateSession(event)
    if (!validation.isValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Session validation failed'
      })
    }
    
    const { user, workspace } = validation
    
    // Get request body
    const body = await readBody(event)
    const { permission, workspaceId } = body
    
    // Validate inputs
    if (!permission) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Permission parameter is required'
      })
    }
    
    const targetWorkspaceId = workspaceId || workspace?.id
    if (!targetWorkspaceId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Workspace ID is required'
      })
    }
    
    // Check permission
    const permissionResult = await checkPermission(
      user?.id!, 
      targetWorkspaceId, 
      permission as Permission
    )
    
    // Log permission check
    await auditLogger.logEvent('auth.permission_checked', {
      event,
      userId: user?.id,
      workspaceId: targetWorkspaceId,
      resource: 'permission',
      resourceId: permission,
      metadata: {
        permission,
        hasPermission: permissionResult.hasPermission,
        reason: permissionResult.reason,
        role: permissionResult.context.role,
        inherited: permissionResult.context.inherited
      }
    })
    
    // If permission denied, log as audit event
    if (!permissionResult.hasPermission) {
      await auditLogger.logEvent('auth.access_denied', {
        event,
        userId: user?.id,
        workspaceId: targetWorkspaceId,
        resource: 'permission',
        resourceId: permission,
        metadata: {
          permission,
          reason: permissionResult.reason,
          role: permissionResult.context.role
        }
      })
    }
    
    return {
      hasPermission: permissionResult.hasPermission,
      reason: permissionResult.reason,
      role: permissionResult.context.role,
      inherited: permissionResult.context.inherited,
      permissions: permissionResult.context.permissions,
      customPermissions: permissionResult.context.permissions.filter(p => 
        !['users.read', 'workspace.read', 'projects.read', 'data.read'].includes(p)
      )
    }
    
  } catch (error) {
    console.error('🔒 [Security API] Error checking permission:', error)
    
    // Log error
    await auditLogger.logEvent('system.error', {
      event,
      metadata: {
        endpoint: '/api/security/permissions/check',
        error: error.message
      }
    })
    
    throw ErrorHandler.handle(error, 'Failed to check permission')
  }
})