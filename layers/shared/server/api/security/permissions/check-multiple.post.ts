import { validateSession } from '~/server/utils/session'
import { checkPermissions } from '~/server/utils/rbac'
import { auditLogger } from '~/server/utils/audit-logger'
import { <PERSON>rror<PERSON>and<PERSON> } from '../../../../utils/error-handler'
import type { Permission } from '~/types/auth'

/**
 * Check multiple permissions for current user
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔒 [Security API] Checking multiple permissions')
    
    // Validate session
    const validation = await validateSession(event)
    if (!validation.isValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Session validation failed'
      })
    }
    
    const { user, workspace } = validation
    
    // Get request body
    const body = await readBody(event)
    const { permissions, workspaceId } = body
    
    // Validate inputs
    if (!permissions || !Array.isArray(permissions)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Permissions parameter must be an array'
      })
    }
    
    const targetWorkspaceId = workspaceId || workspace?.id
    if (!targetWorkspaceId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Workspace ID is required'
      })
    }
    
    // Check permissions
    const permissionResults = await checkPermissions(
      user?.id!, 
      targetWorkspaceId, 
      permissions as Permission[]
    )
    
    // Build response
    const response: Record<Permission, any> = {}
    const deniedPermissions: string[] = []
    
    for (const [permission, result] of Object.entries(permissionResults)) {
      response[permission as Permission] = {
        hasPermission: result.hasPermission,
        reason: result.reason,
        role: result.context.role,
        inherited: result.context.inherited,
        customPermissions: result.context.permissions.filter(p => 
          !['users.read', 'workspace.read', 'projects.read', 'data.read'].includes(p)
        )
      }
      
      if (!result.hasPermission) {
        deniedPermissions.push(permission)
      }
    }
    
    // Log permission check
    await auditLogger.logEvent('auth.permissions_checked', {
      event,
      userId: user?.id,
      workspaceId: targetWorkspaceId,
      resource: 'permissions',
      metadata: {
        permissions: permissions,
        checkedCount: permissions.length,
        deniedCount: deniedPermissions.length,
        deniedPermissions,
        role: Object.values(permissionResults)[0]?.context?.role || 'unknown'
      }
    })
    
    // If any permissions denied, log as audit event
    if (deniedPermissions.length > 0) {
      await auditLogger.logEvent('auth.access_denied', {
        event,
        userId: user?.id,
        workspaceId: targetWorkspaceId,
        resource: 'permissions',
        metadata: {
          deniedPermissions,
          totalChecked: permissions.length,
          role: Object.values(permissionResults)[0]?.context?.role || 'unknown'
        }
      })
    }
    
    return response
    
  } catch (error) {
    console.error('🔒 [Security API] Error checking multiple permissions:', error)
    
    // Log error
    await auditLogger.logEvent('system.error', {
      event,
      metadata: {
        endpoint: '/api/security/permissions/check-multiple',
        error: error.message
      }
    })
    
    throw ErrorHandler.handle(error, 'Failed to check multiple permissions')
  }
})