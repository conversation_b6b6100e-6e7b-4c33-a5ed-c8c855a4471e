import { validateSession } from '~/server/utils/session'
import { auditLogger } from '~/server/utils/audit-logger'
import { <PERSON><PERSON>r<PERSON><PERSON><PERSON> } from '../../../../utils/error-handler'

/**
 * Report security event from frontend
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔒 [Security API] Reporting security event')
    
    // Validate session
    const validation = await validateSession(event)
    if (!validation.isValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Session validation failed'
      })
    }
    
    const { user, workspace } = validation
    
    // Get request body
    const body = await readBody(event)
    const { type, message, metadata, timestamp } = body
    
    // Validate inputs
    if (!type || !message) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Type and message are required'
      })
    }
    
    // Map frontend event types to audit event types
    const eventTypeMapping: Record<string, string> = {
      'permission_denied': 'auth.access_denied',
      'session_expired': 'auth.session_expired',
      'csrf_failed': 'security.csrf_detected',
      'rate_limit': 'security.rate_limit_exceeded',
      'suspicious_activity': 'security.suspicious_activity',
      'security_violation': 'security.suspicious_activity',
      'unauthorized_access': 'auth.access_denied',
      'data_breach_attempt': 'security.suspicious_activity',
      'malicious_input': 'security.suspicious_activity'
    }
    
    const auditEventType = eventTypeMapping[type] || 'security.suspicious_activity'
    
    // Log the security event
    await auditLogger.logEvent(auditEventType as any, {
      event,
      userId: user?.id,
      workspaceId: workspace?.id,
      resource: 'security_event',
      metadata: {
        frontendEventType: type,
        message,
        timestamp: timestamp || new Date().toISOString(),
        reportedBy: 'frontend',
        userAgent: getHeader(event, 'user-agent'),
        ipAddress: getClientIP(event),
        ...metadata
      }
    })
    
    // Additional logging for critical events
    if (['permission_denied', 'session_expired', 'csrf_failed', 'suspicious_activity'].includes(type)) {
      console.warn('🚨 [Security] Critical event reported:', {
        type,
        message,
        userId: user?.id,
        workspaceId: workspace?.id,
        timestamp: timestamp || new Date().toISOString()
      })
    }
    
    return {
      success: true,
      eventId: crypto.randomUUID(),
      timestamp: new Date().toISOString(),
      message: 'Security event reported successfully'
    }
    
  } catch (error) {
    console.error('🔒 [Security API] Error reporting security event:', error)
    
    // Log error
    await auditLogger.logEvent('system.error', {
      event,
      metadata: {
        endpoint: '/api/security/events/report',
        error: error.message
      }
    })
    
    throw ErrorHandler.handle(error, 'Failed to report security event')
  }
})

/**
 * Helper function to get client IP
 */
function getClientIP(event: any): string {
  const xForwardedFor = getHeader(event, 'x-forwarded-for')
  const xRealIp = getHeader(event, 'x-real-ip')
  
  if (xForwardedFor) {
    return xForwardedFor.split(',')[0].trim()
  }
  
  if (xRealIp) {
    return xRealIp
  }
  
  return event.node.req.socket.remoteAddress || 'unknown'
}