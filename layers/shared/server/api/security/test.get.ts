import { validateSession } from '~/server/utils/session'
import { checkPermission, getWorkspace<PERSON>em<PERSON> } from '~/server/utils/rbac'
import { auditLogger } from '~/server/utils/audit-logger'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../utils/error-handler'

/**
 * Test endpoint for security features
 * This endpoint tests various security components
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔒 [Security Test] Running security tests')
    
    // Test 1: Session validation
    const validation = await validateSession(event)
    if (!validation.isValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Session validation failed'
      })
    }
    
    const { user, workspace, profile } = validation
    
    // Test 2: Permission checking
    const permissionTests = [
      'users.read',
      'projects.create',
      'workspace.update',
      'data.export',
      'ai.use'
    ]
    
    const permissionResults = {}
    for (const permission of permissionTests) {
      const result = await checkPermission(user?.id!, workspace?.id!, permission as any)
      permissionResults[permission] = {
        hasPermission: result.hasPermission,
        reason: result.reason,
        role: result.context.role
      }
    }
    
    // Test 3: Audit logging
    await auditLogger.logEvent('security.test_executed', {
      event,
      userId: user?.id,
      workspaceId: workspace?.id,
      metadata: {
        testType: 'security_features',
        permissionTests: permissionTests.length,
        userRole: profile?.role,
        timestamp: new Date().toISOString()
      }
    })
    
    // Test 4: Get workspace members (permission required)
    let workspaceMembers = []
    const canViewMembers = await checkPermission(user?.id!, workspace?.id!, 'users.read')
    if (canViewMembers.hasPermission) {
      workspaceMembers = await getWorkspaceMembers(workspace?.id!)
    }
    
    // Test 5: Security context validation
    const securityContext = {
      requestId: event.context.requestId,
      hasSecurityContext: Boolean(event.context.security),
      fingerprint: event.context.security?.fingerprint,
      rateLimit: event.context.security?.rateLimit,
      suspicious: event.context.security?.suspicious,
      patterns: event.context.security?.patterns
    }
    
    // Test 6: Rate limiting status
    const rateLimitHeaders = {
      remaining: getHeader(event, 'X-RateLimit-Remaining'),
      reset: getHeader(event, 'X-RateLimit-Reset')
    }
    
    const testResults = {
      success: true,
      timestamp: new Date().toISOString(),
      tests: {
        sessionValidation: {
          passed: validation.isValid,
          user: user ? {
            id: user.id,
            email: user.email,
            displayName: user.displayName
          } : null,
          workspace: workspace ? {
            id: workspace.id,
            name: workspace.name,
            userRole: workspace.userRole
          } : null,
          profile: profile ? {
            id: profile.id,
            role: profile.role,
            permissions: profile.permissions
          } : null
        },
        
        permissionChecking: {
          passed: true,
          results: permissionResults,
          summary: {
            totalChecked: permissionTests.length,
            allowed: Object.values(permissionResults).filter((r: any) => r.hasPermission).length,
            denied: Object.values(permissionResults).filter((r: any) => !r.hasPermission).length
          }
        },
        
        auditLogging: {
          passed: true,
          message: 'Security test event logged successfully'
        },
        
        workspaceAccess: {
          passed: canViewMembers.hasPermission,
          memberCount: workspaceMembers.length,
          canViewMembers: canViewMembers.hasPermission,
          reason: canViewMembers.reason
        },
        
        securityMiddleware: {
          passed: Boolean(event.context.security),
          context: securityContext,
          rateLimitHeaders
        }
      },
      
      recommendations: generateTestRecommendations(permissionResults, securityContext)
    }
    
    return testResults
    
  } catch (error) {
    console.error('🔒 [Security Test] Test failed:', error)
    
    // Log test failure
    await auditLogger.logEvent('security.test_failed', {
      event,
      metadata: {
        error: error.message,
        statusCode: error.statusCode,
        timestamp: new Date().toISOString()
      }
    })
    
    return {
      success: false,
      error: error.message,
      timestamp: new Date().toISOString(),
      tests: {
        sessionValidation: { passed: false, error: error.message },
        permissionChecking: { passed: false, error: error.message },
        auditLogging: { passed: false, error: error.message },
        workspaceAccess: { passed: false, error: error.message },
        securityMiddleware: { passed: false, error: error.message }
      }
    }
  }
})

/**
 * Generate test recommendations based on results
 */
function generateTestRecommendations(permissionResults: any, securityContext: any): Array<{
  type: 'info' | 'warning' | 'error'
  message: string
  action?: string
}> {
  const recommendations = []
  
  // Check permission results
  const deniedPermissions = Object.entries(permissionResults)
    .filter(([_, result]: [string, any]) => !result.hasPermission)
    .map(([permission, _]) => permission)
  
  if (deniedPermissions.length === Object.keys(permissionResults).length) {
    recommendations.push({
      type: 'warning' as const,
      message: 'User has very limited permissions',
      action: 'Consider granting additional permissions if needed'
    })
  }
  
  // Check security context
  if (!securityContext.hasSecurityContext) {
    recommendations.push({
      type: 'error' as const,
      message: 'Security middleware not properly configured',
      action: 'Check security middleware setup'
    })
  }
  
  if (securityContext.suspicious) {
    recommendations.push({
      type: 'warning' as const,
      message: 'Suspicious activity detected',
      action: 'Review security patterns and user behavior'
    })
  }
  
  // Check rate limiting
  if (securityContext.rateLimit?.remaining < 10) {
    recommendations.push({
      type: 'warning' as const,
      message: 'Rate limit threshold approaching',
      action: 'Monitor API usage and consider rate limit adjustments'
    })
  }
  
  // Success message
  if (recommendations.length === 0) {
    recommendations.push({
      type: 'info' as const,
      message: 'All security tests passed successfully',
      action: 'Continue monitoring security metrics'
    })
  }
  
  return recommendations
}