import { validateSession } from '~/server/utils/session'
import { auditLogger } from '~/server/utils/audit-logger'
import { <PERSON>rror<PERSON>and<PERSON> } from '../../../../utils/error-handler'
import { randomUUID } from 'crypto'

/**
 * Refresh CSRF token for authenticated user
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔒 [Security API] Refreshing CSRF token')
    
    // Validate session
    const validation = await validateSession(event)
    if (!validation.isValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Session validation failed'
      })
    }
    
    const { user, workspace } = validation
    
    // Generate new CSRF token
    const csrfToken = randomUUID() + '-' + Date.now()
    
    // Set CSRF token in response header
    setHeader(event, 'X-CSRF-Token', csrfToken)
    
    // Log CSRF token refresh
    await auditLogger.logEvent('auth.token_refreshed', {
      event,
      userId: user?.id,
      workspaceId: workspace?.id,
      resource: 'csrf_token',
      metadata: {
        tokenType: 'csrf',
        refreshedAt: new Date().toISOString(),
        userAgent: getHeader(event, 'user-agent'),
        ipAddress: getClientIP(event)
      }
    })
    
    return {
      success: true,
      csrfToken,
      expiresAt: new Date(Date.now() + 60 * 60 * 1000).toISOString(), // 1 hour
      message: 'CSRF token refreshed successfully'
    }
    
  } catch (error) {
    console.error('🔒 [Security API] Error refreshing CSRF token:', error)
    
    // Log error
    await auditLogger.logEvent('system.error', {
      event,
      metadata: {
        endpoint: '/api/security/csrf/refresh',
        error: error.message
      }
    })
    
    throw ErrorHandler.handle(error, 'Failed to refresh CSRF token')
  }
})

/**
 * Helper function to get client IP
 */
function getClientIP(event: any): string {
  const xForwardedFor = getHeader(event, 'x-forwarded-for')
  const xRealIp = getHeader(event, 'x-real-ip')
  
  if (xForwardedFor) {
    return xForwardedFor.split(',')[0].trim()
  }
  
  if (xRealIp) {
    return xRealIp
  }
  
  return event.node.req.socket.remoteAddress || 'unknown'
}