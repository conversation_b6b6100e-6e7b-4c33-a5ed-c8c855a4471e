import { getEmbeddingAnalytics } from '../../utils/embeddings'
import { getVectorSearchAnalytics } from '../../utils/vector-search'
import { sessionMiddleware, requirePermission } from '../../middleware/session'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../utils/error-handler'

/**
 * Get AI and vector search analytics
 * GET /api/ai/analytics
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [AI Analytics API] Getting analytics')
    
    // Validate session first
    await sessionMiddleware(event)
    
    // Check permissions
    await requirePermission('ai.analytics')(event)
    
    const query = getQuery(event)
    
    // Parse date range if provided
    let dateRange: { start: string; end: string } | undefined
    if (query.startDate && query.endDate) {
      dateRange = {
        start: query.startDate as string,
        end: query.endDate as string
      }
    }
    
    // Get embedding analytics
    const embeddingAnalytics = await getEmbeddingAnalytics(event, dateRange)
    
    // Get search analytics
    const searchAnalytics = await getVectorSearchAnalytics(event.context.workspace?.id || '')
    
    console.log('🔥 [AI Analytics API] Analytics retrieved successfully')
    
    return {
      success: true,
      data: {
        embeddings: embeddingAnalytics,
        search: {
          totalSearches: searchAnalytics.length,
          recentSearches: searchAnalytics.slice(-10),
          averageExecutionTime: searchAnalytics.length > 0 
            ? searchAnalytics.reduce((sum, s) => sum + s.executionTime, 0) / searchAnalytics.length 
            : 0,
          searchTypes: searchAnalytics.reduce((acc, s) => {
            acc[s.queryType] = (acc[s.queryType] || 0) + 1
            return acc
          }, {} as Record<string, number>)
        },
        workspace: event.context.workspace?.name,
        generatedAt: new Date().toISOString()
      },
      message: 'Analytics retrieved successfully'
    }
    
  } catch (error) {
    console.error('🔥 [AI Analytics API] Error getting analytics:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to get analytics')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})