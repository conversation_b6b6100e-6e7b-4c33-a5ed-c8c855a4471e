import { refreshEmbeddings } from '../../utils/embeddings'
import { sessionMiddleware, requirePermission } from '../../middleware/session'
import { <PERSON><PERSON>r<PERSON>and<PERSON> } from '../../../utils/error-handler'

/**
 * Refresh embeddings for changed content
 * POST /api/ai/refresh
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [AI Refresh API] Refreshing embeddings')
    
    // Validate session first
    await sessionMiddleware(event)
    
    // Check permissions
    await requirePermission('ai.manage')(event)
    
    const body = await readBody(event)
    
    if (!body.embeddingIds || !Array.isArray(body.embeddingIds)) {
      throw new Error('Embedding IDs array is required')
    }
    
    // Validate batch size
    if (body.embeddingIds.length > 50) {
      throw new Error('Cannot refresh more than 50 embeddings at once')
    }
    
    // Refresh embeddings
    const results = await refreshEmbeddings(event, body.embeddingIds)
    
    console.log(`🔥 [AI Refresh API] Refreshed ${results.length} embeddings`)
    
    return {
      success: true,
      data: {
        refreshed: results.length,
        requested: body.embeddingIds.length,
        embeddings: results.map(embedding => ({
          id: embedding.id,
          content: embedding.content.substring(0, 100) + '...',
          dimensions: embedding.embedding.length,
          metadata: embedding.metadata
        }))
      },
      message: 'Embeddings refreshed successfully',
      workspace: event.context.workspace?.name
    }
    
  } catch (error) {
    console.error('🔥 [AI Refresh API] Error refreshing embeddings:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to refresh embeddings')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})