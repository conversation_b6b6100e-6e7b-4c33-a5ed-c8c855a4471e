import { findSimilarDocuments } from '../../utils/vector-search'
import { sessionMiddleware, requirePermission } from '../../middleware/session'
import { <PERSON>rror<PERSON>andler } from '../../../utils/error-handler'

/**
 * Find similar documents
 * POST /api/ai/similar
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [AI Similar API] Finding similar documents')
    
    // Validate session first
    await sessionMiddleware(event)
    
    // Check permissions
    await requirePermission('ai.search')(event)
    
    const body = await readBody(event)
    
    if (!body.documentId) {
      throw new Error('Document ID is required')
    }
    
    // Find similar documents
    const results = await findSimilarDocuments(event, body.documentId, {
      limit: body.limit || 10,
      threshold: body.threshold || 0.5,
      excludeIds: body.excludeIds || [],
      sameTypeOnly: body.sameTypeOnly === true
    })
    
    console.log(`🔥 [AI Similar API] Found ${results.length} similar documents`)
    
    return {
      success: true,
      data: {
        documentId: body.documentId,
        results,
        total: results.length,
        options: {
          limit: body.limit || 10,
          threshold: body.threshold || 0.5,
          sameTypeOnly: body.sameTypeOnly === true
        }
      },
      message: 'Similar documents found successfully',
      workspace: event.context.workspace?.name
    }
    
  } catch (error) {
    console.error('🔥 [AI Similar API] Error finding similar documents:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to find similar documents')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})