import { generateEmbedding } from '../../utils/vertex-ai'
import { performVectorSearch } from '../../utils/vector-search'
import { generateAndStoreEmbedding } from '../../utils/embeddings'
import { sessionMiddleware } from '../../middleware/session'
import { <PERSON>rror<PERSON>and<PERSON> } from '../../../utils/error-handler'

/**
 * Test vector search functionality
 * GET /api/ai/test
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [AI Test API] Testing vector search functionality')
    
    // Validate session first
    await sessionMiddleware(event)
    
    const testResults: any = {
      timestamp: new Date().toISOString(),
      tests: []
    }
    
    // Test 1: VertexAI Embedding Generation
    try {
      console.log('🔥 [AI Test] Testing VertexAI embedding generation')
      const testText = 'This is a test document for vector search functionality'
      const embeddingResult = await generateEmbedding(testText, {
        task_type: 'RETRIEVAL_DOCUMENT'
      })
      
      testResults.tests.push({
        name: 'VertexAI Embedding Generation',
        status: 'passed',
        details: {
          textLength: testText.length,
          embeddingDimensions: embeddingResult.embedding.length,
          tokenCount: embeddingResult.statistics.token_count,
          truncated: embeddingResult.statistics.truncated
        }
      })
      
      console.log('✅ [AI Test] VertexAI embedding generation test passed')
    } catch (error) {
      testResults.tests.push({
        name: 'VertexAI Embedding Generation',
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      console.error('❌ [AI Test] VertexAI embedding generation test failed:', error)
    }
    
    // Test 2: Store Embedding
    try {
      console.log('🔥 [AI Test] Testing embedding storage')
      const testText = 'This is a test document for embedding storage'
      const storedEmbedding = await generateAndStoreEmbedding(event, testText, {
        type: 'document',
        source: 'test',
        tags: ['test', 'vector-search']
      })
      
      testResults.tests.push({
        name: 'Embedding Storage',
        status: 'passed',
        details: {
          embeddingId: storedEmbedding.id,
          dimensions: storedEmbedding.embedding.length,
          contentLength: storedEmbedding.content.length,
          metadata: storedEmbedding.metadata
        }
      })
      
      console.log('✅ [AI Test] Embedding storage test passed')
    } catch (error) {
      testResults.tests.push({
        name: 'Embedding Storage',
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      console.error('❌ [AI Test] Embedding storage test failed:', error)
    }
    
    // Test 3: Vector Search
    try {
      console.log('🔥 [AI Test] Testing vector search')
      const searchResults = await performVectorSearch(event, {
        query: 'test document',
        options: {
          limit: 5,
          threshold: 0.1,
          distance: 'cosine'
        }
      })
      
      testResults.tests.push({
        name: 'Vector Search',
        status: 'passed',
        details: {
          queryText: 'test document',
          resultsCount: searchResults.length,
          topScore: searchResults.length > 0 ? searchResults[0].score : 0,
          executionTime: 'measured in search function'
        }
      })
      
      console.log('✅ [AI Test] Vector search test passed')
    } catch (error) {
      testResults.tests.push({
        name: 'Vector Search',
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      console.error('❌ [AI Test] Vector search test failed:', error)
    }
    
    // Test 4: Configuration Validation
    try {
      console.log('🔥 [AI Test] Testing configuration')
      const config = useRuntimeConfig()
      
      const configTests = {
        hasGoogleCloudProject: !!config.googleCloud?.projectId,
        hasGoogleCloudLocation: !!config.googleCloud?.location,
        hasFirebaseConfig: !!config.firebase?.projectId,
        hasWorkspaceContext: !!event.context.workspace,
        hasUserContext: !!event.context.user
      }
      
      const configValid = Object.values(configTests).every(Boolean)
      
      testResults.tests.push({
        name: 'Configuration Validation',
        status: configValid ? 'passed' : 'warning',
        details: configTests
      })
      
      console.log('✅ [AI Test] Configuration validation test completed')
    } catch (error) {
      testResults.tests.push({
        name: 'Configuration Validation',
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error'
      })
      console.error('❌ [AI Test] Configuration validation test failed:', error)
    }
    
    // Calculate overall status
    const passedTests = testResults.tests.filter(t => t.status === 'passed').length
    const totalTests = testResults.tests.length
    const overallStatus = passedTests === totalTests ? 'all_passed' : 
                         passedTests > 0 ? 'partial_passed' : 'all_failed'
    
    console.log(`🔥 [AI Test] Test suite completed: ${passedTests}/${totalTests} tests passed`)
    
    return {
      success: true,
      data: {
        ...testResults,
        summary: {
          total: totalTests,
          passed: passedTests,
          failed: totalTests - passedTests,
          status: overallStatus
        }
      },
      message: `Vector search test suite completed: ${passedTests}/${totalTests} tests passed`,
      workspace: event.context.workspace?.name
    }
    
  } catch (error) {
    console.error('🔥 [AI Test API] Test suite failed:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Vector search test suite failed')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})