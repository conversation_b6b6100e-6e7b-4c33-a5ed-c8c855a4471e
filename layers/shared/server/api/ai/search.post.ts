import { performVectorSearch, performHybridSearch } from '../../utils/vector-search'
import { sessionMiddleware, requirePermission } from '../../middleware/session'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../utils/error-handler'

/**
 * Perform semantic search across collections
 * POST /api/ai/search
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [AI Search API] Performing semantic search')
    
    // Validate session first
    await sessionMiddleware(event)
    
    // Check permissions
    await requirePermission('ai.search')(event)
    
    const body = await readBody(event)
    
    if (!body.query) {
      throw new Error('Query is required')
    }
    
    // Validate query length
    if (body.query.length > 1000) {
      throw new Error('Query exceeds maximum length of 1,000 characters')
    }
    
    // Determine search type
    const searchType = body.type || 'vector'
    
    let results
    
    if (searchType === 'hybrid' && body.textQuery) {
      // Perform hybrid search
      results = await performHybridSearch(event, {
        query: body.query,
        textQuery: body.textQuery,
        filters: {
          type: body.filters?.type,
          tags: body.filters?.tags,
          source: body.filters?.source,
          dateRange: body.filters?.dateRange
        },
        options: {
          limit: body.limit || 20,
          threshold: body.threshold || 0.3,
          distance: body.distance || 'cosine',
          includeMetadata: body.includeMetadata !== false,
          rerank: body.rerank === true
        },
        weights: body.weights || { vector: 0.7, text: 0.3 },
        textFields: body.textFields || ['content', 'metadata.source']
      })
    } else {
      // Perform vector search
      results = await performVectorSearch(event, {
        query: body.query,
        embedding: body.embedding,
        filters: {
          type: body.filters?.type,
          tags: body.filters?.tags,
          source: body.filters?.source,
          dateRange: body.filters?.dateRange
        },
        options: {
          limit: body.limit || 20,
          threshold: body.threshold || 0.3,
          distance: body.distance || 'cosine',
          includeMetadata: body.includeMetadata !== false,
          rerank: body.rerank === true
        }
      })
    }
    
    console.log(`🔥 [AI Search API] Search completed: ${results.length} results`)
    
    return {
      success: true,
      data: {
        results,
        query: body.query,
        searchType,
        total: results.length,
        filters: body.filters,
        options: {
          limit: body.limit || 20,
          threshold: body.threshold || 0.3,
          distance: body.distance || 'cosine'
        }
      },
      message: 'Search completed successfully',
      workspace: event.context.workspace?.name
    }
    
  } catch (error) {
    console.error('🔥 [AI Search API] Error performing search:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to perform search')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})