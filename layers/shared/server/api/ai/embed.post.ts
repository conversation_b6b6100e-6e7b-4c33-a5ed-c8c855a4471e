import { generateAndStoreEmbedding } from '../../utils/embeddings'
import { sessionMiddleware, requirePermission } from '../../middleware/session'
import { <PERSON>rrorHandler } from '../../../utils/error-handler'

/**
 * Generate and store text embedding
 * POST /api/ai/embed
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [AI Embed API] Generating text embedding')
    
    // Validate session first
    await sessionMiddleware(event)
    
    // Check permissions
    await requirePermission('ai.embed')(event)
    
    const body = await readBody(event)
    
    if (!body.content) {
      throw new Error('Content is required')
    }
    
    // Validate content length
    if (body.content.length > 50000) {
      throw new Error('Content exceeds maximum length of 50,000 characters')
    }
    
    // Generate and store embedding
    const embedding = await generateAndStoreEmbedding(
      event,
      body.content,
      {
        type: body.type || 'document',
        source: body.source,
        tags: body.tags || [],
        language: body.language || 'en'
      }
    )
    
    console.log('🔥 [AI Embed API] Embedding generated successfully:', embedding.id)
    
    return {
      success: true,
      data: {
        id: embedding.id,
        content: embedding.content,
        dimensions: embedding.embedding.length,
        metadata: embedding.metadata
      },
      message: 'Embedding generated successfully',
      workspace: event.context.workspace?.name
    }
    
  } catch (error) {
    console.error('🔥 [AI Embed API] Error generating embedding:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to generate embedding')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})