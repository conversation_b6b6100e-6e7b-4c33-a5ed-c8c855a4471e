import { batchGenerateAndStoreEmbeddings } from '../../utils/embeddings'
import { sessionMiddleware, requirePermission } from '../../middleware/session'
import { ErrorHandler } from '../../../utils/error-handler'

/**
 * Bulk embedding generation
 * POST /api/ai/batch-embed
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [AI Batch Embed API] Generating batch embeddings')
    
    // Validate session first
    await sessionMiddleware(event)
    
    // Check permissions
    await requirePermission('ai.embed')(event)
    
    const body = await readBody(event)
    
    if (!body.items || !Array.isArray(body.items)) {
      throw new Error('Items array is required')
    }
    
    // Validate batch size
    if (body.items.length > 100) {
      throw new Error('Batch size cannot exceed 100 items')
    }
    
    // Validate each item
    for (const item of body.items) {
      if (!item.content) {
        throw new Error('Content is required for each item')
      }
      
      if (item.content.length > 50000) {
        throw new Error('Content exceeds maximum length of 50,000 characters')
      }
    }
    
    // Prepare items for batch processing
    const items = body.items.map((item: any) => ({
      content: item.content,
      metadata: {
        type: item.type || 'document',
        source: item.source,
        tags: item.tags || [],
        language: item.language || 'en'
      }
    }))
    
    // Generate and store embeddings in batch
    const results = await batchGenerateAndStoreEmbeddings(event, items)
    
    console.log(`🔥 [AI Batch Embed API] Batch embedding completed: ${results.length} embeddings generated`)
    
    return {
      success: true,
      data: {
        results: results.map(embedding => ({
          id: embedding.id,
          content: embedding.content,
          dimensions: embedding.embedding.length,
          metadata: embedding.metadata
        })),
        total: results.length,
        batchSize: body.items.length
      },
      message: 'Batch embeddings generated successfully',
      workspace: event.context.workspace?.name
    }
    
  } catch (error) {
    console.error('🔥 [AI Batch Embed API] Error generating batch embeddings:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to generate batch embeddings')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})