import { checkVertexAIHealth, getEmbeddingModelInfo } from '../../utils/vertex-ai'
import { sessionMiddleware } from '../../middleware/session'
import { ErrorHandler } from '../../../utils/error-handler'

/**
 * Check AI services health
 * GET /api/ai/health
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [AI Health API] Checking AI services health')
    
    // Validate session first (optional for health check)
    try {
      await sessionMiddleware(event)
    } catch (error) {
      // Continue without session for basic health check
    }
    
    // Check VertexAI health
    const vertexHealth = await checkVertexAIHealth()
    
    // Get model info
    const modelInfo = await getEmbeddingModelInfo()
    
    // Overall health status
    const overallHealth = vertexHealth.status === 'healthy' ? 'healthy' : 'unhealthy'
    
    console.log(`🔥 [AI Health API] Health check completed: ${overallHealth}`)
    
    return {
      success: true,
      data: {
        status: overallHealth,
        services: {
          vertexai: vertexHealth
        },
        model: modelInfo,
        timestamp: new Date().toISOString(),
        version: '1.0'
      },
      message: `AI services are ${overallHealth}`
    }
    
  } catch (error) {
    console.error('🔥 [AI Health API] Health check failed:', error)
    
    const appError = ErrorHandler.handle(error, 'AI health check failed')
    
    return {
      success: false,
      data: {
        status: 'unhealthy',
        error: appError.message,
        timestamp: new Date().toISOString()
      },
      message: 'AI services health check failed'
    }
  }
})