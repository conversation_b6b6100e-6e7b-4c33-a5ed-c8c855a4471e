import { storeEmbedding } from '../../utils/embeddings'
import { sessionMiddleware, requirePermission } from '../../middleware/session'
import { ErrorHandler } from '../../../utils/error-handler'

/**
 * Store new embedding
 * POST /api/embeddings
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [Embeddings API] Storing new embedding')
    
    // Validate session first
    await sessionMiddleware(event)
    
    // Check permissions
    await requirePermission('data.create')(event)
    
    const body = await readBody(event)
    
    if (!body.content || !body.embedding) {
      throw new Error('Content and embedding are required')
    }
    
    // Store embedding
    const embedding = await storeEmbedding(
      event,
      body.content,
      body.embedding,
      body.metadata || {}
    )
    
    console.log('🔥 [Embeddings API] Embedding stored successfully:', embedding.id)
    
    return {
      success: true,
      data: embedding,
      message: 'Embedding stored successfully'
    }
    
  } catch (error) {
    console.error('🔥 [Embeddings API] Error storing embedding:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to store embedding')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})