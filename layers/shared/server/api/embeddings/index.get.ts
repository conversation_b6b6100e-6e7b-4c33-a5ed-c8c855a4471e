import { getWorkspaceEmbeddings } from '../../utils/embeddings'
import { sessionMiddleware } from '../../middleware/session'
import { <PERSON>rror<PERSON>and<PERSON> } from '../../../utils/error-handler'

/**
 * Get workspace embeddings
 * GET /api/embeddings
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [Embeddings API] Getting workspace embeddings')
    
    // Validate session first
    await sessionMiddleware(event)
    
    // Parse query parameters
    const query = getQuery(event)
    
    const options = {
      type: query.type as string | undefined,
      userId: query.userId as string | undefined,
      tags: query.tags ? (Array.isArray(query.tags) ? query.tags : [query.tags]) as string[] : undefined,
      limit: query.limit ? parseInt(query.limit as string) : undefined
    }
    
    // Get embeddings
    const embeddings = await getWorkspaceEmbeddings(event, options)
    
    console.log(`🔥 [Embeddings API] Retrieved ${embeddings.length} embeddings`)
    
    return {
      success: true,
      data: embeddings,
      message: 'Embeddings retrieved successfully',
      workspace: event.context.workspace?.name
    }
    
  } catch (error) {
    console.error('🔥 [Embeddings API] Error getting embeddings:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to get embeddings')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})