import { readBody } from 'h3'
import { getFirestore, Timestamp } from 'firebase-admin/firestore'
import { initializeFirebaseAdmin } from '../../utils/firebase-admin'
import { sessionMiddleware } from '../../middleware/session'
import { batchProcessEmbeddings } from '../../utils/embeddings'
import { ErrorHandler } from '../../../utils/error-handler'
import type { 
  BatchOperation, 
  BatchResult, 
  BaseEntity, 
  CrudOptions 
} from '~/types/data-api'

/**
 * Batch operations for multiple documents with transaction support
 * POST /api/data/batch
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [Data Batch API] Processing batch request')
    
    // Validate session and apply middleware
    await sessionMiddleware(event)
    
    // Parse request body
    const body = await readBody(event)
    const { operations, options = {} } = body
    
    if (!operations || !Array.isArray(operations)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        data: { error: 'Operations array is required' }
      })
    }
    
    if (operations.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        data: { error: 'At least one operation is required' }
      })
    }
    
    if (operations.length > 100) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        data: { error: 'Maximum 100 operations allowed per batch' }
      })
    }
    
    console.log(`🔥 [Data Batch API] Processing ${operations.length} operations`)
    
    // Initialize Firebase Admin
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    // Process operations
    let result: BatchResult
    
    if (options.useTransaction) {
      // Use Firestore transaction for atomic operations
      console.log('🔥 [Data Batch API] Using transaction mode')
      result = await processOperationsInTransaction(db, operations, event, options)
    } else {
      // Process operations individually
      console.log('🔥 [Data Batch API] Using individual operations mode')
      result = await processOperationsIndividually(db, operations, event, options)
    }
    
    // Handle bulk embedding generation if requested
    if (options.generateEmbeddings) {
      console.log('🔥 [Data Batch API] Generating bulk embeddings')
      await processBulkEmbeddings(event, result, options)
    }
    
    console.log('🔥 [Data Batch API] Batch operations completed')
    
    return {
      success: true,
      data: result,
      message: `Batch operations completed: ${result.successCount} successful, ${result.errorCount} failed`
    }
    
  } catch (error) {
    console.error('🔥 [Data Batch API] Error:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to process batch operations')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})

/**
 * Process operations in a Firestore transaction
 */
async function processOperationsInTransaction(
  db: any,
  operations: BatchOperation[],
  event: any,
  options: any
): Promise<BatchResult> {
  const result: BatchResult = {
    success: true,
    operations: [],
    totalCount: operations.length,
    successCount: 0,
    errorCount: 0
  }
  
  try {
    await db.runTransaction(async (transaction: any) => {
      const now = Timestamp.now()
      
      // First, validate all operations and collect document references
      const validatedOperations = await validateOperations(db, operations, event, transaction)
      
      // Execute all operations within the transaction
      for (let i = 0; i < validatedOperations.length; i++) {
        const operation = validatedOperations[i]
        const originalOp = operations[i]
        
        try {
          switch (operation.operation) {
            case 'create':
              await executeCreateOperation(transaction, operation, event, now)
              break
            case 'update':
              await executeUpdateOperation(transaction, operation, event, now)
              break
            case 'delete':
              await executeDeleteOperation(transaction, operation, event, now)
              break
            default:
              throw new Error(`Unsupported operation: ${operation.operation}`)
          }
          
          result.operations.push({
            operation: operation.operation,
            id: operation.id,
            success: true
          })
          result.successCount++
          
        } catch (error) {
          result.operations.push({
            operation: operation.operation,
            id: operation.id,
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          })
          result.errorCount++
          
          // In transaction mode, any error fails the entire batch
          throw error
        }
      }
    })
    
  } catch (error) {
    console.error('🔥 [Data Batch API] Transaction failed:', error)
    result.success = false
    throw error
  }
  
  return result
}

/**
 * Process operations individually (non-atomic)
 */
async function processOperationsIndividually(
  db: any,
  operations: BatchOperation[],
  event: any,
  options: any
): Promise<BatchResult> {
  const result: BatchResult = {
    success: true,
    operations: [],
    totalCount: operations.length,
    successCount: 0,
    errorCount: 0
  }
  
  const now = Timestamp.now()
  
  // Process operations in chunks to avoid overwhelming Firestore
  const chunkSize = options.chunkSize || 10
  
  for (let i = 0; i < operations.length; i += chunkSize) {
    const chunk = operations.slice(i, i + chunkSize)
    
    const chunkPromises = chunk.map(async (operation, index) => {
      const globalIndex = i + index
      
      try {
        let operationResult: any
        
        switch (operation.operation) {
          case 'create':
            operationResult = await executeCreateOperationIndividual(db, operation, event, now)
            break
          case 'update':
            operationResult = await executeUpdateOperationIndividual(db, operation, event, now)
            break
          case 'delete':
            operationResult = await executeDeleteOperationIndividual(db, operation, event, now)
            break
          default:
            throw new Error(`Unsupported operation: ${operation.operation}`)
        }
        
        result.operations[globalIndex] = {
          operation: operation.operation,
          id: operationResult.id,
          success: true
        }
        result.successCount++
        
      } catch (error) {
        result.operations[globalIndex] = {
          operation: operation.operation,
          id: operation.id,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        }
        result.errorCount++
      }
    })
    
    await Promise.all(chunkPromises)
  }
  
  return result
}

/**
 * Validate operations before execution
 */
async function validateOperations(
  db: any,
  operations: BatchOperation[],
  event: any,
  transaction?: any
): Promise<BatchOperation[]> {
  const validatedOps: BatchOperation[] = []
  
  for (const operation of operations) {
    // Validate collection name
    if (!operation.collection) {
      throw new Error('Collection name is required for all operations')
    }
    
    // Validate operation type
    if (!['create', 'update', 'delete'].includes(operation.operation)) {
      throw new Error(`Invalid operation type: ${operation.operation}`)
    }
    
    // Validate required fields based on operation type
    if (operation.operation === 'create' && !operation.data) {
      throw new Error('Data is required for create operations')
    }
    
    if (operation.operation === 'update' && (!operation.id || !operation.data)) {
      throw new Error('ID and data are required for update operations')
    }
    
    if (operation.operation === 'delete' && !operation.id) {
      throw new Error('ID is required for delete operations')
    }
    
    // For update and delete operations, verify document exists and workspace access
    if (operation.operation !== 'create') {
      const docRef = db.collection(operation.collection).doc(operation.id)
      const doc = transaction ? await transaction.get(docRef) : await docRef.get()
      
      if (!doc.exists) {
        throw new Error(`Document ${operation.id} not found in ${operation.collection}`)
      }
      
      const docData = doc.data()
      if (!docData.workspaceIds || !docData.workspaceIds.includes(event.context.workspace!.id)) {
        throw new Error(`Access denied to document ${operation.id}`)
      }
    }
    
    validatedOps.push(operation)
  }
  
  return validatedOps
}

/**
 * Execute create operation in transaction
 */
async function executeCreateOperation(
  transaction: any,
  operation: BatchOperation,
  event: any,
  now: Timestamp
): Promise<void> {
  const docId = operation.id || generateDocumentId()
  const docRef = transaction.collection(operation.collection).doc(docId)
  
  const documentData: BaseEntity = {
    id: docId,
    ...operation.data,
    workspaceIds: [event.context.workspace!.id],
    profileIds: [event.context.profile!.id],
    createdAt: now,
    updatedAt: now,
    deletedAt: null,
    createdBy: event.context.profile!.id,
    updatedBy: event.context.profile!.id
  }
  
  transaction.set(docRef, documentData)
}

/**
 * Execute update operation in transaction
 */
async function executeUpdateOperation(
  transaction: any,
  operation: BatchOperation,
  event: any,
  now: Timestamp
): Promise<void> {
  const docRef = transaction.collection(operation.collection).doc(operation.id)
  
  const updateData = {
    ...operation.data,
    updatedAt: now,
    updatedBy: event.context.profile!.id
  }
  
  // Remove fields that shouldn't be updated
  delete updateData.id
  delete updateData.createdAt
  delete updateData.createdBy
  delete updateData.workspaceIds
  delete updateData.profileIds
  
  transaction.update(docRef, updateData)
}

/**
 * Execute delete operation in transaction
 */
async function executeDeleteOperation(
  transaction: any,
  operation: BatchOperation,
  event: any,
  now: Timestamp
): Promise<void> {
  const docRef = transaction.collection(operation.collection).doc(operation.id)
  
  if (operation.filters?.hardDelete) {
    transaction.delete(docRef)
  } else {
    transaction.update(docRef, {
      deletedAt: now,
      deletedBy: event.context.profile!.id,
      updatedAt: now,
      updatedBy: event.context.profile!.id
    })
  }
}

/**
 * Execute individual create operation
 */
async function executeCreateOperationIndividual(
  db: any,
  operation: BatchOperation,
  event: any,
  now: Timestamp
): Promise<{ id: string }> {
  const docId = operation.id || generateDocumentId()
  const docRef = db.collection(operation.collection).doc(docId)
  
  const documentData: BaseEntity = {
    id: docId,
    ...operation.data,
    workspaceIds: [event.context.workspace!.id],
    profileIds: [event.context.profile!.id],
    createdAt: now,
    updatedAt: now,
    deletedAt: null,
    createdBy: event.context.profile!.id,
    updatedBy: event.context.profile!.id
  }
  
  await docRef.set(documentData)
  return { id: docId }
}

/**
 * Execute individual update operation
 */
async function executeUpdateOperationIndividual(
  db: any,
  operation: BatchOperation,
  event: any,
  now: Timestamp
): Promise<{ id: string }> {
  const docRef = db.collection(operation.collection).doc(operation.id!)
  
  const updateData = {
    ...operation.data,
    updatedAt: now,
    updatedBy: event.context.profile!.id
  }
  
  // Remove fields that shouldn't be updated
  delete updateData.id
  delete updateData.createdAt
  delete updateData.createdBy
  delete updateData.workspaceIds
  delete updateData.profileIds
  
  await docRef.update(updateData)
  return { id: operation.id! }
}

/**
 * Execute individual delete operation
 */
async function executeDeleteOperationIndividual(
  db: any,
  operation: BatchOperation,
  event: any,
  now: Timestamp
): Promise<{ id: string }> {
  const docRef = db.collection(operation.collection).doc(operation.id!)
  
  if (operation.filters?.hardDelete) {
    await docRef.delete()
  } else {
    await docRef.update({
      deletedAt: now,
      deletedBy: event.context.profile!.id,
      updatedAt: now,
      updatedBy: event.context.profile!.id
    })
  }
  
  return { id: operation.id! }
}

/**
 * Process bulk embeddings for successful operations
 */
async function processBulkEmbeddings(
  event: any,
  result: BatchResult,
  options: any
): Promise<void> {
  try {
    const embeddingTasks: Array<{
      content: string
      embedding: number[]
      metadata: any
    }> = []
    
    // Collect embedding tasks from successful create operations
    for (const operation of result.operations) {
      if (operation.success && operation.operation === 'create') {
        // Generate embedding content (simplified)
        const content = `Document ${operation.id} created`
        const embedding = await generateEmbedding(content)
        
        embeddingTasks.push({
          content,
          embedding,
          metadata: {
            type: 'document',
            source: 'batch_operation',
            tags: ['batch', event.context.workspace!.id]
          }
        })
      }
    }
    
    if (embeddingTasks.length > 0) {
      await batchProcessEmbeddings(event, embeddingTasks)
      console.log(`🔥 [Data Batch API] Generated ${embeddingTasks.length} embeddings`)
    }
    
  } catch (error) {
    console.error('🔥 [Data Batch API] Error processing bulk embeddings:', error)
    // Don't fail the main operation
  }
}

/**
 * Generate embedding vector (placeholder implementation)
 */
async function generateEmbedding(content: string): Promise<number[]> {
  const dimension = 768
  const embedding = new Array(dimension).fill(0).map(() => Math.random() * 2 - 1)
  const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0))
  return embedding.map(val => val / magnitude)
}

/**
 * Generate document ID
 */
function generateDocumentId(): string {
  return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}