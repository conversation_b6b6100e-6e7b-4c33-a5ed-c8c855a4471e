import { readBody } from 'h3'
import { getFirestore, Timestamp } from 'firebase-admin/firestore'
import { initializeFirebaseAdmin } from '../../utils/firebase-admin'
import { sessionMiddleware } from '../../middleware/session'
import { storeEmbedding, updateEmbedding } from '../../utils/embeddings'
import { ErrorHandler } from '../../../utils/error-handler'
import type { BaseEntity, CrudOptions } from '~/types/data-api'

/**
 * Update existing document with workspace validation and embeddings
 * POST /api/data/update
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [Data Update API] Processing update request')
    
    // Validate session and apply middleware
    await sessionMiddleware(event)
    
    // Parse request body
    const body = await readBody(event)
    const { collection, id, data, options = {} } = body
    
    if (!collection) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        data: { error: 'Collection name is required' }
      })
    }
    
    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        data: { error: 'Document ID is required' }
      })
    }
    
    if (!data || typeof data !== 'object') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        data: { error: 'Data object is required' }
      })
    }
    
    console.log('🔥 [Data Update API] Updating document:', id, 'in collection:', collection)
    
    // Initialize Firebase Admin
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    // Get existing document
    const docRef = db.collection(collection).doc(id)
    const docSnapshot = await docRef.get()
    
    if (!docSnapshot.exists) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Not Found',
        data: { error: 'Document not found' }
      })
    }
    
    const existingData = docSnapshot.data() as BaseEntity
    
    // Validate workspace access
    if (!existingData.workspaceIds || !existingData.workspaceIds.includes(event.context.workspace!.id)) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Forbidden',
        data: { error: 'Access denied to this document' }
      })
    }
    
    // Prepare update data
    const now = Timestamp.now()
    const updateData: Partial<BaseEntity> = {
      ...data,
      updatedAt: now,
      updatedBy: event.context.profile!.id
    }
    
    // Remove fields that shouldn't be updated
    delete updateData.id
    delete updateData.createdAt
    delete updateData.createdBy
    delete updateData.workspaceIds
    delete updateData.profileIds
    
    // Check if embedding regeneration is needed
    const needsEmbeddingUpdate = options.embed && Array.isArray(options.embed)
    let embeddingUpdatePromise: Promise<void> | null = null
    
    if (needsEmbeddingUpdate) {
      console.log('🔥 [Data Update API] Regenerating embeddings for fields:', options.embed)
      
      embeddingUpdatePromise = (async () => {
        try {
          const mergedData = { ...existingData, ...updateData }
          const embeddingContent = generateEmbeddingContent(mergedData, options.embed)
          
          if (embeddingContent) {
            // Generate new embedding
            const embedding = await generateEmbedding(embeddingContent)
            
            // Update embedding in the embeddings collection
            if (existingData.embedding) {
              // Try to update existing embedding
              await updateEmbedding(event, `${collection}_${id}`, {
                content: embeddingContent,
                embedding,
                metadata: {
                  type: 'document',
                  source: collection,
                  tags: [collection, event.context.workspace!.id]
                }
              })
            } else {
              // Create new embedding
              await storeEmbedding(event, embeddingContent, embedding, {
                type: 'document',
                source: collection,
                tags: [collection, event.context.workspace!.id]
              })
            }
            
            // Update document with new embedding
            updateData.embedding = embedding
            updateData.embeddingModel = 'text-embedding-004'
            updateData.embeddingVersion = '1.0'
            
            console.log('🔥 [Data Update API] Embeddings regenerated')
          }
        } catch (embeddingError) {
          console.error('🔥 [Data Update API] Error updating embeddings:', embeddingError)
          // Continue without embeddings - don't fail the entire operation
        }
      })()
    }
    
    // Wait for embedding update if needed
    if (embeddingUpdatePromise) {
      await embeddingUpdatePromise
    }
    
    // Update document in Firestore
    await docRef.update(updateData)
    
    console.log('🔥 [Data Update API] Document updated:', id)
    
    // Create audit log entry
    await createAuditLog(event, {
      action: 'update',
      collection,
      documentId: id,
      originalData: existingData,
      updateData,
      timestamp: now
    })
    
    // Get updated document
    const updatedSnapshot = await docRef.get()
    const updatedData = updatedSnapshot.data() as BaseEntity
    
    console.log('🔥 [Data Update API] Document updated successfully')
    
    return {
      success: true,
      data: updatedData,
      message: 'Document updated successfully'
    }
    
  } catch (error) {
    console.error('🔥 [Data Update API] Error:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to update document')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})

/**
 * Generate embedding content from specified fields
 */
function generateEmbeddingContent(data: any, fields: string[]): string {
  const content: string[] = []
  
  for (const field of fields) {
    if (field in data && data[field]) {
      const value = data[field]
      if (typeof value === 'string') {
        content.push(value)
      } else if (typeof value === 'object') {
        content.push(JSON.stringify(value))
      } else {
        content.push(String(value))
      }
    }
  }
  
  return content.join(' ')
}

/**
 * Generate embedding vector (placeholder implementation)
 * In production, this would use VertexAI or another embedding service
 */
async function generateEmbedding(content: string): Promise<number[]> {
  // Placeholder implementation - generates a random vector
  // In production, you would use VertexAI:
  // const model = getGenerativeModel(vertexAI, { model: 'textembedding-gecko' })
  // const result = await model.embedContent(content)
  // return result.embedding.values
  
  const dimension = 768 // Standard embedding dimension
  const embedding = new Array(dimension).fill(0).map(() => Math.random() * 2 - 1)
  
  // Normalize the vector
  const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0))
  return embedding.map(val => val / magnitude)
}

/**
 * Create audit log entry
 */
async function createAuditLog(
  event: any,
  logData: {
    action: string
    collection: string
    documentId: string
    originalData: any
    updateData: any
    timestamp: Timestamp
  }
): Promise<void> {
  try {
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const auditEntry = {
      id: generateDocumentId(),
      action: logData.action,
      collection: logData.collection,
      documentId: logData.documentId,
      workspaceId: event.context.workspace!.id,
      userId: event.context.user!.id,
      profileId: event.context.profile!.id,
      timestamp: logData.timestamp,
      metadata: {
        userAgent: event.node.req.headers['user-agent'],
        ipAddress: getClientIP(event),
        originalData: logData.originalData,
        updateData: logData.updateData,
        changes: calculateChanges(logData.originalData, logData.updateData)
      },
      createdAt: logData.timestamp
    }
    
    await db.collection('audit_logs').doc(auditEntry.id).set(auditEntry)
    console.log('🔥 [Data Update API] Audit log created:', auditEntry.id)
    
  } catch (error) {
    console.error('🔥 [Data Update API] Error creating audit log:', error)
    // Don't fail the main operation if audit logging fails
  }
}

/**
 * Calculate changes between original and updated data
 */
function calculateChanges(original: any, updated: any): any {
  const changes: any = {}
  
  for (const [key, value] of Object.entries(updated)) {
    if (original[key] !== value) {
      changes[key] = {
        from: original[key],
        to: value
      }
    }
  }
  
  return changes
}

/**
 * Generate document ID
 */
function generateDocumentId(): string {
  return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Get client IP address
 */
function getClientIP(event: any): string {
  const xForwardedFor = event.node.req.headers['x-forwarded-for']
  const xRealIp = event.node.req.headers['x-real-ip']
  
  if (xForwardedFor) {
    return Array.isArray(xForwardedFor) ? xForwardedFor[0] : xForwardedFor.split(',')[0].trim()
  }
  
  if (xRealIp) {
    return Array.isArray(xRealIp) ? xRealIp[0] : xRealIp
  }
  
  return event.node.req.socket.remoteAddress || 'unknown'
}