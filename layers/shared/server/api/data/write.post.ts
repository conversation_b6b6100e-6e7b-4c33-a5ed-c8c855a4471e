import { readBody } from 'h3'
import { getFirestore, Timestamp } from 'firebase-admin/firestore'
import { initializeFirebaseAdmin } from '../../utils/firebase-admin'
import { sessionMiddleware } from '../../middleware/session'
import { storeEmbedding } from '../../utils/embeddings'
import { <PERSON>rror<PERSON>and<PERSON> } from '../../../utils/error-handler'
import type { BaseEntity, CrudOptions } from '~/types/data-api'

/**
 * Create new document with workspace scoping and embeddings
 * POST /api/data/write
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [Data Write API] Processing write request')
    
    // Validate session and apply middleware
    await sessionMiddleware(event)
    
    // Parse request body
    const body = await readBody(event)
    const { collection, data, options = {} } = body
    
    if (!collection) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        data: { error: 'Collection name is required' }
      })
    }
    
    if (!data || typeof data !== 'object') {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        data: { error: 'Data object is required' }
      })
    }
    
    console.log('🔥 [Data Write API] Writing to collection:', collection)
    
    // Initialize Firebase Admin
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    // Generate document ID
    const docId = generateDocumentId()
    
    // Prepare document data with multi-tenant fields
    const now = Timestamp.now()
    const documentData: BaseEntity = {
      id: docId,
      ...data,
      
      // Multi-tenant fields
      workspaceIds: [event.context.workspace!.id],
      profileIds: [event.context.profile!.id],
      
      // Audit fields
      createdAt: now,
      updatedAt: now,
      deletedAt: null,
      createdBy: event.context.profile!.id,
      updatedBy: event.context.profile!.id,
      
      // Vector search fields (will be populated if embedding is requested)
      embedding: undefined,
      embeddingModel: undefined,
      embeddingVersion: undefined
    }
    
    // Store document in Firestore
    await db.collection(collection).doc(docId).set(documentData)
    
    console.log('🔥 [Data Write API] Document created:', docId)
    
    // Generate embeddings if requested
    if (options.embed && Array.isArray(options.embed)) {
      console.log('🔥 [Data Write API] Generating embeddings for fields:', options.embed)
      
      try {
        const embeddingContent = generateEmbeddingContent(documentData, options.embed)
        
        if (embeddingContent) {
          // For now, we'll use a placeholder embedding
          // In production, you'd use VertexAI to generate real embeddings
          const embedding = await generateEmbedding(embeddingContent)
          
          // Store embedding
          await storeEmbedding(event, embeddingContent, embedding, {
            type: 'document',
            source: collection,
            tags: [collection, event.context.workspace!.id]
          })
          
          // Update document with embedding reference
          await db.collection(collection).doc(docId).update({
            embedding,
            embeddingModel: 'text-embedding-004',
            embeddingVersion: '1.0'
          })
          
          // Update local copy
          documentData.embedding = embedding
          documentData.embeddingModel = 'text-embedding-004'
          documentData.embeddingVersion = '1.0'
          
          console.log('🔥 [Data Write API] Embeddings generated and stored')
        }
      } catch (embeddingError) {
        console.error('🔥 [Data Write API] Error generating embeddings:', embeddingError)
        // Continue without embeddings - don't fail the entire operation
      }
    }
    
    // Create audit log entry
    await createAuditLog(event, {
      action: 'create',
      collection,
      documentId: docId,
      data: documentData,
      timestamp: now
    })
    
    // Remove undefined fields from response
    const cleanedData = removeUndefinedFields(documentData)
    
    console.log('🔥 [Data Write API] Document created successfully')
    
    return {
      success: true,
      data: cleanedData,
      message: 'Document created successfully'
    }
    
  } catch (error) {
    console.error('🔥 [Data Write API] Error:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to create document')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})

/**
 * Generate document ID
 */
function generateDocumentId(): string {
  return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Generate embedding content from specified fields
 */
function generateEmbeddingContent(data: any, fields: string[]): string {
  const content: string[] = []
  
  for (const field of fields) {
    if (field in data && data[field]) {
      const value = data[field]
      if (typeof value === 'string') {
        content.push(value)
      } else if (typeof value === 'object') {
        content.push(JSON.stringify(value))
      } else {
        content.push(String(value))
      }
    }
  }
  
  return content.join(' ')
}

/**
 * Generate embedding vector (placeholder implementation)
 * In production, this would use VertexAI or another embedding service
 */
async function generateEmbedding(content: string): Promise<number[]> {
  // Placeholder implementation - generates a random vector
  // In production, you would use VertexAI:
  // const model = getGenerativeModel(vertexAI, { model: 'textembedding-gecko' })
  // const result = await model.embedContent(content)
  // return result.embedding.values
  
  const dimension = 768 // Standard embedding dimension
  const embedding = new Array(dimension).fill(0).map(() => Math.random() * 2 - 1)
  
  // Normalize the vector
  const magnitude = Math.sqrt(embedding.reduce((sum, val) => sum + val * val, 0))
  return embedding.map(val => val / magnitude)
}

/**
 * Create audit log entry
 */
async function createAuditLog(
  event: any,
  logData: {
    action: string
    collection: string
    documentId: string
    data: any
    timestamp: Timestamp
  }
): Promise<void> {
  try {
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const auditEntry = {
      id: generateDocumentId(),
      action: logData.action,
      collection: logData.collection,
      documentId: logData.documentId,
      workspaceId: event.context.workspace!.id,
      userId: event.context.user!.id,
      profileId: event.context.profile!.id,
      timestamp: logData.timestamp,
      metadata: {
        userAgent: event.node.req.headers['user-agent'],
        ipAddress: getClientIP(event),
        dataSnapshot: logData.data
      },
      createdAt: logData.timestamp
    }
    
    await db.collection('audit_logs').doc(auditEntry.id).set(auditEntry)
    console.log('🔥 [Data Write API] Audit log created:', auditEntry.id)
    
  } catch (error) {
    console.error('🔥 [Data Write API] Error creating audit log:', error)
    // Don't fail the main operation if audit logging fails
  }
}

/**
 * Remove undefined fields from object
 */
function removeUndefinedFields(obj: any): any {
  const cleaned: any = {}
  
  for (const [key, value] of Object.entries(obj)) {
    if (value !== undefined) {
      if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Timestamp)) {
        cleaned[key] = removeUndefinedFields(value)
      } else {
        cleaned[key] = value
      }
    }
  }
  
  return cleaned
}

/**
 * Get client IP address
 */
function getClientIP(event: any): string {
  const xForwardedFor = event.node.req.headers['x-forwarded-for']
  const xRealIp = event.node.req.headers['x-real-ip']
  
  if (xForwardedFor) {
    return Array.isArray(xForwardedFor) ? xForwardedFor[0] : xForwardedFor.split(',')[0].trim()
  }
  
  if (xRealIp) {
    return Array.isArray(xRealIp) ? xRealIp[0] : xRealIp
  }
  
  return event.node.req.socket.remoteAddress || 'unknown'
}