import { readBody } from 'h3'
import { getFirestore, Timestamp } from 'firebase-admin/firestore'
import { initializeFirebaseAdmin } from '../../utils/firebase-admin'
import { sessionMiddleware } from '../../middleware/session'
import { deleteEmbedding } from '../../utils/embeddings'
import { ErrorHandler } from '../../../utils/error-handler'
import type { BaseEntity, CrudOptions } from '~/types/data-api'

/**
 * Delete document with workspace validation and audit trail
 * POST /api/data/delete
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [Data Delete API] Processing delete request')
    
    // Validate session and apply middleware
    await sessionMiddleware(event)
    
    // Parse request body
    const body = await readBody(event)
    const { collection, id, options = {} } = body
    
    if (!collection) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        data: { error: 'Collection name is required' }
      })
    }
    
    if (!id) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        data: { error: 'Document ID is required' }
      })
    }
    
    console.log('🔥 [Data Delete API] Deleting document:', id, 'from collection:', collection)
    
    // Initialize Firebase Admin
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    // Get existing document
    const docRef = db.collection(collection).doc(id)
    const docSnapshot = await docRef.get()
    
    if (!docSnapshot.exists) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Not Found',
        data: { error: 'Document not found' }
      })
    }
    
    const existingData = docSnapshot.data() as BaseEntity
    
    // Validate workspace access
    if (!existingData.workspaceIds || !existingData.workspaceIds.includes(event.context.workspace!.id)) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Forbidden',
        data: { error: 'Access denied to this document' }
      })
    }
    
    // Check if document is already soft-deleted
    if (existingData.deletedAt && !options.hardDelete) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        data: { error: 'Document is already deleted' }
      })
    }
    
    const now = Timestamp.now()
    let result: any
    
    if (options.hardDelete) {
      // Hard delete - permanently remove document
      console.log('🔥 [Data Delete API] Performing hard delete')
      
      // Delete associated embeddings
      await deleteAssociatedEmbeddings(event, collection, id)
      
      // Delete document from Firestore
      await docRef.delete()
      
      // Delete related cascade documents if specified
      if (options.cascade) {
        await deleteCascadeDocuments(event, collection, id, existingData)
      }
      
      result = { id, deleted: true, hardDelete: true }
      
    } else {
      // Soft delete - mark as deleted but keep in database
      console.log('🔥 [Data Delete API] Performing soft delete')
      
      const updateData: Partial<BaseEntity> = {
        deletedAt: now,
        deletedBy: event.context.profile!.id,
        updatedAt: now,
        updatedBy: event.context.profile!.id
      }
      
      // Update document with soft delete markers
      await docRef.update(updateData)
      
      result = { id, deleted: true, hardDelete: false }
    }
    
    console.log('🔥 [Data Delete API] Document deleted:', id)
    
    // Create audit log entry
    await createAuditLog(event, {
      action: options.hardDelete ? 'hard_delete' : 'soft_delete',
      collection,
      documentId: id,
      originalData: existingData,
      timestamp: now
    })
    
    console.log('🔥 [Data Delete API] Document deleted successfully')
    
    return {
      success: true,
      data: result,
      message: `Document ${options.hardDelete ? 'permanently deleted' : 'deleted'} successfully`
    }
    
  } catch (error) {
    console.error('🔥 [Data Delete API] Error:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to delete document')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})

/**
 * Delete associated embeddings
 */
async function deleteAssociatedEmbeddings(
  event: any,
  collection: string,
  documentId: string
): Promise<void> {
  try {
    console.log('🔥 [Data Delete API] Deleting associated embeddings')
    
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    // Find embeddings associated with this document
    const embeddingsQuery = db.collection('embeddings')
      .where('metadata.source', '==', collection)
      .where('metadata.workspaceId', '==', event.context.workspace!.id)
      .where('id', '==', `${collection}_${documentId}`)
    
    const embeddingsSnapshot = await embeddingsQuery.get()
    
    if (!embeddingsSnapshot.empty) {
      const deletePromises = embeddingsSnapshot.docs.map(doc => 
        deleteEmbedding(event, doc.id)
      )
      
      await Promise.all(deletePromises)
      console.log(`🔥 [Data Delete API] Deleted ${embeddingsSnapshot.size} associated embeddings`)
    }
    
  } catch (error) {
    console.error('🔥 [Data Delete API] Error deleting embeddings:', error)
    // Don't fail the main operation if embedding deletion fails
  }
}

/**
 * Delete cascade documents (related documents)
 */
async function deleteCascadeDocuments(
  event: any,
  collection: string,
  documentId: string,
  originalData: any
): Promise<void> {
  try {
    console.log('🔥 [Data Delete API] Deleting cascade documents')
    
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    // Define cascade relationships based on collection type
    const cascadeRules = getCascadeRules(collection)
    
    for (const rule of cascadeRules) {
      const cascadeQuery = db.collection(rule.collection)
        .where(rule.field, '==', documentId)
        .where('workspaceIds', 'array-contains', event.context.workspace!.id)
      
      const cascadeSnapshot = await cascadeQuery.get()
      
      if (!cascadeSnapshot.empty) {
        const deletePromises = cascadeSnapshot.docs.map(doc => {
          if (rule.action === 'delete') {
            return doc.ref.delete()
          } else if (rule.action === 'soft_delete') {
            return doc.ref.update({
              deletedAt: Timestamp.now(),
              deletedBy: event.context.profile!.id,
              updatedAt: Timestamp.now(),
              updatedBy: event.context.profile!.id
            })
          } else if (rule.action === 'nullify') {
            return doc.ref.update({
              [rule.field]: null,
              updatedAt: Timestamp.now(),
              updatedBy: event.context.profile!.id
            })
          }
        })
        
        await Promise.all(deletePromises)
        console.log(`🔥 [Data Delete API] Processed ${cascadeSnapshot.size} cascade documents in ${rule.collection}`)
      }
    }
    
  } catch (error) {
    console.error('🔥 [Data Delete API] Error with cascade deletion:', error)
    // Don't fail the main operation if cascade deletion fails
  }
}

/**
 * Get cascade rules for a collection
 */
function getCascadeRules(collection: string): Array<{
  collection: string
  field: string
  action: 'delete' | 'soft_delete' | 'nullify'
}> {
  // Define cascade rules based on your data model
  // This is a simplified example - in production, you'd have more sophisticated rules
  const rules: Record<string, any[]> = {
    'users': [
      { collection: 'profiles', field: 'userId', action: 'delete' },
      { collection: 'documents', field: 'ownerId', action: 'nullify' }
    ],
    'workspaces': [
      { collection: 'profiles', field: 'workspaceId', action: 'delete' },
      { collection: 'documents', field: 'workspaceId', action: 'delete' }
    ],
    'projects': [
      { collection: 'tasks', field: 'projectId', action: 'delete' },
      { collection: 'documents', field: 'projectId', action: 'nullify' }
    ]
  }
  
  return rules[collection] || []
}

/**
 * Create audit log entry
 */
async function createAuditLog(
  event: any,
  logData: {
    action: string
    collection: string
    documentId: string
    originalData: any
    timestamp: Timestamp
  }
): Promise<void> {
  try {
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const auditEntry = {
      id: generateDocumentId(),
      action: logData.action,
      collection: logData.collection,
      documentId: logData.documentId,
      workspaceId: event.context.workspace!.id,
      userId: event.context.user!.id,
      profileId: event.context.profile!.id,
      timestamp: logData.timestamp,
      metadata: {
        userAgent: event.node.req.headers['user-agent'],
        ipAddress: getClientIP(event),
        originalData: logData.originalData,
        severity: logData.action === 'hard_delete' ? 'high' : 'medium'
      },
      createdAt: logData.timestamp
    }
    
    await db.collection('audit_logs').doc(auditEntry.id).set(auditEntry)
    console.log('🔥 [Data Delete API] Audit log created:', auditEntry.id)
    
  } catch (error) {
    console.error('🔥 [Data Delete API] Error creating audit log:', error)
    // Don't fail the main operation if audit logging fails
  }
}

/**
 * Generate document ID
 */
function generateDocumentId(): string {
  return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Get client IP address
 */
function getClientIP(event: any): string {
  const xForwardedFor = event.node.req.headers['x-forwarded-for']
  const xRealIp = event.node.req.headers['x-real-ip']
  
  if (xForwardedFor) {
    return Array.isArray(xForwardedFor) ? xForwardedFor[0] : xForwardedFor.split(',')[0].trim()
  }
  
  if (xRealIp) {
    return Array.isArray(xRealIp) ? xRealIp[0] : xRealIp
  }
  
  return event.node.req.socket.remoteAddress || 'unknown'
}