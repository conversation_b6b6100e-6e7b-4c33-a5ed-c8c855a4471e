import { readBody } from 'h3'
import { getFirestore } from 'firebase-admin/firestore'
import { initializeFirebaseAdmin } from '../../utils/firebase-admin'
import { sessionMiddleware } from '../../middleware/session'
import { searchEmbeddings } from '../../utils/embeddings'
import { <PERSON>rro<PERSON><PERSON><PERSON><PERSON> } from '../../../utils/error-handler'
import type { 
  DataQuery, 
  DataResult, 
  QueryFilters, 
  SortConfig, 
  PaginationConfig,
  VectorSearchConfig,
  BaseEntity 
} from '~/types/data-api'

/**
 * Read data with multi-tenant support and vector search
 * POST /api/data/read
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [Data Read API] Processing read request')
    
    // Validate session and apply middleware
    await sessionMiddleware(event)
    
    // Parse request body
    const body = await readBody(event)
    const { collection, query = {}, options = {} } = body
    
    if (!collection) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Bad Request',
        data: { error: 'Collection name is required' }
      })
    }
    
    console.log('🔥 [Data Read API] Reading from collection:', collection)
    
    // Initialize Firebase Admin
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    // Build query with workspace scoping
    const dataQuery: DataQuery = {
      filters: {
        ...query.filters,
        // Always filter by workspace for multi-tenant isolation
        workspaceIds: { $contains: event.context.workspace!.id }
      },
      sort: query.sort,
      pagination: query.pagination,
      vectorSearch: query.vectorSearch,
      select: query.select,
      exclude: query.exclude,
      populate: query.populate,
      useCache: query.useCache,
      cacheTimeout: query.cacheTimeout
    }
    
    let result: DataResult<any>
    
    // Handle vector search if requested
    if (dataQuery.vectorSearch) {
      console.log('🔥 [Data Read API] Processing vector search')
      result = await performVectorSearch(event, collection, dataQuery)
    } else {
      console.log('🔥 [Data Read API] Processing standard query')
      result = await performStandardQuery(db, collection, dataQuery, options)
    }
    
    // Apply field selection/exclusion
    if (dataQuery.select || dataQuery.exclude) {
      result.data = Array.isArray(result.data) 
        ? result.data.map(item => filterFields(item, dataQuery.select, dataQuery.exclude))
        : filterFields(result.data, dataQuery.select, dataQuery.exclude)
    }
    
    console.log('🔥 [Data Read API] Query executed successfully')
    
    return {
      success: true,
      data: result.data,
      meta: result.meta,
      message: 'Data retrieved successfully'
    }
    
  } catch (error) {
    console.error('🔥 [Data Read API] Error:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to read data')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})

/**
 * Perform vector search using embeddings
 */
async function performVectorSearch(
  event: any,
  collection: string,
  query: DataQuery
): Promise<DataResult<any>> {
  const vectorSearch = query.vectorSearch!
  
  // Get embeddings using the text query
  const app = await initializeFirebaseAdmin()
  const db = getFirestore(app)
  
  // First, get the embedding for the search query
  // For now, we'll use a simple approach - in production, you'd use VertexAI
  const searchResults = await searchEmbeddings(event, [], {
    workspaceId: event.context.workspace!.id,
    type: 'document',
    limit: vectorSearch.limit || 10
  })
  
  if (searchResults.length === 0) {
    return {
      statusCode: 200,
      data: [],
      meta: { total: 0, hasMore: false }
    }
  }
  
  // Get the document IDs from search results
  const documentIds = searchResults.map(result => result.embedding.id)
  
  // Query the actual collection using the document IDs
  const collectionRef = db.collection(collection)
  const chunks = chunkArray(documentIds, 10) // Firestore 'in' query limit
  
  let allDocs: any[] = []
  
  for (const chunk of chunks) {
    const snapshot = await collectionRef
      .where('id', 'in', chunk)
      .where('workspaceIds', 'array-contains', event.context.workspace!.id)
      .get()
    
    snapshot.forEach(doc => {
      const data = doc.data()
      const searchResult = searchResults.find(sr => sr.embedding.id === doc.id)
      allDocs.push({
        ...data,
        _similarity: searchResult?.similarity || 0
      })
    })
  }
  
  // Sort by similarity
  allDocs.sort((a, b) => b._similarity - a._similarity)
  
  return {
    statusCode: 200,
    data: allDocs,
    meta: {
      total: allDocs.length,
      hasMore: false
    }
  }
}

/**
 * Perform standard Firestore query
 */
async function performStandardQuery(
  db: any,
  collection: string,
  query: DataQuery,
  options: any
): Promise<DataResult<any>> {
  let firestoreQuery = db.collection(collection)
  
  // Apply filters
  if (query.filters) {
    firestoreQuery = applyFilters(firestoreQuery, query.filters)
  }
  
  // Apply sorting
  if (query.sort && query.sort.length > 0) {
    for (const sort of query.sort) {
      firestoreQuery = firestoreQuery.orderBy(sort.field, sort.direction)
    }
  }
  
  // Apply pagination
  if (query.pagination) {
    const { limit, offset, startAfter, endBefore } = query.pagination
    
    if (limit) {
      firestoreQuery = firestoreQuery.limit(limit)
    }
    
    if (offset) {
      firestoreQuery = firestoreQuery.offset(offset)
    }
    
    if (startAfter) {
      // In real implementation, you'd need to get the document reference
      // firestoreQuery = firestoreQuery.startAfter(startAfterDoc)
    }
    
    if (endBefore) {
      // In real implementation, you'd need to get the document reference
      // firestoreQuery = firestoreQuery.endBefore(endBeforeDoc)
    }
  }
  
  // Execute query
  const snapshot = await firestoreQuery.get()
  
  const data: any[] = []
  snapshot.forEach(doc => {
    data.push({
      id: doc.id,
      ...doc.data()
    })
  })
  
  // Get total count for pagination metadata
  let totalCount = data.length
  if (query.pagination?.limit) {
    // For accurate pagination, we'd need a separate count query
    // This is a simplified implementation
    totalCount = data.length
  }
  
  return {
    statusCode: 200,
    data,
    meta: {
      total: totalCount,
      hasMore: query.pagination?.limit ? data.length === query.pagination.limit : false,
      page: query.pagination?.offset ? Math.floor(query.pagination.offset / (query.pagination.limit || 1)) + 1 : 1,
      limit: query.pagination?.limit
    }
  }
}

/**
 * Apply filters to Firestore query
 */
function applyFilters(query: any, filters: QueryFilters): any {
  for (const [field, filterValue] of Object.entries(filters)) {
    if (typeof filterValue === 'object' && filterValue !== null) {
      for (const [operator, value] of Object.entries(filterValue)) {
        switch (operator) {
          case '$eq':
            query = query.where(field, '==', value)
            break
          case '$ne':
            query = query.where(field, '!=', value)
            break
          case '$gt':
            query = query.where(field, '>', value)
            break
          case '$gte':
            query = query.where(field, '>=', value)
            break
          case '$lt':
            query = query.where(field, '<', value)
            break
          case '$lte':
            query = query.where(field, '<=', value)
            break
          case '$in':
            query = query.where(field, 'in', value)
            break
          case '$nin':
            query = query.where(field, 'not-in', value)
            break
          case '$contains':
            query = query.where(field, 'array-contains', value)
            break
          case '$containsAny':
            query = query.where(field, 'array-contains-any', value)
            break
          default:
            console.warn('🔥 [Data Read API] Unsupported filter operator:', operator)
        }
      }
    } else {
      // Simple equality filter
      query = query.where(field, '==', filterValue)
    }
  }
  
  return query
}

/**
 * Filter fields based on select/exclude options
 */
function filterFields(data: any, select?: string[], exclude?: string[]): any {
  if (!data || (typeof data !== 'object')) return data
  
  const result = { ...data }
  
  if (select && select.length > 0) {
    // Only include selected fields
    const filtered: any = {}
    for (const field of select) {
      if (field in result) {
        filtered[field] = result[field]
      }
    }
    return filtered
  }
  
  if (exclude && exclude.length > 0) {
    // Exclude specified fields
    for (const field of exclude) {
      delete result[field]
    }
  }
  
  return result
}

/**
 * Utility function to chunk array
 */
function chunkArray<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size))
  }
  return chunks
}