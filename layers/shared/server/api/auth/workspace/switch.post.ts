import { switchWorkspace } from '../../../utils/session'
import { sessionMiddleware } from '../../../middleware/session'
import { <PERSON>rror<PERSON><PERSON><PERSON> } from '../../../../utils/error-handler'

/**
 * Switch workspace context
 * POST /api/auth/workspace/switch
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [Workspace API] Switching workspace')
    
    // Validate session first
    await sessionMiddleware(event)
    
    const body = await readBody(event)
    
    if (!body.workspaceId) {
      throw new Error('Workspace ID is required')
    }
    
    // Switch workspace context
    const userSession = await switchWorkspace(event, body.workspaceId)
    
    console.log('🔥 [Workspace API] Workspace switched successfully')
    
    return {
      success: true,
      data: userSession,
      message: 'Workspace switched successfully'
    }
    
  } catch (error) {
    console.error('🔥 [Workspace API] Error switching workspace:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to switch workspace')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})