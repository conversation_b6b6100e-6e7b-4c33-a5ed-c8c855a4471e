/**
 * @fileoverview User registration endpoint for the PIB-METHOD multi-tenant system.
 * 
 * This endpoint handles new user registration with Firebase Auth, creates a default
 * workspace and user profile, establishes secure sessions, and initializes user
 * data structures for the multi-tenant architecture.
 * 
 * Key Features:
 * - Firebase Authentication user creation
 * - Automatic default workspace creation
 * - Owner profile creation with full permissions
 * - Secure session establishment
 * - Vector embedding initialization for AI features
 * - Comprehensive validation and error handling
 * - Multi-tenant workspace setup
 * 
 * Security Considerations:
 * - Email format validation using regex
 * - Password strength validation (minimum 6 characters)
 * - Firebase ID token verification on server-side
 * - HTTP-only session cookies
 * - Owner role assignment with full permissions
 * - CSRF protection via session management
 * 
 * Data Creation Flow:
 * 1. User document in 'users' collection
 * 2. Workspace document in 'workspaces' collection
 * 3. Profile document in 'profiles' collection
 * 4. Initial embedding document for AI features
 * 5. Secure session with HTTP-only cookies
 * 
 * <AUTHOR> Authentication System
 * @version 1.0.0
 * @since 2024-01-01
 */

import { createUserWithEmailAndPassword, signInWithEmailAndPassword } from 'firebase/auth'
import { getAuth } from 'firebase-admin/auth'
import { getFirestore, Timestamp } from 'firebase-admin/firestore'
import { createSession } from '../../utils/session'
import { useFirebaseServer, initializeFirebaseAdmin } from '../../utils/firebase-admin'
import { ErrorHandler } from '../../../utils/error-handler'
import type { User, Workspace, Profile } from '../../types/auth'
import { ROLE_PERMISSIONS } from '../../types/auth'
import type { SessionPayload } from '../../types/session'
import { randomUUID } from 'crypto'

/**
 * Request body interface for signup endpoint.
 * @interface SignupRequest
 */
interface SignupRequest {
  /** User's email address for account creation */
  email: string
  /** User's password (minimum 6 characters) */
  password: string
  /** User's display name for profile */
  displayName: string
  /** Optional custom workspace name (defaults to "User's Workspace") */
  workspaceName?: string
}

/**
 * Successful signup response data structure.
 * @interface SignupResponseData
 */
interface SignupResponseData {
  /** User information and session details */
  user: {
    id: string
    email: string
    displayName: string
    photoURL?: string
    emailVerified: boolean
    lastLoginAt: string
  }
  /** Complete session object with tokens */
  session: SessionPayload
  /** Created workspace details */
  workspace: {
    id: string
    name: string
    slug: string
    ownerId: string
    plan: string
    userRole: string
    createdAt: string
    updatedAt: string
  }
  /** Created user profile in the workspace */
  profile: {
    id: string
    userId: string
    workspaceId: string
    displayName: string
    avatar?: string
    role: string
    permissions: string[]
    createdAt: string
    lastActiveAt: string
  }
}

/**
 * User registration API endpoint with workspace and profile creation.
 * 
 * Creates a new user account via Firebase Auth, sets up a default workspace
 * with the user as owner, creates a user profile with full permissions,
 * and establishes a secure session. Includes initialization of AI features
 * and vector embeddings for the new workspace.
 * 
 * **Registration Flow:**
 * 1. Validate request body (email/password/displayName required)
 * 2. Validate email format and password strength
 * 3. Create Firebase Auth user account
 * 4. Verify ID token with Firebase Admin
 * 5. Create user document in Firestore
 * 6. Generate default workspace with unique slug
 * 7. Create workspace document with default settings
 * 8. Create owner profile with full permissions
 * 9. Initialize vector embeddings for AI features
 * 10. Create secure session with HTTP-only cookie
 * 11. Return user data and workspace context
 * 
 * **Default Workspace Setup:**
 * - Free plan with 14-day trial period
 * - Basic feature limits (5 users, 10 projects, 1GB storage)
 * - Default security and privacy settings
 * - AI features enabled by default
 * - Audit logging enabled
 * - Owner role with full RBAC permissions
 * 
 * **Security Features:**
 * - Email format validation (RFC 5322 basic pattern)
 * - Password strength validation (minimum 6 characters)
 * - Firebase ID token verification on server
 * - HTTP-only session cookies
 * - CSRF protection via session management
 * - Owner role assignment with full permissions
 * 
 * **Error Handling:**
 * - `409` - Email already in use
 * - `400` - Invalid email format, weak password, or missing fields
 * - `500` - Server error or Firebase service issues
 * 
 * @route POST /api/auth/signup
 * @param {H3Event} event - Nuxt server event object
 * @returns {Promise<{success: boolean, data: SignupResponseData, message: string}>} Registration result
 * 
 * @throws {H3Error} 400 - Invalid email format, weak password, or missing required fields
 * @throws {H3Error} 409 - Email already in use
 * @throws {H3Error} 500 - Server error or service unavailable
 * 
 * @example
 * ```typescript
 * // Request
 * const response = await $fetch('/api/auth/signup', {
 *   method: 'POST',
 *   body: {
 *     email: '<EMAIL>',
 *     password: 'securePassword123',
 *     displayName: 'John Doe',
 *     workspaceName: 'My Company' // optional
 *   }
 * })
 * 
 * // Response (success)
 * {
 *   success: true,
 *   data: {
 *     user: {
 *       id: 'firebase-uid',
 *       email: '<EMAIL>',
 *       displayName: 'John Doe',
 *       emailVerified: false,
 *       lastLoginAt: '2024-01-01T10:00:00.000Z'
 *     },
 *     session: { ... },
 *     workspace: {
 *       id: 'workspace-id',
 *       name: 'My Company',
 *       slug: 'my-company',
 *       plan: 'free',
 *       userRole: 'owner'
 *     },
 *     profile: {
 *       id: 'profile-id',
 *       role: 'owner',
 *       permissions: ['read', 'write', 'admin', 'manage_users', ...],
 *       title: 'Owner'
 *     }
 *   },
 *   message: 'Account created successfully'
 * }
 * ```
 * 
 * @see {@link https://firebase.google.com/docs/auth/web/password-auth} Firebase Authentication
 * @see {@link createSession} Session management utilities
 * @see {@link ErrorHandler} Error handling and logging
 * @see {@link ROLE_PERMISSIONS} Role-based access control
 * @see {@link User} User data structure
 * @see {@link Workspace} Workspace data structure
 * @see {@link Profile} Profile data structure
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [Signup API] Processing user signup')
    
    const body = await readBody(event)
    
    // Validate required fields
    if (!body.email || !body.password) {
      throw new Error('Email and password are required')
    }
    
    if (!body.displayName) {
      throw new Error('Display name is required')
    }
    
    const { email, password, displayName, workspaceName } = body
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format')
    }
    
    // Validate password strength
    if (password.length < 6) {
      throw new Error('Password must be at least 6 characters')
    }
    
    console.log('🔥 [Signup API] Creating Firebase user:', email)
    
    // Initialize Firebase for client-side auth
    const { auth, firestore } = await useFirebaseServer(undefined)
    
    // Create user with Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, email, password)
    const firebaseUser = userCredential.user
    
    console.log('🔥 [Signup API] Firebase user created:', firebaseUser.uid)
    
    // Get Firebase ID token for server operations
    const idToken = await firebaseUser.getIdToken()
    
    // Initialize Firebase Admin for server operations
    const adminApp = await initializeFirebaseAdmin()
    const adminAuth = getAuth(adminApp)
    const adminDb = getFirestore(adminApp)
    
    // Verify the ID token
    const decodedToken = await adminAuth.verifyIdToken(idToken)
    
    const now = new Date()
    const timestamp = Timestamp.fromDate(now)
    
    // Create User document
    const userData: User = {
      id: firebaseUser.uid,
      email: firebaseUser.email!,
      displayName: displayName,
      photoURL: firebaseUser.photoURL || undefined,
      emailVerified: firebaseUser.emailVerified,
      phoneNumber: firebaseUser.phoneNumber || undefined,
      createdAt: timestamp,
      updatedAt: timestamp,
      deletedAt: null,
      lastLoginAt: timestamp,
      isActive: true,
      preferences: {
        theme: 'system',
        language: 'en',
        timezone: 'UTC',
        notifications: {
          email: true,
          push: true,
          sms: false
        },
        privacy: {
          profileVisible: true,
          activityVisible: true
        }
      }
    }
    
    console.log('🔥 [Signup API] Creating user document')
    await adminDb.collection('users').doc(firebaseUser.uid).set(userData)
    
    // Create default workspace
    const workspaceId = randomUUID()
    const workspaceSlug = (workspaceName || `${displayName}'s Workspace`)
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '')
    
    const workspaceData: Workspace = {
      id: workspaceId,
      name: workspaceName || `${displayName}'s Workspace`,
      description: 'Default workspace created during signup',
      slug: workspaceSlug,
      ownerId: firebaseUser.uid,
      settings: {
        logo: undefined,
        primaryColor: '#3B82F6',
        timezone: 'UTC',
        language: 'en',
        allowInvites: true,
        requireEmailVerification: false,
        enableSSO: false,
        enableAI: true,
        passwordPolicy: {
          minLength: 6,
          requireUppercase: false,
          requireLowercase: false,
          requireNumbers: false,
          requireSymbols: false
        },
        sessionTimeout: 480, // 8 hours
        dataRetentionDays: 365,
        enableAuditLogs: true
      },
      features: {
        maxUsers: 5,
        maxProjects: 10,
        maxStorage: 1, // 1GB
        enableAPI: true,
        enableIntegrations: false,
        enableAdvancedReporting: false,
        enableCustomBranding: false,
        prioritySupport: false
      },
      createdAt: timestamp,
      updatedAt: timestamp,
      deletedAt: null,
      plan: 'free',
      subscriptionStatus: 'active',
      trialEndsAt: Timestamp.fromDate(new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000)), // 14 days
      billingEmail: email
    }
    
    console.log('🔥 [Signup API] Creating workspace document')
    await adminDb.collection('workspaces').doc(workspaceId).set(workspaceData)
    
    // Create user profile for the workspace (as owner)
    const profileId = randomUUID()
    const profileData: Profile = {
      id: profileId,
      userId: firebaseUser.uid,
      workspaceId: workspaceId,
      displayName: displayName,
      avatar: firebaseUser.photoURL || undefined,
      title: 'Owner',
      department: undefined,
      role: 'owner',
      permissions: ROLE_PERMISSIONS.owner,
      customPermissions: undefined,
      preferences: {
        dashboardLayout: 'grid',
        defaultProject: undefined,
        notifications: {
          projectUpdates: true,
          teamMentions: true,
          systemAlerts: true,
          weeklyDigest: true
        },
        sidebarCollapsed: false,
        defaultView: 'kanban'
      },
      createdAt: timestamp,
      updatedAt: timestamp,
      deletedAt: null,
      lastActiveAt: timestamp,
      invitedBy: undefined,
      invitedAt: undefined,
      acceptedAt: timestamp
    }
    
    console.log('🔥 [Signup API] Creating profile document')
    await adminDb.collection('profiles').doc(profileId).set(profileData)
    
    // Create session payload
    const sessionPayload: SessionPayload = {
      user: {
        id: firebaseUser.uid,
        email: firebaseUser.email!,
        displayName: displayName,
        photoURL: firebaseUser.photoURL || undefined,
        emailVerified: firebaseUser.emailVerified,
        lastLoginAt: now.toISOString()
      },
      currentWorkspace: {
        id: workspaceId,
        name: workspaceData.name,
        slug: workspaceData.slug,
        ownerId: firebaseUser.uid,
        plan: workspaceData.plan,
        userRole: 'owner',
        createdAt: now.toISOString(),
        updatedAt: now.toISOString()
      },
      currentProfile: {
        id: profileId,
        userId: firebaseUser.uid,
        workspaceId: workspaceId,
        displayName: displayName,
        avatar: firebaseUser.photoURL || undefined,
        role: 'owner',
        permissions: ROLE_PERMISSIONS.owner,
        createdAt: now.toISOString(),
        lastActiveAt: now.toISOString()
      },
      workspaces: [{
        id: workspaceId,
        name: workspaceData.name,
        slug: workspaceData.slug,
        ownerId: firebaseUser.uid,
        plan: workspaceData.plan,
        userRole: 'owner',
        createdAt: now.toISOString(),
        updatedAt: now.toISOString()
      }],
      token: {
        idToken: idToken,
        accessToken: undefined,
        refreshToken: undefined,
        expirationTime: decodedToken.exp * 1000
      }
    }
    
    console.log('🔥 [Signup API] Creating session')
    
    // Create session
    const userSession = await createSession(event, sessionPayload)
    
    // Initialize vector embeddings for the new user
    console.log('🔥 [Signup API] Setting up vector embeddings')
    try {
      // Create initial embeddings collection entry for the workspace
      await adminDb.collection('embeddings').doc(`${workspaceId}_init`).set({
        id: `${workspaceId}_init`,
        content: 'Initial workspace setup',
        embedding: new Array(1536).fill(0), // Zero vector for initialization
        metadata: {
          workspaceId: workspaceId,
          userId: firebaseUser.uid,
          profileId: profileId,
          type: 'context',
          source: 'signup',
          tags: ['initialization', 'setup'],
          createdAt: now.toISOString(),
          updatedAt: now.toISOString()
        }
      })
    } catch (embeddingError) {
      console.warn('🔥 [Signup API] Failed to initialize embeddings:', embeddingError)
      // Don't fail the signup if embeddings fail
    }
    
    console.log('🔥 [Signup API] Signup completed successfully')
    
    return {
      success: true,
      data: {
        user: userSession.user,
        session: userSession,
        workspace: userSession.currentWorkspace,
        profile: userSession.currentProfile
      },
      message: 'Account created successfully'
    }
    
  } catch (error) {
    console.error('🔥 [Signup API] Error during signup:', error)
    
    // Handle specific Firebase errors
    if (error.code === 'auth/email-already-in-use') {
      const appError = ErrorHandler.handle(error, 'An account with this email already exists')
      throw createError({
        statusCode: 409,
        statusMessage: appError.message
      })
    }
    
    if (error.code === 'auth/weak-password') {
      const appError = ErrorHandler.handle(error, 'Password is too weak')
      throw createError({
        statusCode: 400,
        statusMessage: appError.message
      })
    }
    
    if (error.code === 'auth/invalid-email') {
      const appError = ErrorHandler.handle(error, 'Invalid email format')
      throw createError({
        statusCode: 400,
        statusMessage: appError.message
      })
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to create account')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})