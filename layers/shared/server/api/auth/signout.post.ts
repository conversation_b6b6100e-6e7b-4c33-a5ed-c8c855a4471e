/**
 * @fileoverview User logout endpoint for the PIB-METHOD multi-tenant system.
 * 
 * This endpoint handles secure user logout with comprehensive session cleanup,
 * Firebase token revocation, and graceful error handling. Ensures complete
 * session termination across client and server-side authentication state.
 * 
 * Key Features:
 * - Complete session cleanup and destruction
 * - Firebase token revocation for security
 * - Client-side Firebase signout
 * - Graceful error handling (always returns success)
 * - Multi-device session invalidation
 * - HTTP-only cookie cleanup
 * 
 * Security Considerations:
 * - Revokes all refresh tokens for the user
 * - Destroys server-side session data
 * - Clears HTTP-only session cookies
 * - Client-side Firebase authentication cleanup
 * - Always returns success to prevent state inconsistencies
 * - Audit logging for security events
 * 
 * Logout Flow:
 * 1. Retrieve session token from HTTP-only cookie
 * 2. Destroy server-side session data
 * 3. Optional Firebase token revocation (if ID token provided)
 * 4. Client-side Firebase signout
 * 5. Always return success for client state consistency
 * 
 * <AUTHOR> Authentication System
 * @version 1.0.0
 * @since 2024-01-01
 */

import { signOut } from 'firebase/auth'
import { getAuth } from 'firebase-admin/auth'
import { destroySession } from '../../utils/session'
import { useFirebaseServer, initializeFirebaseAdmin } from '../../utils/firebase-admin'
import { ErrorHandler } from '../../../utils/error-handler'

/**
 * Request body interface for signout endpoint.
 * @interface SignoutRequest
 */
interface SignoutRequest {
  /** Optional Firebase ID token for token revocation */
  idToken?: string
}

/**
 * Signout response data structure.
 * @interface SignoutResponseData
 */
interface SignoutResponseData {
  /** Always null for signout */
  data: null
  /** Optional warning message if cleanup fails */
  warning?: string
}

/**
 * User logout API endpoint with comprehensive session cleanup.
 * 
 * Performs secure user logout by destroying server-side sessions,
 * revoking Firebase tokens, and cleaning up client-side authentication.
 * Always returns success to maintain client state consistency, even
 * if some cleanup operations fail.
 * 
 * **Logout Flow:**
 * 1. Check for existing session token in HTTP-only cookie
 * 2. Destroy server-side session data and clear cookies
 * 3. Optionally revoke Firebase refresh tokens (if ID token provided)
 * 4. Perform client-side Firebase signout
 * 5. Return success status (always succeeds for client consistency)
 * 
 * **Security Features:**
 * - Complete session destruction with database cleanup
 * - Firebase refresh token revocation (invalidates all user sessions)
 * - HTTP-only cookie removal
 * - Client-side Firebase authentication state cleanup
 * - Graceful error handling to prevent logout failures
 * 
 * **Multi-Device Security:**
 * - When ID token is provided, revokes all refresh tokens
 * - This invalidates sessions across all devices for the user
 * - Provides enhanced security for sensitive account actions
 * 
 * **Error Handling:**
 * - Always returns 200 success status
 * - Logs errors but continues with logout process
 * - Returns warning messages for partial failures
 * - Prevents client state inconsistencies from server errors
 * 
 * @route POST /api/auth/signout
 * @param {H3Event} event - Nuxt server event object
 * @returns {Promise<{success: boolean, data: null, message: string, warning?: string}>} Logout result
 * 
 * @example
 * ```typescript
 * // Basic logout (session cleanup only)
 * const response = await $fetch('/api/auth/signout', {
 *   method: 'POST'
 * })
 * 
 * // Enhanced logout with token revocation
 * const response = await $fetch('/api/auth/signout', {
 *   method: 'POST',
 *   body: {
 *     idToken: 'firebase-id-token' // optional
 *   }
 * })
 * 
 * // Response (always success)
 * {
 *   success: true,
 *   data: null,
 *   message: 'Signed out successfully',
 *   warning: 'Some cleanup operations may have failed' // optional
 * }
 * ```
 * 
 * @see {@link https://firebase.google.com/docs/auth/admin/manage-sessions} Firebase Session Management
 * @see {@link destroySession} Session destruction utilities
 * @see {@link ErrorHandler} Error handling and logging
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [Signout API] Processing user signout')
    
    // Get current session token from cookie
    const sessionToken = getCookie(event, 'omni-session')
    
    if (sessionToken) {
      console.log('🔥 [Signout API] Session token found, performing cleanup')
      
      try {
        // Destroy session and clean up server-side data
        await destroySession(event)
        console.log('🔥 [Signout API] Session destroyed successfully')
      } catch (sessionError) {
        console.warn('🔥 [Signout API] Error destroying session:', sessionError)
        // Continue with signout even if session cleanup fails
      }
    }
    
    // Get Firebase ID token from request body (optional)
    const body = await readBody(event).catch(() => ({}))
    const { idToken } = body
    
    if (idToken) {
      console.log('🔥 [Signout API] Firebase ID token provided, revoking tokens')
      
      try {
        // Initialize Firebase Admin for server operations
        const adminApp = await initializeFirebaseAdmin()
        const adminAuth = getAuth(adminApp)
        
        // Verify token and get user ID
        const decodedToken = await adminAuth.verifyIdToken(idToken)
        
        // Revoke all refresh tokens for the user
        // This will invalidate all sessions for this user across all devices
        await adminAuth.revokeRefreshTokens(decodedToken.uid)
        
        console.log('🔥 [Signout API] Firebase tokens revoked for user:', decodedToken.uid)
      } catch (tokenError) {
        console.warn('🔥 [Signout API] Error revoking Firebase tokens:', tokenError)
        // Continue with signout even if token revocation fails
      }
    }
    
    // Initialize Firebase client for signout
    try {
      const { auth } = await useFirebaseServer(undefined)
      
      // Sign out from Firebase client
      await signOut(auth)
      console.log('🔥 [Signout API] Firebase client signout completed')
    } catch (firebaseError) {
      console.warn('🔥 [Signout API] Error with Firebase client signout:', firebaseError)
      // Continue with signout even if Firebase client signout fails
    }
    
    console.log('🔥 [Signout API] Signout completed successfully')
    
    return {
      success: true,
      data: null,
      message: 'Signed out successfully'
    }
    
  } catch (error) {
    console.error('🔥 [Signout API] Error during signout:', error)
    
    // For signout, we should still return success even if there are errors
    // The client should be able to clear its state regardless
    console.log('🔥 [Signout API] Returning success despite errors')
    
    return {
      success: true,
      data: null,
      message: 'Signed out successfully',
      warning: 'Some cleanup operations may have failed'
    }
  }
})