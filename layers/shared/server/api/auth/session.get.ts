/**
 * @fileoverview Session validation endpoint for the PIB-METHOD multi-tenant system.
 * 
 * This endpoint validates and retrieves the current user session, providing
 * complete authentication state, workspace context, and user profile information.
 * Used for session restoration and authentication checks.
 * 
 * Key Features:
 * - Session validation and retrieval
 * - Multi-tenant workspace context loading
 * - User profile and permissions validation
 * - Authentication state verification
 * - Session metadata (creation, expiration, activity)
 * - CSRF and security validation
 * 
 * Security Considerations:
 * - HTTP-only session cookie validation
 * - Session expiration and timeout checks
 * - User and workspace access validation
 * - Firebase token verification
 * - CSRF protection via session tokens
 * - No sensitive token exposure in response
 * 
 * Session Validation Flow:
 * 1. Extract session token from HTTP-only cookie
 * 2. Validate session in database
 * 3. Check session expiration and activity
 * 4. Verify user and workspace access
 * 5. Load current profile and permissions
 * 6. Return sanitized session data
 * 
 * <AUTHOR> Authentication System
 * @version 1.0.0
 * @since 2024-01-01
 */

import { validateSession } from '../../utils/session'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../utils/error-handler'

/**
 * Session response data structure.
 * @interface SessionResponseData
 */
interface SessionResponseData {
  /** User information */
  user: {
    id: string
    email: string
    displayName: string
    photoURL?: string
    emailVerified: boolean
  }
  /** Authentication status flags */
  isAuthenticated: boolean
  isLoading: boolean
  /** Current active workspace */
  currentWorkspace: {
    id: string
    name: string
    slug: string
    ownerId: string
    plan: string
    userRole: string
    createdAt: string
    updatedAt: string
  }
  /** Current user profile in workspace */
  currentProfile: {
    id: string
    userId: string
    workspaceId: string
    displayName: string
    avatar?: string
    role: string
    permissions: string[]
    createdAt: string
    lastActiveAt?: string
  }
  /** All workspaces user has access to */
  workspaces: Array<{
    id: string
    name: string
    slug: string
    ownerId: string
    plan: string
    userRole: string
    createdAt: string
    updatedAt: string
  }>
  /** Session metadata */
  sessionId: string
  createdAt: string
  expiresAt: string
  lastActiveAt: string
  /** Token information (sanitized) */
  token: null
  /** Error state */
  error: null
}

/**
 * Session validation and retrieval API endpoint.
 * 
 * Validates the current user session from HTTP-only cookies and returns
 * complete authentication state including user information, workspace
 * context, and session metadata. Used for session restoration and
 * authentication checks throughout the application.
 * 
 * **Validation Flow:**
 * 1. Extract session token from 'omni-session' HTTP-only cookie
 * 2. Validate session existence and integrity in database
 * 3. Check session expiration and activity timeouts
 * 4. Verify user account status and access permissions
 * 5. Load current workspace and profile information
 * 6. Update session activity timestamp
 * 7. Return sanitized session data (no sensitive tokens)
 * 
 * **Multi-Tenant Context:**
 * - Returns current active workspace information
 * - Includes user's role and permissions in workspace
 * - Provides list of all accessible workspaces
 * - Supports workspace switching via separate endpoint
 * 
 * **Security Features:**
 * - HTTP-only cookie session validation
 * - Session expiration and timeout enforcement
 * - User account status verification
 * - Workspace access permission checks
 * - No sensitive token data in response
 * - CSRF protection via session validation
 * 
 * **Session Metadata:**
 * - Session creation timestamp
 * - Session expiration time
 * - Last activity timestamp
 * - Session ID for tracking
 * 
 * **Error Handling:**
 * - `401` - Invalid, expired, or missing session
 * - `403` - User account disabled or suspended
 * - `500` - Server error or service unavailable
 * 
 * @route GET /api/auth/session
 * @param {H3Event} event - Nuxt server event object
 * @returns {Promise<{success: boolean, data: SessionResponseData, message: string}>} Session information
 * 
 * @throws {H3Error} 401 - Invalid, expired, or missing session
 * @throws {H3Error} 403 - User account disabled or workspace access denied
 * @throws {H3Error} 500 - Server error or service unavailable
 * 
 * @example
 * ```typescript
 * // Client-side session check
 * const response = await $fetch('/api/auth/session', {
 *   method: 'GET',
 *   credentials: 'include' // Important: include HTTP-only cookies
 * })
 * 
 * // Response (success)
 * {
 *   success: true,
 *   data: {
 *     user: {
 *       id: 'firebase-uid',
 *       email: '<EMAIL>',
 *       displayName: 'John Doe',
 *       emailVerified: true
 *     },
 *     isAuthenticated: true,
 *     isLoading: false,
 *     currentWorkspace: {
 *       id: 'workspace-id',
 *       name: 'My Company',
 *       plan: 'pro',
 *       userRole: 'admin'
 *     },
 *     currentProfile: {
 *       id: 'profile-id',
 *       role: 'admin',
 *       permissions: ['read', 'write', 'admin']
 *     },
 *     workspaces: [...],
 *     sessionId: 'session-uuid',
 *     createdAt: '2024-01-01T10:00:00.000Z',
 *     expiresAt: '2024-01-01T18:00:00.000Z',
 *     lastActiveAt: '2024-01-01T15:30:00.000Z',
 *     token: null,
 *     error: null
 *   },
 *   message: 'Session retrieved successfully'
 * }
 * 
 * // Response (unauthorized)
 * // HTTP 401 Unauthorized
 * {
 *   statusCode: 401,
 *   statusMessage: 'Unauthorized',
 *   data: { error: 'Invalid session' }
 * }
 * ```
 * 
 * @see {@link https://developer.mozilla.org/en-US/docs/Web/HTTP/Cookies#Secure_and_HttpOnly_cookies} HTTP-only Cookies
 * @see {@link validateSession} Session validation utilities
 * @see {@link ErrorHandler} Error handling and logging
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [Session API] Getting current session')
    
    const validation = await validateSession(event)
    
    if (!validation.isValid) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Unauthorized',
        data: { error: validation.error || 'Invalid session' }
      })
    }
    
    // Create user session response
    const userSession = {
      user: validation.user,
      isAuthenticated: true,
      isLoading: false,
      currentWorkspace: validation.workspace,
      currentProfile: validation.profile,
      workspaces: [validation.workspace], // TODO: Load all user workspaces
      sessionId: validation.session?.sessionId,
      createdAt: validation.session?.createdAt.toDate().toISOString(),
      expiresAt: validation.session?.expiresAt.toDate().toISOString(),
      lastActiveAt: validation.session?.lastActiveAt.toDate().toISOString(),
      token: null, // Don't expose Firebase token
      error: null
    }
    
    console.log('🔥 [Session API] Session retrieved successfully')
    
    return {
      success: true,
      data: userSession,
      message: 'Session retrieved successfully'
    }
    
  } catch (error) {
    console.error('🔥 [Session API] Error getting session:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to get session')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})