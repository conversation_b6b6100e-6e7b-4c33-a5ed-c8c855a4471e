import { destroySession } from '../../utils/session'
import { <PERSON><PERSON>r<PERSON><PERSON><PERSON> } from '../../../utils/error-handler'

/**
 * Destroy current session (logout)
 * DELETE /api/auth/session
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [Session API] Destroying session')
    
    await destroySession(event)
    
    console.log('🔥 [Session API] Session destroyed successfully')
    
    return {
      success: true,
      message: 'Session destroyed successfully'
    }
    
  } catch (error) {
    console.error('🔥 [Session API] Error destroying session:', error)
    const appError = ErrorHandler.handle(error, 'Failed to destroy session')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})