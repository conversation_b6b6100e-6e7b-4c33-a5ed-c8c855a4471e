/**
 * @fileoverview SSO token generation endpoint for the PIB-METHOD multi-tenant system.
 * 
 * This endpoint generates secure Single Sign-On (SSO) tokens for cross-application
 * authentication within the PIB ecosystem. Enables seamless user authentication
 * between different applications while maintaining security and access control.
 * 
 * Key Features:
 * - Cross-application SSO token generation
 * - Role-based application access control
 * - Short-lived token security (5-minute expiry)
 * - Temporary token storage for validation
 * - JWT-based secure token format
 * - Application-specific access permissions
 * 
 * Security Considerations:
 * - User authentication required before token generation
 * - Application access permission validation
 * - Short token expiration (5 minutes) for security
 * - Secure JWT signing with environment secret
 * - Temporary storage with TTL for validation
 * - User can only generate tokens for themselves
 * 
 * Supported Applications:
 * - main: Primary application (all authenticated users)
 * - crm: CRM module (admin, sales, crm_user roles)
 * - writer: Writing module (admin, writer, content_creator roles)
 * 
 * <AUTHOR> Authentication System
 * @version 1.0.0
 * @since 2024-01-01
 */

import { z } from 'zod'
import { nanoid } from 'nanoid'
import jwt from 'jsonwebtoken'

/**
 * Request validation schema for SSO token generation.
 * @constant generateTokenSchema
 */
const generateTokenSchema = z.object({
  /** Target application for SSO authentication */
  targetApp: z.enum(['main', 'crm', 'writer']),
  /** User ID requesting the SSO token */
  userId: z.string()
})

/**
 * Request body interface for SSO token generation.
 * @interface GenerateSSORequest
 */
interface GenerateSSORequest {
  /** Target application identifier */
  targetApp: 'main' | 'crm' | 'writer'
  /** User ID for token generation */
  userId: string
}

/**
 * SSO token generation response data structure.
 * @interface SSOTokenResponseData
 */
interface SSOTokenResponseData {
  /** Generated JWT SSO token */
  token: string
  /** Token expiration time in seconds */
  expiresIn: number
}

/**
 * JWT token payload structure.
 * @interface SSOTokenPayload
 */
interface SSOTokenPayload {
  /** User ID */
  userId: string
  /** User email address */
  email: string
  /** User roles for access control */
  roles: string[]
  /** Target application */
  targetApp: string
  /** Unique session identifier */
  sessionId: string
  /** Token generation timestamp */
  timestamp: number
}

/**
 * SSO token generation API endpoint for cross-application authentication.
 * 
 * Generates secure JWT tokens for Single Sign-On between PIB applications.
 * Validates user authentication, checks application access permissions,
 * and creates short-lived tokens for secure cross-app authentication.
 * 
 * **Token Generation Flow:**
 * 1. Verify user authentication (requireAuth middleware)
 * 2. Validate request payload (targetApp and userId)
 * 3. Confirm user is requesting token for themselves
 * 4. Check user has access to target application
 * 5. Generate JWT token with user context and permissions
 * 6. Store token temporarily for validation (5-minute TTL)
 * 7. Return token and expiration information
 * 
 * **Application Access Control:**
 * - **main**: All authenticated users (universal access)
 * - **crm**: Users with roles: admin, sales, crm_user
 * - **writer**: Users with roles: admin, writer, content_creator
 * 
 * **Security Features:**
 * - JWT tokens signed with SSO_SECRET environment variable
 * - Short token expiration (5 minutes) for security
 * - User can only generate tokens for themselves
 * - Role-based application access validation
 * - Temporary token storage with TTL for validation
 * - Unique session ID per token for tracking
 * 
 * **Token Payload:**
 * - User ID and email
 * - User roles for access control
 * - Target application identifier
 * - Unique session ID
 * - Generation timestamp
 * - JWT standard fields (exp, iat)
 * 
 * **Error Handling:**
 * - `401` - User not authenticated
 * - `403` - User lacks access to target application or unauthorized token request
 * - `400` - Invalid request payload or missing parameters
 * - `500` - Server error or token generation failure
 * 
 * @route POST /api/auth/sso/generate
 * @param {H3Event} event - Nuxt server event object
 * @returns {Promise<{success: boolean, data: SSOTokenResponseData}>} SSO token generation result
 * 
 * @throws {H3Error} 401 - User not authenticated
 * @throws {H3Error} 403 - Unauthorized to generate token for another user or no app access
 * @throws {H3Error} 400 - Invalid request payload
 * @throws {H3Error} 500 - Server error or token generation failure
 * 
 * @example
 * ```typescript
 * // Generate SSO token for CRM application
 * const response = await $fetch('/api/auth/sso/generate', {
 *   method: 'POST',
 *   body: {
 *     targetApp: 'crm',
 *     userId: 'current-user-id'
 *   },
 *   headers: {
 *     'Authorization': 'Bearer firebase-id-token'
 *   }
 * })
 * 
 * // Response (success)
 * {
 *   success: true,
 *   data: {
 *     token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
 *     expiresIn: 300 // 5 minutes
 *   }
 * }
 * 
 * // Use token for cross-app authentication
 * const appResponse = await $fetch('https://crm.example.com/auth/sso', {
 *   method: 'POST',
 *   body: { token: response.data.token }
 * })
 * ```
 * 
 * @see {@link https://jwt.io/} JWT (JSON Web Tokens)
 * @see {@link checkAppAccess} Application access validation
 * @see {@link ErrorHandler} Error handling and logging
 */
export default defineEventHandler(async (event) => {
  try {
    // Require authentication
    const user = await requireAuth(event)
    
    // Validate request
    const body = await readBody(event)
    const { targetApp, userId } = generateTokenSchema.parse(body)
    
    // Verify user is requesting for themselves
    if (userId !== user.uid) {
      throw createError({
        statusCode: 403,
        statusMessage: 'Unauthorized to generate SSO token for another user'
      })
    }
    
    // Check user has access to target app
    const hasAccess = await checkAppAccess(user, targetApp)
    if (!hasAccess) {
      throw createError({
        statusCode: 403,
        statusMessage: `No access to ${targetApp} application`
      })
    }
    
    // Generate SSO token with short expiry
    const ssoToken = jwt.sign(
      {
        userId: user.uid,
        email: user.email,
        roles: user.roles,
        targetApp,
        sessionId: nanoid(),
        timestamp: Date.now()
      },
      process.env.SSO_SECRET || 'omni-sso-secret-key',
      {
        expiresIn: '5m' // Short expiry for security
      }
    )
    
    // Store token in temporary cache for validation
    const storage = useStorage('sso-tokens')
    await storage.setItem(ssoToken, {
      userId: user.uid,
      targetApp,
      createdAt: new Date().toISOString()
    }, {
      ttl: 300 // 5 minutes
    })
    
    return {
      success: true,
      data: {
        token: ssoToken,
        expiresIn: 300
      }
    }
  } catch (error) {
    const appError = ErrorHandler.handle(error)
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})

/**
 * Validates user access permissions for specific PIB applications.
 * 
 * Checks if the authenticated user has the required roles to access
 * the target application within the PIB ecosystem. Each application
 * has specific role requirements for security and access control.
 * 
 * **Application Access Requirements:**
 * - **main**: Universal access - all authenticated users
 * - **crm**: Restricted access - admin, sales, crm_user roles
 * - **writer**: Restricted access - admin, writer, content_creator roles
 * 
 * @param {any} user - Authenticated user object with roles array
 * @param {string} targetApp - Target application identifier
 * @returns {Promise<boolean>} True if user has access, false otherwise
 * 
 * @example
 * ```typescript
 * const user = { roles: ['admin', 'sales'] }
 * 
 * // Check CRM access
 * const canAccessCRM = await checkAppAccess(user, 'crm') // true
 * 
 * // Check Writer access
 * const canAccessWriter = await checkAppAccess(user, 'writer') // false
 * ```
 * 
 * @see {@link GenerateSSORequest} SSO request interface
 */
async function checkAppAccess(user: any, targetApp: string): Promise<boolean> {
  switch (targetApp) {
    case 'main':
      // All authenticated users can access main app
      return true
      
    case 'crm':
      // Check CRM access roles
      return user.roles.some((role: string) => 
        ['admin', 'sales', 'crm_user'].includes(role)
      )
      
    case 'writer':
      // Check Writer access roles
      return user.roles.some((role: string) => 
        ['admin', 'writer', 'content_creator'].includes(role)
      )
      
    default:
      return false
  }
}