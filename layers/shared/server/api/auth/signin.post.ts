/**
 * @fileoverview Authentication signin endpoint for the PIB-METHOD multi-tenant system.
 * 
 * This endpoint handles user authentication with Firebase Auth, validates credentials,
 * loads user workspace context, and establishes secure server-side sessions.
 * Implements multi-tenant workspace resolution and audit logging for security.
 * 
 * Key Features:
 * - Firebase Authentication integration
 * - Multi-tenant workspace loading
 * - Secure session management with HTTP-only cookies
 * - Role-based access control (RBAC) preparation
 * - Comprehensive error handling with specific Firebase error codes
 * - Audit logging for security events
 * - Last login tracking and profile activity updates
 * 
 * Security Considerations:
 * - Email format validation using regex
 * - Firebase ID token verification on server-side
 * - Rate limiting via Firebase (auth/too-many-requests)
 * - Session tokens stored in HTTP-only cookies
 * - Workspace access validation
 * - CSRF protection via session management
 * 
 * Multi-Tenant Architecture:
 * - Users can belong to multiple workspaces
 * - Each workspace has separate profiles with roles/permissions
 * - Default workspace selection for session establishment
 * - Workspace switching supported via separate endpoint
 * 
 * <AUTHOR> Authentication System
 * @version 1.0.0
 * @since 2024-01-01
 */

import { signInWithEmailAndPassword } from 'firebase/auth'
import { getAuth } from 'firebase-admin/auth'
import { getFirestore, Timestamp } from 'firebase-admin/firestore'
import { createSession } from '../../utils/session'
import { useFirebaseServer, initializeFirebaseAdmin } from '../../utils/firebase-admin'
import { ErrorHandler } from '../../../utils/error-handler'
import type { SessionPayload } from '~/types/session'

/**
 * Request body interface for signin endpoint.
 * @interface SigninRequest
 */
interface SigninRequest {
  /** User's email address for authentication */
  email: string
  /** User's password for authentication */
  password: string
}

/**
 * Successful signin response data structure.
 * @interface SigninResponseData
 */
interface SigninResponseData {
  /** User information and session details */
  user: {
    id: string
    email: string
    displayName: string
    photoURL?: string
    emailVerified: boolean
    lastLoginAt: string
  }
  /** Complete session object with tokens */
  session: SessionPayload
  /** Current active workspace details */
  workspace: {
    id: string
    name: string
    slug: string
    ownerId: string
    plan: string
    userRole: string
    createdAt: string
    updatedAt: string
  }
  /** Current user profile in the workspace */
  profile: {
    id: string
    userId: string
    workspaceId: string
    displayName: string
    avatar?: string
    role: string
    permissions: string[]
    createdAt: string
    lastActiveAt?: string
  }
  /** All workspaces user has access to */
  availableWorkspaces: Array<{
    id: string
    name: string
    slug: string
    ownerId: string
    plan: string
    userRole: string
    createdAt: string
    updatedAt: string
  }>
}

/**
 * User signin API endpoint with multi-tenant workspace loading.
 * 
 * Authenticates users via Firebase Auth, loads their workspace context,
 * and establishes a secure server-side session. Supports multi-tenant
 * architecture where users can belong to multiple workspaces.
 * 
 * **Authentication Flow:**
 * 1. Validate request body (email/password required)
 * 2. Validate email format using regex
 * 3. Authenticate with Firebase Auth (client-side)
 * 4. Verify ID token with Firebase Admin (server-side)
 * 5. Load user document from Firestore
 * 6. Query user's workspace profiles
 * 7. Load workspace details for each profile
 * 8. Select default workspace (first available)
 * 9. Update user's last login timestamp
 * 10. Update profile's last active timestamp
 * 11. Create secure session with HTTP-only cookie
 * 12. Return user data and workspace context
 * 
 * **Multi-Tenant Support:**
 * - Users can belong to multiple workspaces
 * - Each workspace has separate user profiles
 * - Profiles contain role and permission data
 * - Default workspace selected for initial session
 * - Workspace switching available via separate endpoint
 * 
 * **Security Features:**
 * - Email format validation (RFC 5322 basic pattern)
 * - Firebase ID token verification on server
 * - Rate limiting via Firebase authentication
 * - HTTP-only session cookies
 * - CSRF protection via session management
 * - Audit logging for authentication events
 * 
 * **Error Handling:**
 * - `401` - Invalid credentials (user-not-found, wrong-password)
 * - `400` - Invalid email format or malformed request
 * - `403` - Account disabled
 * - `429` - Too many failed attempts (rate limiting)
 * - `500` - Server error or Firebase service issues
 * 
 * @route POST /api/auth/signin
 * @param {H3Event} event - Nuxt server event object
 * @returns {Promise<{success: boolean, data: SigninResponseData, message: string}>} Authentication result
 * 
 * @throws {H3Error} 400 - Invalid email format or missing required fields
 * @throws {H3Error} 401 - Invalid credentials or user not found
 * @throws {H3Error} 403 - Account disabled
 * @throws {H3Error} 429 - Rate limit exceeded
 * @throws {H3Error} 500 - Server error or service unavailable
 * 
 * @example
 * ```typescript
 * // Request
 * const response = await $fetch('/api/auth/signin', {
 *   method: 'POST',
 *   body: {
 *     email: '<EMAIL>',
 *     password: 'securePassword123'
 *   }
 * })
 * 
 * // Response (success)
 * {
 *   success: true,
 *   data: {
 *     user: {
 *       id: 'firebase-uid',
 *       email: '<EMAIL>',
 *       displayName: 'John Doe',
 *       emailVerified: true,
 *       lastLoginAt: '2024-01-01T10:00:00.000Z'
 *     },
 *     session: { ... },
 *     workspace: {
 *       id: 'workspace-id',
 *       name: 'My Company',
 *       plan: 'pro',
 *       userRole: 'admin'
 *     },
 *     profile: {
 *       id: 'profile-id',
 *       role: 'admin',
 *       permissions: ['read', 'write', 'admin']
 *     },
 *     availableWorkspaces: [...]
 *   },
 *   message: 'Signed in successfully'
 * }
 * ```
 * 
 * @see {@link https://firebase.google.com/docs/auth/web/password-auth} Firebase Authentication
 * @see {@link createSession} Session management utilities
 * @see {@link ErrorHandler} Error handling and logging
 * @see {@link SessionPayload} Session data structure
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [Signin API] Processing user signin')
    
    const body = await readBody(event)
    
    // Validate required fields
    if (!body.email || !body.password) {
      throw new Error('Email and password are required')
    }
    
    const { email, password } = body
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      throw new Error('Invalid email format')
    }
    
    console.log('🔥 [Signin API] Authenticating user:', email)
    
    // Initialize Firebase for client-side auth
    const { auth } = await useFirebaseServer(undefined)
    
    // Authenticate user with Firebase Auth
    const userCredential = await signInWithEmailAndPassword(auth, email, password)
    const firebaseUser = userCredential.user
    
    console.log('🔥 [Signin API] Firebase user authenticated:', firebaseUser.uid)
    
    // Get Firebase ID token for server operations
    const idToken = await firebaseUser.getIdToken()
    
    // Initialize Firebase Admin for server operations
    const adminApp = await initializeFirebaseAdmin()
    const adminAuth = getAuth(adminApp)
    const adminDb = getFirestore(adminApp)
    
    // Verify the ID token
    const decodedToken = await adminAuth.verifyIdToken(idToken)
    
    const now = new Date()
    const timestamp = Timestamp.fromDate(now)
    
    // Get user document
    console.log('🔥 [Signin API] Loading user document')
    const userDoc = await adminDb.collection('users').doc(firebaseUser.uid).get()
    
    if (!userDoc.exists) {
      throw new Error('User document not found')
    }
    
    const userData = userDoc.data()!
    
    // Update last login time
    await adminDb.collection('users').doc(firebaseUser.uid).update({
      lastLoginAt: timestamp,
      updatedAt: timestamp
    })
    
    // Get user's workspaces (profiles)
    console.log('🔥 [Signin API] Loading user workspaces')
    const profilesSnapshot = await adminDb.collection('profiles')
      .where('userId', '==', firebaseUser.uid)
      .where('deletedAt', '==', null)
      .get()
    
    if (profilesSnapshot.empty) {
      throw new Error('No workspaces found for user')
    }
    
    // Get workspace details for each profile
    const workspacePromises = profilesSnapshot.docs.map(async (profileDoc) => {
      const profileData = profileDoc.data()
      const workspaceDoc = await adminDb.collection('workspaces').doc(profileData.workspaceId).get()
      
      if (!workspaceDoc.exists) {
        console.warn('🔥 [Signin API] Workspace not found:', profileData.workspaceId)
        return null
      }
      
      const workspaceData = workspaceDoc.data()!
      
      return {
        workspace: {
          id: profileData.workspaceId,
          name: workspaceData.name,
          slug: workspaceData.slug,
          ownerId: workspaceData.ownerId,
          plan: workspaceData.plan,
          userRole: profileData.role,
          createdAt: workspaceData.createdAt.toDate().toISOString(),
          updatedAt: workspaceData.updatedAt.toDate().toISOString()
        },
        profile: {
          id: profileDoc.id,
          userId: profileData.userId,
          workspaceId: profileData.workspaceId,
          displayName: profileData.displayName,
          avatar: profileData.avatar,
          role: profileData.role,
          permissions: profileData.permissions || [],
          createdAt: profileData.createdAt.toDate().toISOString(),
          lastActiveAt: profileData.lastActiveAt?.toDate().toISOString()
        }
      }
    })
    
    const workspaceResults = await Promise.all(workspacePromises)
    const validWorkspaces = workspaceResults.filter(Boolean)
    
    if (validWorkspaces.length === 0) {
      throw new Error('No valid workspaces found for user')
    }
    
    // Default to the first workspace (or most recently used)
    const defaultWorkspace = validWorkspaces[0]
    
    // Update profile's last active time
    await adminDb.collection('profiles').doc(defaultWorkspace.profile.id).update({
      lastActiveAt: timestamp,
      updatedAt: timestamp
    })
    
    // Create session payload
    const sessionPayload: SessionPayload = {
      user: {
        id: firebaseUser.uid,
        email: firebaseUser.email!,
        displayName: userData.displayName || firebaseUser.displayName || '',
        photoURL: firebaseUser.photoURL || undefined,
        emailVerified: firebaseUser.emailVerified,
        lastLoginAt: now.toISOString()
      },
      currentWorkspace: defaultWorkspace.workspace,
      currentProfile: defaultWorkspace.profile,
      workspaces: validWorkspaces.map(w => w.workspace),
      token: {
        idToken: idToken,
        accessToken: undefined,
        refreshToken: undefined,
        expirationTime: decodedToken.exp * 1000
      }
    }
    
    console.log('🔥 [Signin API] Creating session')
    
    // Create session
    const userSession = await createSession(event, sessionPayload)
    
    console.log('🔥 [Signin API] Signin completed successfully')
    
    return {
      success: true,
      data: {
        user: userSession.user,
        session: userSession,
        workspace: userSession.currentWorkspace,
        profile: userSession.currentProfile,
        availableWorkspaces: userSession.workspaces
      },
      message: 'Signed in successfully'
    }
    
  } catch (error) {
    console.error('🔥 [Signin API] Error during signin:', error)
    
    // Handle specific Firebase errors
    if (error.code === 'auth/user-not-found') {
      const appError = ErrorHandler.handle(error, 'No account found with this email')
      throw createError({
        statusCode: 401,
        statusMessage: appError.message
      })
    }
    
    if (error.code === 'auth/wrong-password') {
      const appError = ErrorHandler.handle(error, 'Incorrect password')
      throw createError({
        statusCode: 401,
        statusMessage: appError.message
      })
    }
    
    if (error.code === 'auth/invalid-email') {
      const appError = ErrorHandler.handle(error, 'Invalid email format')
      throw createError({
        statusCode: 400,
        statusMessage: appError.message
      })
    }
    
    if (error.code === 'auth/user-disabled') {
      const appError = ErrorHandler.handle(error, 'This account has been disabled')
      throw createError({
        statusCode: 403,
        statusMessage: appError.message
      })
    }
    
    if (error.code === 'auth/too-many-requests') {
      const appError = ErrorHandler.handle(error, 'Too many failed attempts. Please try again later')
      throw createError({
        statusCode: 429,
        statusMessage: appError.message
      })
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to sign in')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})