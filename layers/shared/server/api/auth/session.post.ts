/**
 * @fileoverview Session creation and update endpoint for the PIB-METHOD multi-tenant system.
 * 
 * This endpoint handles session creation and updates, supporting session refresh
 * functionality, workspace switching, and session restoration. Acts as the
 * central session management endpoint for maintaining authentication state.
 * 
 * Key Features:
 * - New session creation with complete user context
 * - Existing session updates and refresh
 * - Workspace switching session updates
 * - Session token management and rotation
 * - Multi-tenant workspace context management
 * - Secure HTTP-only cookie handling
 * 
 * Security Considerations:
 * - HTTP-only session cookie creation/update
 * - Session payload validation and sanitization
 * - Workspace access permission verification
 * - CSRF protection via secure cookies
 * - Session token rotation for security
 * - User and profile access validation
 * 
 * Session Management Flow:
 * 1. Validate incoming session payload
 * 2. Check for existing session cookie
 * 3. Create new session or update existing
 * 4. Set/update HTTP-only cookies
 * 5. Return session confirmation
 * 
 * <AUTHOR> Authentication System
 * @version 1.0.0
 * @since 2024-01-01
 */

import { createSession, updateUserSession } from '../../utils/session'
import type { SessionPayload } from '~/types/session'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../../utils/error-handler'

/**
 * Session creation/update response data structure.
 * @interface SessionUpdateResponseData
 */
interface SessionUpdateResponseData {
  /** User information */
  user: {
    id: string
    email: string
    displayName: string
    photoURL?: string
    emailVerified: boolean
    lastLoginAt: string
  }
  /** Current active workspace */
  currentWorkspace: {
    id: string
    name: string
    slug: string
    ownerId: string
    plan: string
    userRole: string
    createdAt: string
    updatedAt: string
  }
  /** Current user profile in workspace */
  currentProfile: {
    id: string
    userId: string
    workspaceId: string
    displayName: string
    avatar?: string
    role: string
    permissions: string[]
    createdAt: string
    lastActiveAt?: string
  }
  /** All workspaces user has access to */
  workspaces: Array<{
    id: string
    name: string
    slug: string
    ownerId: string
    plan: string
    userRole: string
    createdAt: string
    updatedAt: string
  }>
  /** Session metadata */
  sessionId: string
  createdAt: string
  expiresAt: string
  lastActiveAt: string
  /** Token information (for session management) */
  token: {
    idToken: string
    accessToken?: string
    refreshToken?: string
    expirationTime: number
  }
}

/**
 * Session creation and update API endpoint.
 * 
 * Creates new user sessions or updates existing sessions with fresh data.
 * Supports session refresh operations, workspace switching, and session
 * restoration. Manages HTTP-only cookies and session security.
 * 
 * **Session Operations:**
 * - **New Session Creation**: When no existing session cookie is found
 * - **Session Update**: When existing session cookie is present
 * - **Workspace Switching**: Update session with new workspace context
 * - **Token Refresh**: Update session with refreshed authentication tokens
 * 
 * **Creation/Update Flow:**
 * 1. Validate required session payload fields
 * 2. Check for existing 'omni-session' HTTP-only cookie
 * 3. If existing session: update with new data and refresh timestamps
 * 4. If no existing session: create new session with complete setup
 * 5. Set/update HTTP-only session cookies with security flags
 * 6. Return complete session data for client state management
 * 
 * **Multi-Tenant Support:**
 * - Handles workspace context switching
 * - Updates user profile information per workspace
 * - Manages role and permission changes
 * - Supports multiple workspace access
 * 
 * **Security Features:**
 * - HTTP-only cookie session management
 * - Secure session token generation
 * - Session expiration and timeout handling
 * - CSRF protection via secure cookies
 * - Workspace access validation
 * - User permission verification
 * 
 * **Use Cases:**
 * - Initial login session creation
 * - Session refresh after token renewal
 * - Workspace switching operations
 * - Session restoration after page reload
 * - Profile or permission updates
 * 
 * **Error Handling:**
 * - `400` - Missing required session data or invalid payload
 * - `403` - Insufficient permissions or workspace access denied
 * - `500` - Server error or session creation/update failure
 * 
 * @route POST /api/auth/session
 * @param {H3Event} event - Nuxt server event object
 * @returns {Promise<{success: boolean, data: SessionUpdateResponseData, message: string}>} Session operation result
 * 
 * @throws {H3Error} 400 - Missing required session data or invalid payload structure
 * @throws {H3Error} 403 - User lacks access to specified workspace or profile
 * @throws {H3Error} 500 - Server error or session management failure
 * 
 * @example
 * ```typescript
 * // Create new session (after login)
 * const sessionPayload = {
 *   user: {
 *     id: 'firebase-uid',
 *     email: '<EMAIL>',
 *     displayName: 'John Doe',
 *     emailVerified: true,
 *     lastLoginAt: '2024-01-01T10:00:00.000Z'
 *   },
 *   currentWorkspace: {
 *     id: 'workspace-id',
 *     name: 'My Company',
 *     slug: 'my-company',
 *     ownerId: 'firebase-uid',
 *     plan: 'pro',
 *     userRole: 'admin'
 *   },
 *   currentProfile: {
 *     id: 'profile-id',
 *     userId: 'firebase-uid',
 *     workspaceId: 'workspace-id',
 *     displayName: 'John Doe',
 *     role: 'admin',
 *     permissions: ['read', 'write', 'admin']
 *   },
 *   workspaces: [...],
 *   token: {
 *     idToken: 'firebase-id-token',
 *     expirationTime: 1640995200000
 *   }
 * }
 * 
 * const response = await $fetch('/api/auth/session', {
 *   method: 'POST',
 *   body: sessionPayload
 * })
 * 
 * // Response (success)
 * {
 *   success: true,
 *   data: {
 *     user: { ... },
 *     currentWorkspace: { ... },
 *     currentProfile: { ... },
 *     workspaces: [...],
 *     sessionId: 'session-uuid',
 *     createdAt: '2024-01-01T10:00:00.000Z',
 *     expiresAt: '2024-01-01T18:00:00.000Z',
 *     lastActiveAt: '2024-01-01T10:00:00.000Z',
 *     token: { ... }
 *   },
 *   message: 'Session created successfully'
 * }
 * ```
 * 
 * @see {@link https://developer.mozilla.org/en-US/docs/Web/HTTP/Cookies#Secure_and_HttpOnly_cookies} HTTP-only Cookies
 * @see {@link createSession} Session creation utilities
 * @see {@link updateUserSession} Session update utilities
 * @see {@link SessionPayload} Session data structure
 * @see {@link ErrorHandler} Error handling and logging
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [Session API] Creating/updating session')
    
    const body = await readBody(event) as SessionPayload
    
    // Validate required fields
    if (!body.user || !body.currentWorkspace || !body.currentProfile) {
      throw new Error('Missing required session data')
    }
    
    // Check if this is an update or new session
    const existingSession = getCookie(event, 'omni-session')
    
    let userSession
    if (existingSession) {
      // Update existing session
      userSession = await updateUserSession(event, body)
    } else {
      // Create new session
      userSession = await createSession(event, body)
    }
    
    console.log('🔥 [Session API] Session created/updated successfully')
    
    return {
      success: true,
      data: userSession,
      message: 'Session created successfully'
    }
    
  } catch (error) {
    console.error('🔥 [Session API] Error handling session:', error)
    const appError = ErrorHandler.handle(error, 'Failed to create session')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})