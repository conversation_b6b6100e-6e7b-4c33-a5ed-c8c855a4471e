import { sessionMiddleware, requirePermission, requireRole } from '../../middleware/session'
import { <PERSON>rrorHandler } from '../../../utils/error-handler'

/**
 * Example protected route demonstrating session middleware usage
 * GET /api/examples/protected-route
 */
export default defineEventHandler(async (event) => {
  try {
    console.log('🔥 [Protected Route] Processing request')
    
    // Validate session first
    await sessionMiddleware(event)
    
    // Check specific permissions
    await requirePermission('data.read')(event)
    
    // Optional: Check specific roles
    // await requireRole(['admin', 'manager'])(event)
    
    // Access session context
    const user = event.context.user
    const workspace = event.context.workspace
    const profile = event.context.profile
    const session = event.context.session
    
    console.log('🔥 [Protected Route] Access granted for user:', user?.email)
    
    return {
      success: true,
      data: {
        message: 'Access granted to protected resource',
        user: {
          id: user?.id,
          email: user?.email,
          displayName: user?.displayName
        },
        workspace: {
          id: workspace?.id,
          name: workspace?.name,
          plan: workspace?.plan
        },
        profile: {
          id: profile?.id,
          role: profile?.role,
          permissions: profile?.permissions
        },
        session: {
          id: session?.sessionId,
          createdAt: session?.createdAt.toDate().toISOString(),
          expiresAt: session?.expiresAt.toDate().toISOString()
        }
      }
    }
    
  } catch (error) {
    console.error('🔥 [Protected Route] Error:', error)
    
    // Handle createError objects
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }
    
    const appError = ErrorHandler.handle(error, 'Failed to access protected resource')
    
    throw createError({
      statusCode: appError.statusCode || 500,
      statusMessage: appError.message
    })
  }
})