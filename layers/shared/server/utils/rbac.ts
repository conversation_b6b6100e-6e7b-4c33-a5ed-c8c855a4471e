import { getFirestore } from 'firebase-admin/firestore'
import { type H3Event, createError } from 'h3'
import { initializeFirebaseAdmin } from './firebase-admin'
import type { 
  WorkspaceRole, 
  Permission, 
  ROLE_PERMISSIONS, 
  Profile,
  SessionWorkspace,
  SessionProfile
} from '~/types/auth'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../utils/error-handler'

/**
 * Enhanced Role-Based Access Control (RBAC) System
 * Provides comprehensive permission management with hierarchical roles
 */

/**
 * Permission check result with detailed context
 */
export interface PermissionCheckResult {
  hasPermission: boolean
  reason: string
  context: {
    userId: string
    workspaceId: string
    profileId: string
    role: WorkspaceRole
    permissions: Permission[]
    checkedPermission: Permission
    inherited: boolean
  }
}

/**
 * Role hierarchy definition (higher numbers = more permissions)
 */
export const ROLE_HIERARCHY: Record<WorkspaceRole, number> = {
  guest: 0,
  viewer: 1,
  member: 2,
  manager: 3,
  admin: 4,
  owner: 5
}

/**
 * Permission inheritance rules
 */
export const PERMISSION_INHERITANCE: Record<Permission, Permission[]> = {
  // User management inheritance
  'users.delete': ['users.update', 'users.read'],
  'users.update': ['users.read'],
  'users.create': ['users.read'],
  'users.invite': ['users.read'],
  
  // Workspace management inheritance
  'workspace.delete': ['workspace.update', 'workspace.read'],
  'workspace.update': ['workspace.read'],
  'workspace.billing': ['workspace.read'],
  'workspace.settings': ['workspace.read'],
  
  // Project management inheritance
  'projects.delete': ['projects.update', 'projects.read'],
  'projects.update': ['projects.read'],
  'projects.create': ['projects.read'],
  'projects.share': ['projects.read'],
  
  // Data access inheritance
  'data.delete': ['data.update', 'data.read'],
  'data.update': ['data.read'],
  'data.create': ['data.read'],
  'data.export': ['data.read'],
  
  // AI features inheritance
  'ai.admin': ['ai.use'],
  
  // Integrations inheritance
  'integrations.delete': ['integrations.update', 'integrations.read'],
  'integrations.update': ['integrations.read'],
  'integrations.create': ['integrations.read'],
  
  // Analytics inheritance
  'reports.create': ['analytics.read'],
  'reports.export': ['analytics.read']
}

/**
 * Role-based permission mappings with inheritance support
 */
export const ENHANCED_ROLE_PERMISSIONS: Record<WorkspaceRole, Permission[]> = {
  owner: [
    'users.read', 'users.create', 'users.update', 'users.delete', 'users.invite',
    'workspace.read', 'workspace.update', 'workspace.delete', 'workspace.billing', 'workspace.settings',
    'projects.read', 'projects.create', 'projects.update', 'projects.delete', 'projects.share',
    'data.read', 'data.create', 'data.update', 'data.delete', 'data.export',
    'ai.use', 'ai.admin',
    'integrations.read', 'integrations.create', 'integrations.update', 'integrations.delete',
    'analytics.read', 'reports.create', 'reports.export'
  ],
  admin: [
    'users.read', 'users.create', 'users.update', 'users.invite',
    'workspace.read', 'workspace.update', 'workspace.settings',
    'projects.read', 'projects.create', 'projects.update', 'projects.delete', 'projects.share',
    'data.read', 'data.create', 'data.update', 'data.delete', 'data.export',
    'ai.use', 'ai.admin',
    'integrations.read', 'integrations.create', 'integrations.update', 'integrations.delete',
    'analytics.read', 'reports.create', 'reports.export'
  ],
  manager: [
    'users.read', 'users.invite',
    'workspace.read',
    'projects.read', 'projects.create', 'projects.update', 'projects.share',
    'data.read', 'data.create', 'data.update', 'data.export',
    'ai.use',
    'integrations.read',
    'analytics.read', 'reports.create'
  ],
  member: [
    'users.read',
    'workspace.read',
    'projects.read', 'projects.create', 'projects.update',
    'data.read', 'data.create', 'data.update',
    'ai.use',
    'analytics.read'
  ],
  viewer: [
    'users.read',
    'workspace.read',
    'projects.read',
    'data.read',
    'analytics.read'
  ],
  guest: [
    'workspace.read',
    'projects.read',
    'data.read'
  ]
}

/**
 * Get all permissions for a role including inherited permissions
 */
export function getPermissionsForRole(role: WorkspaceRole): Permission[] {
  const basePermissions = ENHANCED_ROLE_PERMISSIONS[role] || []
  const inheritedPermissions = new Set<Permission>()
  
  // Add base permissions
  basePermissions.forEach(permission => {
    inheritedPermissions.add(permission)
    
    // Add inherited permissions
    const inherited = PERMISSION_INHERITANCE[permission] || []
    inherited.forEach(inheritedPerm => {
      inheritedPermissions.add(inheritedPerm)
    })
  })
  
  return Array.from(inheritedPermissions)
}

/**
 * Check if a role has sufficient hierarchy level
 */
export function hasRoleHierarchy(userRole: WorkspaceRole, requiredRole: WorkspaceRole): boolean {
  return ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[requiredRole]
}

/**
 * Get user's profile in a workspace
 */
export async function getUserProfile(userId: string, workspaceId: string): Promise<SessionProfile | null> {
  try {
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const profileQuery = await db.collection('profiles')
      .where('userId', '==', userId)
      .where('workspaceId', '==', workspaceId)
      .where('deletedAt', '==', null)
      .limit(1)
      .get()
    
    if (profileQuery.empty) {
      return null
    }
    
    const profileDoc = profileQuery.docs[0]
    const profileData = profileDoc.data()
    
    return {
      id: profileDoc.id,
      userId: profileData.userId,
      workspaceId: profileData.workspaceId,
      displayName: profileData.displayName || '',
      avatar: profileData.avatar,
      role: profileData.role as WorkspaceRole,
      permissions: profileData.permissions || [],
      createdAt: profileData.createdAt?.toDate()?.toISOString() || new Date().toISOString(),
      lastActiveAt: profileData.lastActiveAt?.toDate()?.toISOString()
    }
  } catch (error) {
    console.error('🔐 [RBAC] Error getting user profile:', error)
    throw ErrorHandler.handle(error, 'Failed to get user profile')
  }
}

/**
 * Check if user has specific permission in workspace
 */
export async function checkPermission(
  userId: string,
  workspaceId: string,
  permission: Permission
): Promise<PermissionCheckResult> {
  try {
    console.log('🔐 [RBAC] Checking permission:', { userId, workspaceId, permission })
    
    const profile = await getUserProfile(userId, workspaceId)
    if (!profile) {
      return {
        hasPermission: false,
        reason: 'User profile not found in workspace',
        context: {
          userId,
          workspaceId,
          profileId: '',
          role: 'guest',
          permissions: [],
          checkedPermission: permission,
          inherited: false
        }
      }
    }
    
    // Get all permissions for user's role (including inherited)
    const rolePermissions = getPermissionsForRole(profile.role)
    
    // Check custom permissions
    const customPermissions = profile.permissions || []
    const allPermissions = [...rolePermissions, ...customPermissions]
    
    const hasPermission = allPermissions.includes(permission)
    const inherited = rolePermissions.includes(permission) && !ENHANCED_ROLE_PERMISSIONS[profile.role].includes(permission)
    
    return {
      hasPermission,
      reason: hasPermission 
        ? `Permission granted via ${inherited ? 'inheritance' : 'role'}`
        : 'Permission denied',
      context: {
        userId,
        workspaceId,
        profileId: profile.id,
        role: profile.role,
        permissions: allPermissions,
        checkedPermission: permission,
        inherited
      }
    }
  } catch (error) {
    console.error('🔐 [RBAC] Error checking permission:', error)
    throw ErrorHandler.handle(error, 'Failed to check permission')
  }
}

/**
 * Check multiple permissions at once
 */
export async function checkPermissions(
  userId: string,
  workspaceId: string,
  permissions: Permission[]
): Promise<Record<Permission, PermissionCheckResult>> {
  const results: Record<Permission, PermissionCheckResult> = {}
  
  for (const permission of permissions) {
    results[permission] = await checkPermission(userId, workspaceId, permission)
  }
  
  return results
}

/**
 * Middleware for permission checking
 */
export function requirePermission(permission: Permission) {
  return async (event: H3Event) => {
    try {
      const { user, workspace } = event.context
      
      if (!user || !workspace) {
        throw createError({
          statusCode: 401,
          statusMessage: 'Authentication required'
        })
      }
      
      const permissionResult = await checkPermission(user.id, workspace.id, permission)
      
      if (!permissionResult.hasPermission) {
        throw createError({
          statusCode: 403,
          statusMessage: `Permission denied: ${permission}`,
          data: { 
            required: permission, 
            reason: permissionResult.reason,
            userRole: permissionResult.context.role
          }
        })
      }
      
      // Add permission context to event
      event.context.permissionContext = permissionResult.context
      
    } catch (error) {
      console.error('🔐 [RBAC] Permission check failed:', error)
      throw error
    }
  }
}

/**
 * Role-based middleware
 */
export function requireRole(role: WorkspaceRole) {
  return async (event: H3Event) => {
    try {
      const { user, workspace } = event.context
      
      if (!user || !workspace) {
        throw createError({
          statusCode: 401,
          statusMessage: 'Authentication required'
        })
      }
      
      const profile = await getUserProfile(user.id, workspace.id)
      if (!profile) {
        throw createError({
          statusCode: 403,
          statusMessage: 'Access denied: Profile not found'
        })
      }
      
      const hasRole = hasRoleHierarchy(profile.role, role)
      if (!hasRole) {
        throw createError({
          statusCode: 403,
          statusMessage: `Access denied: Requires ${role} role or higher`,
          data: { 
            required: role, 
            current: profile.role,
            hierarchy: ROLE_HIERARCHY[profile.role]
          }
        })
      }
      
      // Add role context to event
      event.context.roleContext = {
        role: profile.role,
        hierarchy: ROLE_HIERARCHY[profile.role],
        permissions: getPermissionsForRole(profile.role)
      }
      
    } catch (error) {
      console.error('🔐 [RBAC] Role check failed:', error)
      throw error
    }
  }
}

/**
 * Grant custom permission to user
 */
export async function grantPermission(
  userId: string,
  workspaceId: string,
  permission: Permission,
  grantedBy: string
): Promise<void> {
  try {
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const profile = await getUserProfile(userId, workspaceId)
    if (!profile) {
      throw new Error('User profile not found')
    }
    
    const profileRef = db.collection('profiles').doc(profile.id)
    const currentPermissions = profile.permissions || []
    
    if (!currentPermissions.includes(permission)) {
      await profileRef.update({
        permissions: [...currentPermissions, permission],
        updatedAt: new Date()
      })
      
      console.log('🔐 [RBAC] Permission granted:', { userId, workspaceId, permission, grantedBy })
    }
    
  } catch (error) {
    console.error('🔐 [RBAC] Error granting permission:', error)
    throw ErrorHandler.handle(error, 'Failed to grant permission')
  }
}

/**
 * Revoke custom permission from user
 */
export async function revokePermission(
  userId: string,
  workspaceId: string,
  permission: Permission,
  revokedBy: string
): Promise<void> {
  try {
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const profile = await getUserProfile(userId, workspaceId)
    if (!profile) {
      throw new Error('User profile not found')
    }
    
    const profileRef = db.collection('profiles').doc(profile.id)
    const currentPermissions = profile.permissions || []
    
    const updatedPermissions = currentPermissions.filter(p => p !== permission)
    
    await profileRef.update({
      permissions: updatedPermissions,
      updatedAt: new Date()
    })
    
    console.log('🔐 [RBAC] Permission revoked:', { userId, workspaceId, permission, revokedBy })
    
  } catch (error) {
    console.error('🔐 [RBAC] Error revoking permission:', error)
    throw ErrorHandler.handle(error, 'Failed to revoke permission')
  }
}

/**
 * Update user role in workspace
 */
export async function updateUserRole(
  userId: string,
  workspaceId: string,
  newRole: WorkspaceRole,
  updatedBy: string
): Promise<void> {
  try {
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const profile = await getUserProfile(userId, workspaceId)
    if (!profile) {
      throw new Error('User profile not found')
    }
    
    const profileRef = db.collection('profiles').doc(profile.id)
    
    await profileRef.update({
      role: newRole,
      updatedAt: new Date()
    })
    
    console.log('🔐 [RBAC] Role updated:', { userId, workspaceId, oldRole: profile.role, newRole, updatedBy })
    
  } catch (error) {
    console.error('🔐 [RBAC] Error updating role:', error)
    throw ErrorHandler.handle(error, 'Failed to update role')
  }
}

/**
 * Get workspace members with their roles and permissions
 */
export async function getWorkspaceMembers(workspaceId: string): Promise<{
  id: string
  userId: string
  displayName: string
  avatar?: string
  role: WorkspaceRole
  permissions: Permission[]
  lastActiveAt?: string
}[]> {
  try {
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const profilesQuery = await db.collection('profiles')
      .where('workspaceId', '==', workspaceId)
      .where('deletedAt', '==', null)
      .get()
    
    const members = profilesQuery.docs.map(doc => {
      const data = doc.data()
      const rolePermissions = getPermissionsForRole(data.role as WorkspaceRole)
      const customPermissions = data.permissions || []
      
      return {
        id: doc.id,
        userId: data.userId,
        displayName: data.displayName || '',
        avatar: data.avatar,
        role: data.role as WorkspaceRole,
        permissions: [...rolePermissions, ...customPermissions],
        lastActiveAt: data.lastActiveAt?.toDate()?.toISOString()
      }
    })
    
    return members
  } catch (error) {
    console.error('🔐 [RBAC] Error getting workspace members:', error)
    throw ErrorHandler.handle(error, 'Failed to get workspace members')
  }
}

/**
 * Utilities for permission checking
 */
export const RBACUtils = {
  /**
   * Check if user can perform action on resource
   */
  canPerformAction: async (
    userId: string,
    workspaceId: string,
    action: string,
    resource: string
  ): Promise<boolean> => {
    const permission = `${resource}.${action}` as Permission
    const result = await checkPermission(userId, workspaceId, permission)
    return result.hasPermission
  },
  
  /**
   * Get user's effective permissions (role + custom)
   */
  getEffectivePermissions: async (
    userId: string,
    workspaceId: string
  ): Promise<Permission[]> => {
    const profile = await getUserProfile(userId, workspaceId)
    if (!profile) return []
    
    const rolePermissions = getPermissionsForRole(profile.role)
    const customPermissions = profile.permissions || []
    
    return [...new Set([...rolePermissions, ...customPermissions])]
  },
  
  /**
   * Validate permission exists
   */
  isValidPermission: (permission: string): permission is Permission => {
    const allPermissions = new Set<Permission>()
    
    Object.values(ENHANCED_ROLE_PERMISSIONS).forEach(permissions => {
      permissions.forEach(p => allPermissions.add(p))
    })
    
    return allPermissions.has(permission as Permission)
  }
}