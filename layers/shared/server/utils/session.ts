import { type H3<PERSON><PERSON>, getCookie, set<PERSON><PERSON>ie, delete<PERSON><PERSON>ie, getHeader } from 'h3'
import { getAuth } from 'firebase-admin/auth'
import { getFirestore } from 'firebase-admin/firestore'
import { Timestamp } from 'firebase-admin/firestore'
import { sign, verify } from 'jsonwebtoken'
import { createHash, randomUUID } from 'crypto'
import type { 
  ServerSession, 
  SessionPayload, 
  SessionValidationResult, 
  SessionConfig, 
  SessionUser, 
  SessionWorkspace, 
  SessionProfile,
  UserSession,
  DEFAULT_SESSION_CONFIG
} from '~/types/session'
import { initializeFirebaseAdmin } from './firebase-admin'
import { ErrorHandler } from '../../utils/error-handler'

/**
 * Session Management Utilities
 * Handles HTTP-only cookie sessions with Firebase token validation
 */

// Session configuration
const SESSION_CONFIG: SessionConfig = {
  cookieName: 'omni-session',
  maxAge: 60 * 60 * 24 * 7, // 7 days
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax',
  path: '/'
}

// JWT secret for session token signing
const JWT_SECRET = process.env.JWT_SECRET || 'your-session-secret-key'

/**
 * Create a secure session with HTTP-only cookies
 */
export async function createSession(
  event: H3Event,
  payload: SessionPayload
): Promise<UserSession> {
  try {
    console.log('🔥 [Session] Creating session for user:', payload.user.id)
    
    // Generate session ID
    const sessionId = generateSessionId()
    const now = new Date()
    const expiresAt = new Date(now.getTime() + SESSION_CONFIG.maxAge * 1000)
    
    // Get request metadata
    const ipAddress = getClientIP(event) || 'unknown'
    const userAgent = getHeader(event, 'user-agent') || 'unknown'
    
    // Create server session data
    const serverSession: ServerSession = {
      sessionId,
      userId: payload.user.id,
      currentWorkspaceId: payload.currentWorkspace.id,
      currentProfileId: payload.currentProfile.id,
      ipAddress,
      userAgent,
      createdAt: Timestamp.fromDate(now),
      lastActiveAt: Timestamp.fromDate(now),
      expiresAt: Timestamp.fromDate(expiresAt),
      firebaseToken: {
        idToken: payload.token?.idToken || '',
        expirationTime: payload.token?.expirationTime || 0
      }
    }
    
    // Store session in Firestore
    await storeServerSession(serverSession)
    
    // Create signed session token
    const sessionToken = sign(
      { sessionId, userId: payload.user.id },
      JWT_SECRET,
      { expiresIn: SESSION_CONFIG.maxAge }
    )
    
    // Set HTTP-only cookie
    setCookie(event, SESSION_CONFIG.cookieName, sessionToken, {
      httpOnly: SESSION_CONFIG.httpOnly,
      secure: SESSION_CONFIG.secure,
      sameSite: SESSION_CONFIG.sameSite,
      maxAge: SESSION_CONFIG.maxAge,
      path: SESSION_CONFIG.path
    })
    
    // Create user session response
    const userSession: UserSession = {
      user: payload.user,
      isAuthenticated: true,
      isLoading: false,
      currentWorkspace: payload.currentWorkspace,
      currentProfile: payload.currentProfile,
      workspaces: payload.workspaces,
      sessionId,
      createdAt: now.toISOString(),
      expiresAt: expiresAt.toISOString(),
      lastActiveAt: now.toISOString(),
      token: payload.token,
      error: null
    }
    
    console.log('🔥 [Session] Session created successfully:', sessionId)
    return userSession
    
  } catch (error) {
    console.error('🔥 [Session] Error creating session:', error)
    throw ErrorHandler.handle(error, 'Failed to create session')
  }
}

/**
 * Validate session from HTTP-only cookie
 */
export async function validateSession(event: H3Event): Promise<SessionValidationResult> {
  try {
    console.log('🔥 [Session] Validating session')
    
    // Get session cookie
    const sessionToken = getCookie(event, SESSION_CONFIG.cookieName)
    if (!sessionToken) {
      console.log('🔥 [Session] No session cookie found')
      return { isValid: false, error: 'No session cookie' }
    }
    
    // Verify JWT token
    let decoded: any
    try {
      decoded = verify(sessionToken, JWT_SECRET)
    } catch (error) {
      console.error('🔥 [Session] JWT verification failed:', error)
      return { isValid: false, error: 'Invalid session token' }
    }
    
    // Get server session from Firestore
    const serverSession = await getServerSession(decoded.sessionId)
    if (!serverSession) {
      console.log('🔥 [Session] Server session not found')
      return { isValid: false, error: 'Session not found' }
    }
    
    // Check if session is expired
    const now = new Date()
    if (serverSession.expiresAt.toDate() < now) {
      console.log('🔥 [Session] Session expired')
      await deleteServerSession(decoded.sessionId)
      return { isValid: false, error: 'Session expired' }
    }
    
    // Validate Firebase token
    const firebaseValidation = await validateFirebaseToken(serverSession.firebaseToken.idToken)
    if (!firebaseValidation.isValid) {
      console.log('🔥 [Session] Firebase token validation failed')
      return { isValid: false, error: 'Invalid Firebase token' }
    }
    
    // Get user, workspace, and profile data
    const [user, workspace, profile] = await Promise.all([
      getUserData(serverSession.userId),
      getWorkspaceData(serverSession.currentWorkspaceId),
      getProfileData(serverSession.currentProfileId)
    ])
    
    if (!user || !workspace || !profile) {
      console.log('🔥 [Session] Failed to load session data')
      return { isValid: false, error: 'Session data not found' }
    }
    
    // Update last active time
    await updateSessionActivity(serverSession.sessionId)
    
    console.log('🔥 [Session] Session validated successfully:', serverSession.sessionId)
    return {
      isValid: true,
      session: serverSession,
      user,
      workspace,
      profile
    }
    
  } catch (error) {
    console.error('🔥 [Session] Error validating session:', error)
    return { isValid: false, error: 'Session validation failed' }
  }
}

/**
 * Update session with new data
 */
export async function updateUserSession(
  event: H3Event,
  updates: Partial<SessionPayload>
): Promise<UserSession> {
  try {
    console.log('🔥 [Session] Updating session')
    
    // Validate current session
    const validation = await validateSession(event)
    if (!validation.isValid || !validation.session) {
      throw new Error('Invalid session for update')
    }
    
    // Update server session
    const updateData: Partial<ServerSession> = {
      lastActiveAt: Timestamp.fromDate(new Date())
    }
    
    if (updates.currentWorkspace) {
      updateData.currentWorkspaceId = updates.currentWorkspace.id
    }
    
    if (updates.currentProfile) {
      updateData.currentProfileId = updates.currentProfile.id
    }
    
    if (updates.token?.idToken) {
      updateData.firebaseToken = {
        idToken: updates.token.idToken,
        expirationTime: updates.token.expirationTime || 0
      }
    }
    
    await updateServerSession(validation.session.sessionId, updateData)
    
    // Create updated user session
    const userSession: UserSession = {
      user: updates.user || validation.user!,
      isAuthenticated: true,
      isLoading: false,
      currentWorkspace: updates.currentWorkspace || validation.workspace!,
      currentProfile: updates.currentProfile || validation.profile!,
      workspaces: updates.workspaces || [validation.workspace!],
      sessionId: validation.session.sessionId,
      createdAt: validation.session.createdAt.toDate().toISOString(),
      expiresAt: validation.session.expiresAt.toDate().toISOString(),
      lastActiveAt: new Date().toISOString(),
      token: updates.token || null,
      error: null
    }
    
    console.log('🔥 [Session] Session updated successfully')
    return userSession
    
  } catch (error) {
    console.error('🔥 [Session] Error updating session:', error)
    throw ErrorHandler.handle(error, 'Failed to update session')
  }
}

/**
 * Destroy session and clear cookies
 */
export async function destroySession(event: H3Event): Promise<void> {
  try {
    console.log('🔥 [Session] Destroying session')
    
    // Get session cookie
    const sessionToken = getCookie(event, SESSION_CONFIG.cookieName)
    if (sessionToken) {
      try {
        const decoded = verify(sessionToken, JWT_SECRET) as any
        await deleteServerSession(decoded.sessionId)
      } catch (error) {
        console.warn('🔥 [Session] Error decoding session for destruction:', error)
      }
    }
    
    // Clear session cookie
    deleteCookie(event, SESSION_CONFIG.cookieName, {
      path: SESSION_CONFIG.path
    })
    
    console.log('🔥 [Session] Session destroyed successfully')
    
  } catch (error) {
    console.error('🔥 [Session] Error destroying session:', error)
    throw ErrorHandler.handle(error, 'Failed to destroy session')
  }
}

/**
 * Switch workspace context
 */
export async function switchWorkspace(
  event: H3Event,
  workspaceId: string
): Promise<UserSession> {
  try {
    console.log('🔥 [Session] Switching workspace to:', workspaceId)
    
    // Validate current session
    const validation = await validateSession(event)
    if (!validation.isValid || !validation.session) {
      throw new Error('Invalid session for workspace switch')
    }
    
    // Get new workspace and profile data
    const [workspace, profile] = await Promise.all([
      getWorkspaceData(workspaceId),
      getProfileData(validation.session.userId, workspaceId)
    ])
    
    if (!workspace || !profile) {
      throw new Error('Workspace or profile not found')
    }
    
    // Update session with new workspace context
    return await updateUserSession(event, {
      currentWorkspace: workspace,
      currentProfile: profile
    })
    
  } catch (error) {
    console.error('🔥 [Session] Error switching workspace:', error)
    throw ErrorHandler.handle(error, 'Failed to switch workspace')
  }
}

// Helper functions

/**
 * Store server session in Firestore
 */
async function storeServerSession(session: ServerSession): Promise<void> {
  const app = await initializeFirebaseAdmin()
  const db = getFirestore(app)
  
  await db.collection('sessions').doc(session.sessionId).set(session)
}

/**
 * Get server session from Firestore
 */
async function getServerSession(sessionId: string): Promise<ServerSession | null> {
  const app = await initializeFirebaseAdmin()
  const db = getFirestore(app)
  
  const doc = await db.collection('sessions').doc(sessionId).get()
  return doc.exists ? doc.data() as ServerSession : null
}

/**
 * Update server session in Firestore
 */
async function updateServerSession(
  sessionId: string,
  updates: Partial<ServerSession>
): Promise<void> {
  const app = await initializeFirebaseAdmin()
  const db = getFirestore(app)
  
  await db.collection('sessions').doc(sessionId).update(updates)
}

/**
 * Delete server session from Firestore
 */
async function deleteServerSession(sessionId: string): Promise<void> {
  const app = await initializeFirebaseAdmin()
  const db = getFirestore(app)
  
  await db.collection('sessions').doc(sessionId).delete()
}

/**
 * Update session activity timestamp
 */
async function updateSessionActivity(sessionId: string): Promise<void> {
  await updateServerSession(sessionId, {
    lastActiveAt: Timestamp.fromDate(new Date())
  })
}

/**
 * Validate Firebase ID token
 */
async function validateFirebaseToken(idToken: string): Promise<{ isValid: boolean; error?: string }> {
  try {
    const app = await initializeFirebaseAdmin()
    const auth = getAuth(app)
    
    await auth.verifyIdToken(idToken)
    return { isValid: true }
  } catch (error) {
    console.error('🔥 [Session] Firebase token validation error:', error)
    return { isValid: false, error: 'Invalid Firebase token' }
  }
}

/**
 * Get user data for session
 */
async function getUserData(userId: string): Promise<SessionUser | null> {
  try {
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const doc = await db.collection('users').doc(userId).get()
    if (!doc.exists) return null
    
    const userData = doc.data()
    return {
      id: userId,
      email: userData?.email || '',
      displayName: userData?.displayName || '',
      photoURL: userData?.photoURL,
      emailVerified: userData?.emailVerified || false,
      lastLoginAt: userData?.lastLoginAt?.toDate()?.toISOString()
    }
  } catch (error) {
    console.error('🔥 [Session] Error getting user data:', error)
    return null
  }
}

/**
 * Get workspace data for session
 */
async function getWorkspaceData(workspaceId: string): Promise<SessionWorkspace | null> {
  try {
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const doc = await db.collection('workspaces').doc(workspaceId).get()
    if (!doc.exists) return null
    
    const workspaceData = doc.data()
    return {
      id: workspaceId,
      name: workspaceData?.name || '',
      slug: workspaceData?.slug || '',
      ownerId: workspaceData?.ownerId || '',
      plan: workspaceData?.plan || 'free',
      userRole: 'member', // This would be determined by the profile
      createdAt: workspaceData?.createdAt?.toDate()?.toISOString() || new Date().toISOString(),
      updatedAt: workspaceData?.updatedAt?.toDate()?.toISOString() || new Date().toISOString()
    }
  } catch (error) {
    console.error('🔥 [Session] Error getting workspace data:', error)
    return null
  }
}

/**
 * Get profile data for session
 */
async function getProfileData(profileId: string): Promise<SessionProfile | null>
async function getProfileData(userId: string, workspaceId: string): Promise<SessionProfile | null>
async function getProfileData(
  profileIdOrUserId: string,
  workspaceId?: string
): Promise<SessionProfile | null> {
  try {
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    let query
    if (workspaceId) {
      // Search by userId and workspaceId
      query = db.collection('profiles')
        .where('userId', '==', profileIdOrUserId)
        .where('workspaceId', '==', workspaceId)
        .limit(1)
      
      const snapshot = await query.get()
      if (snapshot.empty) return null
      
      const doc = snapshot.docs[0]
      const profileData = doc.data()
      
      return {
        id: doc.id,
        userId: profileData?.userId || '',
        workspaceId: profileData?.workspaceId || '',
        displayName: profileData?.displayName || '',
        avatar: profileData?.avatar,
        role: profileData?.role || 'member',
        permissions: profileData?.permissions || [],
        createdAt: profileData?.createdAt?.toDate()?.toISOString() || new Date().toISOString(),
        lastActiveAt: profileData?.lastActiveAt?.toDate()?.toISOString()
      }
    } else {
      // Search by profileId
      const doc = await db.collection('profiles').doc(profileIdOrUserId).get()
      if (!doc.exists) return null
      
      const profileData = doc.data()
      return {
        id: profileIdOrUserId,
        userId: profileData?.userId || '',
        workspaceId: profileData?.workspaceId || '',
        displayName: profileData?.displayName || '',
        avatar: profileData?.avatar,
        role: profileData?.role || 'member',
        permissions: profileData?.permissions || [],
        createdAt: profileData?.createdAt?.toDate()?.toISOString() || new Date().toISOString(),
        lastActiveAt: profileData?.lastActiveAt?.toDate()?.toISOString()
      }
    }
  } catch (error) {
    console.error('🔥 [Session] Error getting profile data:', error)
    return null
  }
}

/**
 * Generate secure session ID
 */
function generateSessionId(): string {
  return randomUUID()
}

/**
 * Get client IP address
 */
function getClientIP(event: H3Event): string | undefined {
  const xForwardedFor = getHeader(event, 'x-forwarded-for')
  const xRealIp = getHeader(event, 'x-real-ip')
  
  if (xForwardedFor) {
    return xForwardedFor.split(',')[0].trim()
  }
  
  if (xRealIp) {
    return xRealIp
  }
  
  return event.node.req.socket.remoteAddress
}

/**
 * Clean up expired sessions (should be called periodically)
 */
export async function cleanupExpiredSessions(): Promise<void> {
  try {
    console.log('🔥 [Session] Cleaning up expired sessions')
    
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const now = Timestamp.fromDate(new Date())
    const expiredSessions = await db.collection('sessions')
      .where('expiresAt', '<', now)
      .limit(100)
      .get()
    
    const deletePromises = expiredSessions.docs.map(doc => doc.ref.delete())
    await Promise.all(deletePromises)
    
    console.log(`🔥 [Session] Cleaned up ${expiredSessions.size} expired sessions`)
    
  } catch (error) {
    console.error('🔥 [Session] Error cleaning up expired sessions:', error)
  }
}

/**
 * Get session statistics
 */
export async function getSessionStats(): Promise<{
  totalSessions: number;
  activeSessions: number;
  expiredSessions: number;
}> {
  try {
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const now = Timestamp.fromDate(new Date())
    
    const [totalSnapshot, activeSnapshot, expiredSnapshot] = await Promise.all([
      db.collection('sessions').count().get(),
      db.collection('sessions').where('expiresAt', '>', now).count().get(),
      db.collection('sessions').where('expiresAt', '<', now).count().get()
    ])
    
    return {
      totalSessions: totalSnapshot.data().count,
      activeSessions: activeSnapshot.data().count,
      expiredSessions: expiredSnapshot.data().count
    }
    
  } catch (error) {
    console.error('🔥 [Session] Error getting session stats:', error)
    return { totalSessions: 0, activeSessions: 0, expiredSessions: 0 }
  }
}