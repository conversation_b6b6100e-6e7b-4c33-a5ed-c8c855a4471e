import { getFirestore } from 'firebase-admin/firestore'
import { initializeFirebaseAdmin } from './firebase-admin'
import { generateEmbedding, generateBatchEmbeddings } from './vertex-ai'
import { <PERSON>rror<PERSON>and<PERSON> } from '../../utils/error-handler'
import type { H3Event } from 'h3'

/**
 * Vector Embeddings Utilities
 * Handles vector storage and retrieval for AI features
 */

export interface VectorEmbedding {
  id: string
  content: string
  embedding: number[]
  metadata: {
    workspaceId: string
    userId: string
    profileId: string
    type: 'document' | 'conversation' | 'knowledge' | 'context'
    source?: string
    tags?: string[]
    version?: string
    language?: string
    dimensions?: number
    model?: string
    createdAt: string
    updatedAt: string
  }
}

export interface EmbeddingQuery {
  workspaceId: string
  userId?: string
  type?: VectorEmbedding['metadata']['type']
  tags?: string[]
  limit?: number
  threshold?: number
}

export interface EmbeddingSearchResult {
  embedding: VectorEmbedding
  similarity: number
}

/**
 * Store vector embedding with session context
 */
export async function storeEmbedding(
  event: H3Event,
  content: string,
  embedding: number[],
  metadata: Partial<VectorEmbedding['metadata']>
): Promise<VectorEmbedding> {
  try {
    console.log('🔥 [Embeddings] Storing embedding for workspace:', event.context.workspace?.id)
    
    // Ensure session context is available
    if (!event.context.session || !event.context.user || !event.context.workspace || !event.context.profile) {
      throw new Error('Session context required for embedding storage')
    }
    
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const now = new Date().toISOString()
    const embeddingData: VectorEmbedding = {
      id: generateEmbeddingId(),
      content,
      embedding,
      metadata: {
        workspaceId: event.context.workspace.id,
        userId: event.context.user.id,
        profileId: event.context.profile.id,
        type: metadata.type || 'document',
        source: metadata.source,
        tags: metadata.tags || [],
        createdAt: now,
        updatedAt: now
      }
    }
    
    // Store in Firestore
    await db.collection('embeddings').doc(embeddingData.id).set(embeddingData)
    
    console.log('🔥 [Embeddings] Embedding stored successfully:', embeddingData.id)
    return embeddingData
    
  } catch (error) {
    console.error('🔥 [Embeddings] Error storing embedding:', error)
    throw ErrorHandler.handle(error, 'Failed to store embedding')
  }
}

/**
 * Search embeddings using vector similarity
 */
export async function searchEmbeddings(
  event: H3Event,
  queryEmbedding: number[],
  options: EmbeddingQuery = {}
): Promise<EmbeddingSearchResult[]> {
  try {
    console.log('🔥 [Embeddings] Searching embeddings for workspace:', event.context.workspace?.id)
    
    // Ensure session context is available
    if (!event.context.session || !event.context.workspace) {
      throw new Error('Session context required for embedding search')
    }
    
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    // Build query constraints
    let query = db.collection('embeddings')
      .where('metadata.workspaceId', '==', event.context.workspace.id)
    
    if (options.userId) {
      query = query.where('metadata.userId', '==', options.userId)
    }
    
    if (options.type) {
      query = query.where('metadata.type', '==', options.type)
    }
    
    if (options.tags && options.tags.length > 0) {
      query = query.where('metadata.tags', 'array-contains-any', options.tags)
    }
    
    // Execute query
    const snapshot = await query.get()
    
    if (snapshot.empty) {
      console.log('🔥 [Embeddings] No embeddings found')
      return []
    }
    
    // Calculate similarity scores
    const results: EmbeddingSearchResult[] = []
    const threshold = options.threshold || 0.7
    
    snapshot.forEach(doc => {
      const embedding = doc.data() as VectorEmbedding
      const similarity = cosineSimilarity(queryEmbedding, embedding.embedding)
      
      if (similarity >= threshold) {
        results.push({ embedding, similarity })
      }
    })
    
    // Sort by similarity (descending) and limit results
    results.sort((a, b) => b.similarity - a.similarity)
    
    const limit = options.limit || 10
    const limitedResults = results.slice(0, limit)
    
    console.log(`🔥 [Embeddings] Found ${limitedResults.length} relevant embeddings`)
    return limitedResults
    
  } catch (error) {
    console.error('🔥 [Embeddings] Error searching embeddings:', error)
    throw ErrorHandler.handle(error, 'Failed to search embeddings')
  }
}

/**
 * Get embedding by ID
 */
export async function getEmbedding(
  event: H3Event,
  embeddingId: string
): Promise<VectorEmbedding | null> {
  try {
    console.log('🔥 [Embeddings] Getting embedding:', embeddingId)
    
    // Ensure session context is available
    if (!event.context.session || !event.context.workspace) {
      throw new Error('Session context required for embedding retrieval')
    }
    
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const doc = await db.collection('embeddings').doc(embeddingId).get()
    
    if (!doc.exists) {
      console.log('🔥 [Embeddings] Embedding not found:', embeddingId)
      return null
    }
    
    const embedding = doc.data() as VectorEmbedding
    
    // Verify workspace access
    if (embedding.metadata.workspaceId !== event.context.workspace.id) {
      console.log('🔥 [Embeddings] Access denied to embedding:', embeddingId)
      return null
    }
    
    console.log('🔥 [Embeddings] Embedding retrieved successfully:', embeddingId)
    return embedding
    
  } catch (error) {
    console.error('🔥 [Embeddings] Error getting embedding:', error)
    throw ErrorHandler.handle(error, 'Failed to get embedding')
  }
}

/**
 * Update embedding content and metadata
 */
export async function updateEmbedding(
  event: H3Event,
  embeddingId: string,
  updates: {
    content?: string
    embedding?: number[]
    metadata?: Partial<VectorEmbedding['metadata']>
  }
): Promise<VectorEmbedding | null> {
  try {
    console.log('🔥 [Embeddings] Updating embedding:', embeddingId)
    
    // Ensure session context is available
    if (!event.context.session || !event.context.workspace) {
      throw new Error('Session context required for embedding update')
    }
    
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    // Get existing embedding
    const doc = await db.collection('embeddings').doc(embeddingId).get()
    
    if (!doc.exists) {
      console.log('🔥 [Embeddings] Embedding not found for update:', embeddingId)
      return null
    }
    
    const existingEmbedding = doc.data() as VectorEmbedding
    
    // Verify workspace access
    if (existingEmbedding.metadata.workspaceId !== event.context.workspace.id) {
      console.log('🔥 [Embeddings] Access denied to update embedding:', embeddingId)
      return null
    }
    
    // Prepare updates
    const updateData: Partial<VectorEmbedding> = {
      ...updates,
      metadata: {
        ...existingEmbedding.metadata,
        ...updates.metadata,
        updatedAt: new Date().toISOString()
      }
    }
    
    // Update in Firestore
    await db.collection('embeddings').doc(embeddingId).update(updateData)
    
    // Return updated embedding
    const updatedDoc = await db.collection('embeddings').doc(embeddingId).get()
    const updatedEmbedding = updatedDoc.data() as VectorEmbedding
    
    console.log('🔥 [Embeddings] Embedding updated successfully:', embeddingId)
    return updatedEmbedding
    
  } catch (error) {
    console.error('🔥 [Embeddings] Error updating embedding:', error)
    throw ErrorHandler.handle(error, 'Failed to update embedding')
  }
}

/**
 * Delete embedding
 */
export async function deleteEmbedding(
  event: H3Event,
  embeddingId: string
): Promise<boolean> {
  try {
    console.log('🔥 [Embeddings] Deleting embedding:', embeddingId)
    
    // Ensure session context is available
    if (!event.context.session || !event.context.workspace) {
      throw new Error('Session context required for embedding deletion')
    }
    
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    // Get existing embedding to verify access
    const doc = await db.collection('embeddings').doc(embeddingId).get()
    
    if (!doc.exists) {
      console.log('🔥 [Embeddings] Embedding not found for deletion:', embeddingId)
      return false
    }
    
    const embedding = doc.data() as VectorEmbedding
    
    // Verify workspace access
    if (embedding.metadata.workspaceId !== event.context.workspace.id) {
      console.log('🔥 [Embeddings] Access denied to delete embedding:', embeddingId)
      return false
    }
    
    // Delete from Firestore
    await db.collection('embeddings').doc(embeddingId).delete()
    
    console.log('🔥 [Embeddings] Embedding deleted successfully:', embeddingId)
    return true
    
  } catch (error) {
    console.error('🔥 [Embeddings] Error deleting embedding:', error)
    throw ErrorHandler.handle(error, 'Failed to delete embedding')
  }
}

/**
 * Get embeddings for a workspace
 */
export async function getWorkspaceEmbeddings(
  event: H3Event,
  options: EmbeddingQuery = {}
): Promise<VectorEmbedding[]> {
  try {
    console.log('🔥 [Embeddings] Getting workspace embeddings')
    
    // Ensure session context is available
    if (!event.context.session || !event.context.workspace) {
      throw new Error('Session context required for workspace embeddings')
    }
    
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    // Build query constraints
    let query = db.collection('embeddings')
      .where('metadata.workspaceId', '==', event.context.workspace.id)
      .orderBy('metadata.createdAt', 'desc')
    
    if (options.userId) {
      query = query.where('metadata.userId', '==', options.userId)
    }
    
    if (options.type) {
      query = query.where('metadata.type', '==', options.type)
    }
    
    if (options.tags && options.tags.length > 0) {
      query = query.where('metadata.tags', 'array-contains-any', options.tags)
    }
    
    if (options.limit) {
      query = query.limit(options.limit)
    }
    
    // Execute query
    const snapshot = await query.get()
    
    const embeddings: VectorEmbedding[] = []
    snapshot.forEach(doc => {
      embeddings.push(doc.data() as VectorEmbedding)
    })
    
    console.log(`🔥 [Embeddings] Found ${embeddings.length} workspace embeddings`)
    return embeddings
    
  } catch (error) {
    console.error('🔥 [Embeddings] Error getting workspace embeddings:', error)
    throw ErrorHandler.handle(error, 'Failed to get workspace embeddings')
  }
}

/**
 * Get embedding statistics for a workspace
 */
export async function getEmbeddingStats(
  event: H3Event
): Promise<{
  totalEmbeddings: number
  embeddingsByType: Record<string, number>
  embeddingsByUser: Record<string, number>
  recentActivity: number
}> {
  try {
    console.log('🔥 [Embeddings] Getting embedding statistics')
    
    // Ensure session context is available
    if (!event.context.session || !event.context.workspace) {
      throw new Error('Session context required for embedding statistics')
    }
    
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    // Get all embeddings for workspace
    const snapshot = await db.collection('embeddings')
      .where('metadata.workspaceId', '==', event.context.workspace.id)
      .get()
    
    const totalEmbeddings = snapshot.size
    const embeddingsByType: Record<string, number> = {}
    const embeddingsByUser: Record<string, number> = {}
    
    let recentActivity = 0
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
    
    snapshot.forEach(doc => {
      const embedding = doc.data() as VectorEmbedding
      
      // Count by type
      embeddingsByType[embedding.metadata.type] = (embeddingsByType[embedding.metadata.type] || 0) + 1
      
      // Count by user
      embeddingsByUser[embedding.metadata.userId] = (embeddingsByUser[embedding.metadata.userId] || 0) + 1
      
      // Count recent activity
      if (embedding.metadata.createdAt > oneDayAgo) {
        recentActivity++
      }
    })
    
    console.log(`🔥 [Embeddings] Statistics: ${totalEmbeddings} total embeddings`)
    return {
      totalEmbeddings,
      embeddingsByType,
      embeddingsByUser,
      recentActivity
    }
    
  } catch (error) {
    console.error('🔥 [Embeddings] Error getting embedding statistics:', error)
    throw ErrorHandler.handle(error, 'Failed to get embedding statistics')
  }
}

/**
 * Clean up old embeddings
 */
export async function cleanupOldEmbeddings(
  event: H3Event,
  maxAge: number = 90 * 24 * 60 * 60 * 1000 // 90 days
): Promise<number> {
  try {
    console.log('🔥 [Embeddings] Cleaning up old embeddings')
    
    // Ensure session context is available
    if (!event.context.session || !event.context.workspace) {
      throw new Error('Session context required for embedding cleanup')
    }
    
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const cutoffDate = new Date(Date.now() - maxAge).toISOString()
    
    // Get old embeddings
    const snapshot = await db.collection('embeddings')
      .where('metadata.workspaceId', '==', event.context.workspace.id)
      .where('metadata.createdAt', '<', cutoffDate)
      .limit(100) // Process in batches
      .get()
    
    if (snapshot.empty) {
      console.log('🔥 [Embeddings] No old embeddings to clean up')
      return 0
    }
    
    // Delete old embeddings
    const deletePromises = snapshot.docs.map(doc => doc.ref.delete())
    await Promise.all(deletePromises)
    
    const deletedCount = snapshot.size
    console.log(`🔥 [Embeddings] Cleaned up ${deletedCount} old embeddings`)
    return deletedCount
    
  } catch (error) {
    console.error('🔥 [Embeddings] Error cleaning up old embeddings:', error)
    throw ErrorHandler.handle(error, 'Failed to clean up old embeddings')
  }
}

// Helper functions

/**
 * Generate unique embedding ID
 */
function generateEmbeddingId(): string {
  return `emb_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Calculate cosine similarity between two vectors
 */
function cosineSimilarity(a: number[], b: number[]): number {
  if (a.length !== b.length) {
    throw new Error('Vectors must have the same length')
  }
  
  let dotProduct = 0
  let normA = 0
  let normB = 0
  
  for (let i = 0; i < a.length; i++) {
    dotProduct += a[i] * b[i]
    normA += a[i] * a[i]
    normB += b[i] * b[i]
  }
  
  const magnitude = Math.sqrt(normA) * Math.sqrt(normB)
  
  if (magnitude === 0) {
    return 0
  }
  
  return dotProduct / magnitude
}

/**
 * Normalize vector to unit length
 */
export function normalizeVector(vector: number[]): number[] {
  const magnitude = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0))
  
  if (magnitude === 0) {
    return vector
  }
  
  return vector.map(val => val / magnitude)
}

/**
 * Calculate Euclidean distance between two vectors
 */
export function euclideanDistance(a: number[], b: number[]): number {
  if (a.length !== b.length) {
    throw new Error('Vectors must have the same length')
  }
  
  let sum = 0
  for (let i = 0; i < a.length; i++) {
    const diff = a[i] - b[i]
    sum += diff * diff
  }
  
  return Math.sqrt(sum)
}

/**
 * Batch process embeddings
 */
export async function batchProcessEmbeddings(
  event: H3Event,
  embeddings: Array<{
    content: string
    embedding: number[]
    metadata: Partial<VectorEmbedding['metadata']>
  }>
): Promise<VectorEmbedding[]> {
  try {
    console.log(`🔥 [Embeddings] Batch processing ${embeddings.length} embeddings`)
    
    // Process in chunks to avoid overwhelming Firestore
    const chunkSize = 10
    const results: VectorEmbedding[] = []
    
    for (let i = 0; i < embeddings.length; i += chunkSize) {
      const chunk = embeddings.slice(i, i + chunkSize)
      const chunkResults = await Promise.all(
        chunk.map(embedding => 
          storeEmbedding(event, embedding.content, embedding.embedding, embedding.metadata)
        )
      )
      results.push(...chunkResults)
    }
    
    console.log(`🔥 [Embeddings] Batch processed ${results.length} embeddings`)
    return results
    
  } catch (error) {
    console.error('🔥 [Embeddings] Error batch processing embeddings:', error)
    throw ErrorHandler.handle(error, 'Failed to batch process embeddings')
  }
}

/**
 * Generate and store embedding from text
 */
export async function generateAndStoreEmbedding(
  event: H3Event,
  content: string,
  metadata: Partial<VectorEmbedding['metadata']> = {}
): Promise<VectorEmbedding> {
  try {
    console.log('🔥 [Embeddings] Generating and storing embedding')
    
    // Generate embedding using VertexAI
    const embeddingResponse = await generateEmbedding(content, {
      task_type: 'RETRIEVAL_DOCUMENT'
    })
    
    // Store with enhanced metadata
    const enhancedMetadata = {
      ...metadata,
      version: '1.0',
      dimensions: embeddingResponse.embedding.length,
      model: 'text-embedding-004'
    }
    
    return await storeEmbedding(event, content, embeddingResponse.embedding, enhancedMetadata)
    
  } catch (error) {
    console.error('🔥 [Embeddings] Error generating and storing embedding:', error)
    throw ErrorHandler.handle(error, 'Failed to generate and store embedding')
  }
}

/**
 * Batch generate and store embeddings
 */
export async function batchGenerateAndStoreEmbeddings(
  event: H3Event,
  items: Array<{
    content: string
    metadata: Partial<VectorEmbedding['metadata']>
  }>
): Promise<VectorEmbedding[]> {
  try {
    console.log(`🔥 [Embeddings] Batch generating and storing ${items.length} embeddings`)
    
    // Generate embeddings in batch
    const texts = items.map(item => item.content)
    const batchResponse = await generateBatchEmbeddings(texts, {
      task_type: 'RETRIEVAL_DOCUMENT'
    })
    
    // Store embeddings with enhanced metadata
    const storePromises = batchResponse.embeddings.map(async (embeddingResponse, index) => {
      const item = items[index]
      const enhancedMetadata = {
        ...item.metadata,
        version: '1.0',
        dimensions: embeddingResponse.embedding.length,
        model: 'text-embedding-004'
      }
      
      return await storeEmbedding(event, item.content, embeddingResponse.embedding, enhancedMetadata)
    })
    
    const results = await Promise.all(storePromises)
    
    console.log(`🔥 [Embeddings] Batch generated and stored ${results.length} embeddings`)
    return results
    
  } catch (error) {
    console.error('🔥 [Embeddings] Error batch generating and storing embeddings:', error)
    throw ErrorHandler.handle(error, 'Failed to batch generate and store embeddings')
  }
}

/**
 * Refresh embeddings for changed content
 */
export async function refreshEmbeddings(
  event: H3Event,
  embeddingIds: string[]
): Promise<VectorEmbedding[]> {
  try {
    console.log(`🔥 [Embeddings] Refreshing ${embeddingIds.length} embeddings`)
    
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    const results: VectorEmbedding[] = []
    
    for (const embeddingId of embeddingIds) {
      // Get existing embedding
      const existingEmbedding = await getEmbedding(event, embeddingId)
      if (!existingEmbedding) {
        console.warn(`🔥 [Embeddings] Embedding not found for refresh: ${embeddingId}`)
        continue
      }
      
      // Generate new embedding
      const embeddingResponse = await generateEmbedding(existingEmbedding.content, {
        task_type: 'RETRIEVAL_DOCUMENT'
      })
      
      // Update with new embedding and metadata
      const updatedEmbedding = await updateEmbedding(event, embeddingId, {
        embedding: embeddingResponse.embedding,
        metadata: {
          ...existingEmbedding.metadata,
          version: '1.0',
          dimensions: embeddingResponse.embedding.length,
          model: 'text-embedding-004'
        }
      })
      
      if (updatedEmbedding) {
        results.push(updatedEmbedding)
      }
    }
    
    console.log(`🔥 [Embeddings] Refreshed ${results.length} embeddings`)
    return results
    
  } catch (error) {
    console.error('🔥 [Embeddings] Error refreshing embeddings:', error)
    throw ErrorHandler.handle(error, 'Failed to refresh embeddings')
  }
}

/**
 * Migrate embeddings to new version
 */
export async function migrateEmbeddings(
  event: H3Event,
  fromVersion: string,
  toVersion: string
): Promise<{
  migrated: number
  errors: number
  skipped: number
}> {
  try {
    console.log(`🔥 [Embeddings] Migrating embeddings from ${fromVersion} to ${toVersion}`)
    
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    // Get embeddings with old version
    const snapshot = await db.collection('embeddings')
      .where('metadata.workspaceId', '==', event.context.workspace?.id)
      .where('metadata.version', '==', fromVersion)
      .limit(100) // Process in batches
      .get()
    
    if (snapshot.empty) {
      console.log('🔥 [Embeddings] No embeddings to migrate')
      return { migrated: 0, errors: 0, skipped: 0 }
    }
    
    let migrated = 0
    let errors = 0
    let skipped = 0
    
    for (const doc of snapshot.docs) {
      try {
        const embedding = doc.data() as VectorEmbedding
        
        // Skip if already migrated
        if (embedding.metadata.version === toVersion) {
          skipped++
          continue
        }
        
        // Generate new embedding with updated model
        const embeddingResponse = await generateEmbedding(embedding.content, {
          task_type: 'RETRIEVAL_DOCUMENT'
        })
        
        // Update embedding
        await updateEmbedding(event, embedding.id, {
          embedding: embeddingResponse.embedding,
          metadata: {
            ...embedding.metadata,
            version: toVersion,
            dimensions: embeddingResponse.embedding.length,
            model: 'text-embedding-004'
          }
        })
        
        migrated++
        
      } catch (error) {
        console.error(`🔥 [Embeddings] Error migrating embedding ${doc.id}:`, error)
        errors++
      }
    }
    
    console.log(`🔥 [Embeddings] Migration completed: ${migrated} migrated, ${errors} errors, ${skipped} skipped`)
    return { migrated, errors, skipped }
    
  } catch (error) {
    console.error('🔥 [Embeddings] Error migrating embeddings:', error)
    throw ErrorHandler.handle(error, 'Failed to migrate embeddings')
  }
}

/**
 * Get embedding performance analytics
 */
export async function getEmbeddingAnalytics(
  event: H3Event,
  dateRange?: { start: string; end: string }
): Promise<{
  totalEmbeddings: number
  embeddingsByType: Record<string, number>
  embeddingsByModel: Record<string, number>
  averageDimensions: number
  storageUsage: number
  recentActivity: Array<{
    date: string
    created: number
    updated: number
  }>
}> {
  try {
    console.log('🔥 [Embeddings] Getting embedding analytics')
    
    const app = await initializeFirebaseAdmin()
    const db = getFirestore(app)
    
    // Build query with date range if provided
    let query = db.collection('embeddings')
      .where('metadata.workspaceId', '==', event.context.workspace?.id)
    
    if (dateRange) {
      query = query
        .where('metadata.createdAt', '>=', dateRange.start)
        .where('metadata.createdAt', '<=', dateRange.end)
    }
    
    const snapshot = await query.get()
    
    const totalEmbeddings = snapshot.size
    const embeddingsByType: Record<string, number> = {}
    const embeddingsByModel: Record<string, number> = {}
    let totalDimensions = 0
    let totalStorage = 0
    const dailyActivity: Record<string, { created: number; updated: number }> = {}
    
    snapshot.forEach(doc => {
      const embedding = doc.data() as VectorEmbedding
      
      // Count by type
      embeddingsByType[embedding.metadata.type] = (embeddingsByType[embedding.metadata.type] || 0) + 1
      
      // Count by model
      const model = embedding.metadata.model || 'unknown'
      embeddingsByModel[model] = (embeddingsByModel[model] || 0) + 1
      
      // Calculate dimensions
      totalDimensions += embedding.embedding.length
      
      // Calculate storage usage (approximate)
      totalStorage += embedding.content.length + (embedding.embedding.length * 8) // 8 bytes per float
      
      // Track daily activity
      const createdDate = embedding.metadata.createdAt.split('T')[0]
      if (!dailyActivity[createdDate]) {
        dailyActivity[createdDate] = { created: 0, updated: 0 }
      }
      dailyActivity[createdDate].created++
      
      const updatedDate = embedding.metadata.updatedAt.split('T')[0]
      if (updatedDate !== createdDate) {
        if (!dailyActivity[updatedDate]) {
          dailyActivity[updatedDate] = { created: 0, updated: 0 }
        }
        dailyActivity[updatedDate].updated++
      }
    })
    
    // Convert daily activity to array
    const recentActivity = Object.entries(dailyActivity)
      .map(([date, activity]) => ({ date, ...activity }))
      .sort((a, b) => a.date.localeCompare(b.date))
      .slice(-30) // Last 30 days
    
    const analytics = {
      totalEmbeddings,
      embeddingsByType,
      embeddingsByModel,
      averageDimensions: totalEmbeddings > 0 ? Math.round(totalDimensions / totalEmbeddings) : 0,
      storageUsage: totalStorage,
      recentActivity
    }
    
    console.log('🔥 [Embeddings] Analytics generated successfully')
    return analytics
    
  } catch (error) {
    console.error('🔥 [Embeddings] Error getting embedding analytics:', error)
    throw ErrorHandler.handle(error, 'Failed to get embedding analytics')
  }
}