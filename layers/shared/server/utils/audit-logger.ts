import { getFirestore, Timestamp } from 'firebase-admin/firestore'
import { type H3E<PERSON>, getHeader } from 'h3'
import { randomUUID } from 'crypto'
import { initializeFirebaseAdmin } from './firebase-admin'
import type { AuditLogEntry, WorkspaceRole, Permission } from '~/types/auth'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../utils/error-handler'

/**
 * Advanced Audit Logging System
 * Comprehensive tracking of security events and user actions
 * 
 * This system provides enterprise-grade audit logging capabilities with:
 * - Comprehensive event tracking (auth, data, security, system)
 * - Risk scoring and threat detection
 * - Compliance support (GDPR, SOC2)
 * - Advanced querying and analytics
 * - Automated security alerting
 * - Retention policy management
 * 
 * @example
 * ```typescript
 * // Log a login event
 * await auditLogger.logEvent('auth.login', {
 *   event: h3Event,
 *   userId: 'user123',
 *   workspaceId: 'ws456',
 *   sessionId: 'session789'
 * })
 * 
 * // Query audit logs
 * const logs = await auditLogger.queryLogs({
 *   workspaceId: 'ws456',
 *   eventType: 'auth.login',
 *   startDate: new Date('2024-01-01'),
 *   limit: 50
 * })
 * ```
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */

/**
 * Audit event types for comprehensive security and operational monitoring
 * 
 * Events are categorized into logical groups for better organization:
 * - Authentication: Login, logout, password changes
 * - Authorization: Permission changes, role assignments
 * - User Management: User lifecycle events
 * - Workspace Management: Workspace operations
 * - Data Operations: CRUD operations on business data
 * - Security: Threat detection and security events
 * - System: Operational and maintenance events
 * 
 * @example
 * ```typescript
 * // Authentication events
 * 'auth.login' | 'auth.logout' | 'auth.login_failed'
 * 
 * // Security events
 * 'security.suspicious_activity' | 'security.rate_limit_exceeded'
 * 
 * // Data events
 * 'data.created' | 'data.updated' | 'data.deleted'
 * ```
 */
export type AuditEventType = 
  // Authentication events
  | 'auth.login'
  | 'auth.logout'
  | 'auth.login_failed'
  | 'auth.password_changed'
  | 'auth.password_reset'
  | 'auth.account_locked'
  | 'auth.account_unlocked'
  | 'auth.session_expired'
  | 'auth.token_refreshed'
  
  // Authorization events
  | 'auth.permission_granted'
  | 'auth.permission_revoked'
  | 'auth.role_changed'
  | 'auth.access_denied'
  | 'auth.privilege_escalation'
  
  // User management events
  | 'user.created'
  | 'user.updated'
  | 'user.deleted'
  | 'user.invited'
  | 'user.invitation_accepted'
  | 'user.invitation_declined'
  | 'user.removed_from_workspace'
  
  // Workspace events
  | 'workspace.created'
  | 'workspace.updated'
  | 'workspace.deleted'
  | 'workspace.settings_changed'
  | 'workspace.member_added'
  | 'workspace.member_removed'
  | 'workspace.ownership_transferred'
  
  // Data events
  | 'data.created'
  | 'data.read'
  | 'data.updated'
  | 'data.deleted'
  | 'data.exported'
  | 'data.imported'
  | 'data.backup_created'
  | 'data.backup_restored'
  
  // Security events
  | 'security.suspicious_activity'
  | 'security.rate_limit_exceeded'
  | 'security.ip_blocked'
  | 'security.csrf_detected'
  | 'security.xss_attempt'
  | 'security.sql_injection_attempt'
  | 'security.configuration_changed'
  | 'security.encryption_key_rotated'
  | 'security.request_validated'
  
  // System events
  | 'system.error'
  | 'system.maintenance'
  | 'system.backup'
  | 'system.update'
  | 'system.integration_connected'
  | 'system.integration_disconnected'

/**
 * Audit event severity levels for risk assessment and alerting
 * 
 * Severity levels help prioritize security responses:
 * - low: Normal operations (logins, data reads)
 * - medium: Important changes (password resets, permissions)
 * - high: Critical operations (account locks, deletions)
 * - critical: Security threats (privilege escalation, attacks)
 * 
 * @example
 * ```typescript
 * const severity: AuditSeverity = 'high'
 * if (severity === 'critical') {
 *   await triggerSecurityAlert(auditEntry)
 * }
 * ```
 */
export type AuditSeverity = 'low' | 'medium' | 'high' | 'critical'

/**
 * Enhanced audit log entry with comprehensive metadata
 * 
 * Extends the base AuditLogEntry with advanced features:
 * - Risk scoring and threat detection
 * - Compliance tracking (GDPR, SOC2)
 * - Detailed change tracking
 * - Geolocation and device information
 * - Correlation IDs for distributed tracing
 * 
 * @example
 * ```typescript
 * const entry: EnhancedAuditLogEntry = {
 *   id: 'audit-123',
 *   eventType: 'auth.login',
 *   severity: 'low',
 *   userId: 'user123',
 *   workspaceId: 'ws456',
 *   metadata: {
 *     risk: { score: 15, factors: ['new_ip'] },
 *     device: { type: 'desktop', os: 'macOS' }
 *   },
 *   compliance: {
 *     regulation: ['GDPR', 'SOC2'],
 *     retention: 2555,
 *     encrypted: true
 *   }
 * }
 * ```
 */
export interface EnhancedAuditLogEntry extends AuditLogEntry {
  // Event categorization
  eventType: AuditEventType
  severity: AuditSeverity
  category: string
  
  // Enhanced context
  sessionId?: string
  requestId?: string
  correlationId?: string
  
  // Additional metadata
  metadata: {
    method?: string
    path?: string
    statusCode?: number
    responseTime?: number
    userAgent?: string
    referer?: string
    fingerprint?: string
    geoLocation?: {
      country?: string
      city?: string
      region?: string
      latitude?: number
      longitude?: number
    }
    device?: {
      type?: string
      os?: string
      browser?: string
      version?: string
    }
    risk?: {
      score: number
      factors: string[]
    }
  }
  
  // Compliance tracking
  compliance?: {
    regulation: string[]
    retention: number // days
    encrypted: boolean
  }
  
  // Change tracking
  changeDetails?: {
    field: string
    oldValue: any
    newValue: any
    changeType: 'create' | 'update' | 'delete'
  }[]
}

/**
 * Audit query filters for advanced log searching and analysis
 * 
 * Supports complex queries with multiple filter criteria:
 * - Temporal filtering (date ranges)
 * - Entity filtering (workspace, user, event type)
 * - Severity and category filtering
 * - Pagination and sorting
 * 
 * @example
 * ```typescript
 * const filters: AuditQueryFilters = {
 *   workspaceId: 'ws456',
 *   eventType: 'auth.login_failed',
 *   severity: 'medium',
 *   startDate: new Date('2024-01-01'),
 *   endDate: new Date('2024-01-31'),
 *   limit: 100,
 *   sortBy: 'createdAt',
 *   sortOrder: 'desc'
 * }
 * ```
 */
export interface AuditQueryFilters {
  workspaceId?: string
  userId?: string
  eventType?: AuditEventType
  severity?: AuditSeverity
  category?: string
  startDate?: Date
  endDate?: Date
  limit?: number
  offset?: number
  sortBy?: 'createdAt' | 'severity' | 'eventType'
  sortOrder?: 'asc' | 'desc'
}

/**
 * Audit query result with pagination support
 * 
 * Provides structured query results with metadata:
 * - Paginated entries
 * - Total count information
 * - Pagination state (hasMore, nextOffset)
 * 
 * @example
 * ```typescript
 * const result: AuditQueryResult = {
 *   entries: [...auditEntries],
 *   total: 150,
 *   hasMore: true,
 *   nextOffset: 50
 * }
 * ```
 */
export interface AuditQueryResult {
  entries: EnhancedAuditLogEntry[]
  total: number
  hasMore: boolean
  nextOffset?: number
}

/**
 * Risk scoring configuration for threat assessment
 * 
 * Defines risk factors and their severity scores:
 * - IP-based risks (new/suspicious IPs, VPNs, Tor)
 * - Location-based risks (new countries, high-risk regions)
 * - Behavior-based risks (failed logins, rapid actions)
 * - Device-based risks (new devices, suspicious agents)
 * - Pattern-based risks (brute force, credential stuffing)
 * 
 * Risk scores are cumulative and trigger alerts when exceeding thresholds.
 * 
 * @example
 * ```typescript
 * const riskScore = RISK_FACTORS.new_ip + RISK_FACTORS.unusual_time
 * if (riskScore > 40) {
 *   await triggerSecurityAlert(auditEntry)
 * }
 * ```
 */
const RISK_FACTORS = {
  // IP-based risks
  new_ip: 10,
  suspicious_ip: 25,
  tor_exit_node: 50,
  vpn_detected: 5,
  
  // Location-based risks
  new_country: 15,
  high_risk_country: 20,
  
  // Behavior-based risks
  multiple_failed_logins: 30,
  unusual_time: 10,
  rapid_actions: 20,
  privilege_escalation: 40,
  
  // Device-based risks
  new_device: 15,
  suspicious_user_agent: 25,
  
  // Pattern-based risks
  brute_force: 50,
  credential_stuffing: 45,
  session_hijacking: 60
}

/**
 * Event severity mapping for automated risk classification
 * 
 * Maps each audit event type to its default severity level:
 * - Authentication events: Mostly low, failed attempts medium
 * - Authorization events: Medium to high based on impact
 * - Data events: Low to medium based on sensitivity
 * - Security events: High to critical based on threat level
 * - System events: Low to medium for operational events
 * 
 * @example
 * ```typescript
 * const severity = EVENT_SEVERITY_MAP['auth.login_failed'] // 'medium'
 * const criticalSeverity = EVENT_SEVERITY_MAP['security.sql_injection_attempt'] // 'critical'
 * ```
 */
const EVENT_SEVERITY_MAP: Record<AuditEventType, AuditSeverity> = {
  // Authentication events
  'auth.login': 'low',
  'auth.logout': 'low',
  'auth.login_failed': 'medium',
  'auth.password_changed': 'medium',
  'auth.password_reset': 'medium',
  'auth.account_locked': 'high',
  'auth.account_unlocked': 'high',
  'auth.session_expired': 'low',
  'auth.token_refreshed': 'low',
  
  // Authorization events
  'auth.permission_granted': 'medium',
  'auth.permission_revoked': 'medium',
  'auth.role_changed': 'high',
  'auth.access_denied': 'medium',
  'auth.privilege_escalation': 'critical',
  
  // User management events
  'user.created': 'low',
  'user.updated': 'low',
  'user.deleted': 'high',
  'user.invited': 'low',
  'user.invitation_accepted': 'low',
  'user.invitation_declined': 'low',
  'user.removed_from_workspace': 'medium',
  
  // Workspace events
  'workspace.created': 'low',
  'workspace.updated': 'low',
  'workspace.deleted': 'critical',
  'workspace.settings_changed': 'medium',
  'workspace.member_added': 'low',
  'workspace.member_removed': 'medium',
  'workspace.ownership_transferred': 'high',
  
  // Data events
  'data.created': 'low',
  'data.read': 'low',
  'data.updated': 'low',
  'data.deleted': 'medium',
  'data.exported': 'medium',
  'data.imported': 'medium',
  'data.backup_created': 'low',
  'data.backup_restored': 'high',
  
  // Security events
  'security.suspicious_activity': 'high',
  'security.rate_limit_exceeded': 'medium',
  'security.ip_blocked': 'high',
  'security.csrf_detected': 'high',
  'security.xss_attempt': 'high',
  'security.sql_injection_attempt': 'critical',
  'security.configuration_changed': 'high',
  'security.encryption_key_rotated': 'medium',
  'security.request_validated': 'low',
  
  // System events
  'system.error': 'medium',
  'system.maintenance': 'low',
  'system.backup': 'low',
  'system.update': 'low',
  'system.integration_connected': 'low',
  'system.integration_disconnected': 'low'
}

/**
 * Get client IP address with comprehensive proxy support
 * 
 * Handles various proxy headers in order of preference:
 * 1. CF-Connecting-IP (Cloudflare)
 * 2. X-Real-IP (Nginx)
 * 3. X-Forwarded-For (Load balancers)
 * 4. Direct socket connection
 * 
 * @param event - H3 event object
 * @returns Client IP address or 'unknown' if not determinable
 * 
 * @example
 * ```typescript
 * const clientIP = getClientIP(event)
 * if (clientIP !== 'unknown') {
 *   await checkIPReputation(clientIP)
 * }
 * ```
 */
function getClientIP(event: H3Event): string {
  const xForwardedFor = getHeader(event, 'x-forwarded-for')
  const xRealIp = getHeader(event, 'x-real-ip')
  const cfConnectingIp = getHeader(event, 'cf-connecting-ip')
  
  if (cfConnectingIp) return cfConnectingIp
  if (xRealIp) return xRealIp
  if (xForwardedFor) return xForwardedFor.split(',')[0].trim()
  
  return event.node.req.socket.remoteAddress || 'unknown'
}

/**
 * Parse user agent for device and browser information
 * 
 * Extracts device characteristics for security analysis:
 * - Device type (desktop, mobile, tablet)
 * - Operating system (Windows, macOS, Linux, iOS, Android)
 * - Browser type (Chrome, Firefox, Safari, Edge)
 * - Version information (when available)
 * 
 * Note: Basic implementation - production should use ua-parser-js
 * 
 * @param userAgent - User agent string from HTTP headers
 * @returns Device information object
 * 
 * @example
 * ```typescript
 * const device = parseUserAgent(userAgent)
 * if (device.type === 'mobile' && device.os === 'unknown') {
 *   riskScore += RISK_FACTORS.suspicious_user_agent
 * }
 * ```
 */
function parseUserAgent(userAgent: string): { type: string; os: string; browser: string; version: string } {
  // Basic user agent parsing (in production, use a proper library like ua-parser-js)
  const device = {
    type: 'desktop',
    os: 'unknown',
    browser: 'unknown',
    version: 'unknown'
  }
  
  if (userAgent.includes('Mobile')) device.type = 'mobile'
  if (userAgent.includes('Tablet')) device.type = 'tablet'
  
  if (userAgent.includes('Windows')) device.os = 'Windows'
  else if (userAgent.includes('Mac')) device.os = 'macOS'
  else if (userAgent.includes('Linux')) device.os = 'Linux'
  else if (userAgent.includes('Android')) device.os = 'Android'
  else if (userAgent.includes('iOS')) device.os = 'iOS'
  
  if (userAgent.includes('Chrome')) device.browser = 'Chrome'
  else if (userAgent.includes('Firefox')) device.browser = 'Firefox'
  else if (userAgent.includes('Safari')) device.browser = 'Safari'
  else if (userAgent.includes('Edge')) device.browser = 'Edge'
  
  return device
}

/**
 * Calculate risk score based on event context and patterns
 * 
 * Analyzes multiple risk factors to generate a security score:
 * - Failed actions (login failures, access denied)
 * - IP reputation and location
 * - Time-based patterns (unusual hours)
 * - Device characteristics (suspicious user agents)
 * - Behavioral patterns (rapid requests)
 * 
 * @param event - H3 event object
 * @param eventType - Type of audit event
 * @param context - Additional context data
 * @returns Risk assessment with score and contributing factors
 * 
 * @example
 * ```typescript
 * const risk = calculateRiskScore(event, 'auth.login_failed', {})
 * if (risk.score > 40) {
 *   console.log('High risk factors:', risk.factors)
 * }
 * ```
 */
function calculateRiskScore(event: H3Event, eventType: AuditEventType, context: any): { score: number; factors: string[] } {
  const factors: string[] = []
  let score = 0
  
  // Add base risk for event type
  if (eventType.includes('failed') || eventType.includes('denied')) {
    score += 10
    factors.push('failed_action')
  }
  
  // Add IP-based risks (placeholder - implement actual IP reputation checks)
  const clientIP = getClientIP(event)
  if (clientIP === 'unknown') {
    score += 5
    factors.push('unknown_ip')
  }
  
  // Add time-based risks
  const hour = new Date().getHours()
  if (hour < 6 || hour > 22) {
    score += RISK_FACTORS.unusual_time
    factors.push('unusual_time')
  }
  
  // Add device-based risks
  const userAgent = getHeader(event, 'user-agent') || ''
  if (!userAgent || userAgent.length < 50) {
    score += RISK_FACTORS.suspicious_user_agent
    factors.push('suspicious_user_agent')
  }
  
  return { score, factors }
}

/**
 * Enhanced audit logger class with singleton pattern
 * 
 * Provides comprehensive audit logging capabilities:
 * - Event logging with automatic risk scoring
 * - Query and analytics functions
 * - Statistics and trend analysis
 * - Cleanup and retention management
 * - Security alerting for high-risk events
 * 
 * Uses singleton pattern to ensure consistent logging across the application.
 * 
 * @example
 * ```typescript
 * const logger = AuditLogger.getInstance()
 * 
 * // Log an event
 * await logger.logEvent('auth.login', {
 *   event: h3Event,
 *   userId: 'user123',
 *   workspaceId: 'ws456'
 * })
 * 
 * // Query logs
 * const logs = await logger.queryLogs({
 *   workspaceId: 'ws456',
 *   severity: 'high'
 * })
 * ```
 */
export class AuditLogger {
  private static instance: AuditLogger
  private db: FirebaseFirestore.Firestore | null = null
  
  private constructor() {}
  
  public static getInstance(): AuditLogger {
    if (!AuditLogger.instance) {
      AuditLogger.instance = new AuditLogger()
    }
    return AuditLogger.instance
  }
  
  private async getFirestore(): Promise<FirebaseFirestore.Firestore> {
    if (!this.db) {
      const app = await initializeFirebaseAdmin()
      this.db = getFirestore(app)
    }
    return this.db
  }
  
  /**
   * Log an audit event with comprehensive metadata and risk assessment
   * 
   * Creates a detailed audit entry with:
   * - Event classification and severity
   * - Risk scoring and threat detection
   * - Client information (IP, device, location)
   * - Compliance metadata
   * - Change tracking (before/after states)
   * - Correlation IDs for distributed tracing
   * 
   * @param eventType - The type of audit event
   * @param context - Event context and metadata
   * @returns Promise that resolves when event is logged
   * 
   * @example
   * ```typescript
   * await auditLogger.logEvent('auth.login', {
   *   event: h3Event,
   *   userId: 'user123',
   *   workspaceId: 'ws456',
   *   sessionId: 'session789',
   *   metadata: { loginMethod: 'google' }
   * })
   * 
   * // Data modification event
   * await auditLogger.logEvent('data.updated', {
   *   userId: 'user123',
   *   workspaceId: 'ws456',
   *   resource: 'user_profile',
   *   resourceId: 'profile123',
   *   before: { name: 'Old Name' },
   *   after: { name: 'New Name' }
   * })
   * ```
   */
  async logEvent(
    eventType: AuditEventType,
    context: {
      event?: H3Event
      userId?: string
      workspaceId?: string
      profileId?: string
      resource?: string
      resourceId?: string
      action?: string
      before?: any
      after?: any
      sessionId?: string
      requestId?: string
      correlationId?: string
      metadata?: Record<string, any>
    }
  ): Promise<void> {
    try {
      const db = await this.getFirestore()
      const now = Timestamp.now()
      
      // Generate IDs
      const auditId = randomUUID()
      const requestId = context.requestId || randomUUID()
      
      // Get client information
      const clientIP = context.event ? getClientIP(context.event) : 'system'
      const userAgent = context.event ? getHeader(context.event, 'user-agent') || '' : 'system'
      const referer = context.event ? getHeader(context.event, 'referer') || '' : ''
      
      // Calculate risk score
      const risk = context.event ? calculateRiskScore(context.event, eventType, context) : { score: 0, factors: [] }
      
      // Parse device information
      const device = parseUserAgent(userAgent)
      
      // Determine severity
      const severity = EVENT_SEVERITY_MAP[eventType] || 'medium'
      
      // Build audit entry
      const auditEntry: EnhancedAuditLogEntry = {
        id: auditId,
        workspaceId: context.workspaceId || '',
        userId: context.userId || '',
        profileId: context.profileId || '',
        
        // Event details
        eventType,
        severity,
        category: eventType.split('.')[0],
        action: context.action || eventType,
        resource: context.resource || '',
        resourceId: context.resourceId || '',
        
        // Change tracking
        before: context.before || null,
        after: context.after || null,
        
        // Context
        sessionId: context.sessionId,
        requestId,
        correlationId: context.correlationId,
        ipAddress: clientIP,
        userAgent,
        location: '', // Placeholder for geo-location
        
        // Enhanced metadata
        metadata: {
          method: context.event?.node.req.method,
          path: context.event?.node.req.url,
          referer,
          device,
          risk,
          ...context.metadata
        },
        
        // Compliance
        compliance: {
          regulation: ['GDPR', 'SOC2'],
          retention: 2555, // 7 years in days
          encrypted: true
        },
        
        // Change details
        changeDetails: context.before && context.after 
          ? this.generateChangeDetails(context.before, context.after)
          : undefined,
        
        // Timestamps
        createdAt: now
      }
      
      // Store in Firestore
      await db.collection('audit_logs').doc(auditId).set(auditEntry)
      
      // Log to console for immediate visibility
      console.log(`🔍 [Audit] ${eventType}:`, {
        userId: context.userId,
        workspaceId: context.workspaceId,
        severity,
        risk: risk.score,
        ip: clientIP
      })
      
      // Trigger alerts for high-risk events
      if (risk.score > 40 || severity === 'critical') {
        await this.triggerSecurityAlert(auditEntry)
      }
      
    } catch (error) {
      console.error('🔍 [Audit] Failed to log event:', error)
      // Don't throw - audit logging shouldn't break the main flow
    }
  }
  
  /**
   * Generate detailed change tracking for audit trail
   * 
   * Compares before and after states to create a detailed change log:
   * - Field-level change detection
   * - Change type classification (create, update, delete)
   * - Value comparison with deep object support
   * - Sensitive data handling
   * 
   * @param before - Previous state of the object
   * @param after - New state of the object
   * @returns Array of change details
   * 
   * @example
   * ```typescript
   * const changes = generateChangeDetails(
   *   { name: 'John', email: '<EMAIL>' },
   *   { name: 'John', email: '<EMAIL>', role: 'admin' }
   * )
   * // Result: [
   * //   { field: 'email', oldValue: '<EMAIL>', newValue: '<EMAIL>', changeType: 'update' },
   * //   { field: 'role', oldValue: undefined, newValue: 'admin', changeType: 'create' }
   * // ]
   * ```
   */
  private generateChangeDetails(before: any, after: any): EnhancedAuditLogEntry['changeDetails'] {
    const changes: EnhancedAuditLogEntry['changeDetails'] = []
    
    if (typeof before === 'object' && typeof after === 'object') {
      const allKeys = new Set([...Object.keys(before), ...Object.keys(after)])
      
      for (const key of allKeys) {
        const oldValue = before[key]
        const newValue = after[key]
        
        if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {
          changes?.push({
            field: key,
            oldValue,
            newValue,
            changeType: oldValue === undefined ? 'create' : newValue === undefined ? 'delete' : 'update'
          })
        }
      }
    }
    
    return changes
  }
  
  /**
   * Trigger security alert for high-risk events
   * 
   * Handles security incident response for critical events:
   * - Console logging for immediate visibility
   * - Persistent alert storage in Firestore
   * - Integration points for external alerting systems
   * - Alert metadata for incident response
   * 
   * Triggered automatically when:
   * - Risk score exceeds 40
   * - Event severity is 'critical'
   * - Suspicious patterns are detected
   * 
   * @param auditEntry - The audit entry that triggered the alert
   * @returns Promise that resolves when alert is processed
   * 
   * @example
   * ```typescript
   * // Automatically triggered for high-risk events
   * if (risk.score > 40 || severity === 'critical') {
   *   await this.triggerSecurityAlert(auditEntry)
   * }
   * ```
   */
  private async triggerSecurityAlert(auditEntry: EnhancedAuditLogEntry): Promise<void> {
    try {
      // In production, integrate with alerting systems (Slack, PagerDuty, etc.)
      console.warn('🚨 [Security Alert]', {
        eventType: auditEntry.eventType,
        severity: auditEntry.severity,
        riskScore: auditEntry.metadata.risk?.score,
        userId: auditEntry.userId,
        workspaceId: auditEntry.workspaceId,
        ipAddress: auditEntry.ipAddress,
        timestamp: auditEntry.createdAt
      })
      
      // Store security alert
      const db = await this.getFirestore()
      await db.collection('security_alerts').add({
        auditId: auditEntry.id,
        eventType: auditEntry.eventType,
        severity: auditEntry.severity,
        riskScore: auditEntry.metadata.risk?.score || 0,
        userId: auditEntry.userId,
        workspaceId: auditEntry.workspaceId,
        ipAddress: auditEntry.ipAddress,
        createdAt: auditEntry.createdAt,
        resolved: false
      })
      
    } catch (error) {
      console.error('🚨 [Security Alert] Failed to trigger alert:', error)
    }
  }
  
  /**
   * Query audit logs with advanced filtering and pagination
   * 
   * Supports complex queries with multiple filter criteria:
   * - Workspace and user filtering
   * - Event type and severity filtering
   * - Date range queries
   * - Sorting and pagination
   * - Performance optimization with indexes
   * 
   * @param filters - Query filter criteria
   * @returns Promise resolving to paginated query results
   * 
   * @example
   * ```typescript
   * // Query failed login attempts
   * const failedLogins = await auditLogger.queryLogs({
   *   workspaceId: 'ws456',
   *   eventType: 'auth.login_failed',
   *   startDate: new Date('2024-01-01'),
   *   endDate: new Date('2024-01-31'),
   *   limit: 50,
   *   sortBy: 'createdAt',
   *   sortOrder: 'desc'
   * })
   * 
   * // Query high-severity events
   * const criticalEvents = await auditLogger.queryLogs({
   *   workspaceId: 'ws456',
   *   severity: 'critical',
   *   limit: 100
   * })
   * ```
   */
  async queryLogs(filters: AuditQueryFilters): Promise<AuditQueryResult> {
    try {
      const db = await this.getFirestore()
      let query = db.collection('audit_logs') as FirebaseFirestore.Query
      
      // Apply filters
      if (filters.workspaceId) {
        query = query.where('workspaceId', '==', filters.workspaceId)
      }
      
      if (filters.userId) {
        query = query.where('userId', '==', filters.userId)
      }
      
      if (filters.eventType) {
        query = query.where('eventType', '==', filters.eventType)
      }
      
      if (filters.severity) {
        query = query.where('severity', '==', filters.severity)
      }
      
      if (filters.category) {
        query = query.where('category', '==', filters.category)
      }
      
      if (filters.startDate) {
        query = query.where('createdAt', '>=', Timestamp.fromDate(filters.startDate))
      }
      
      if (filters.endDate) {
        query = query.where('createdAt', '<=', Timestamp.fromDate(filters.endDate))
      }
      
      // Apply sorting
      const sortBy = filters.sortBy || 'createdAt'
      const sortOrder = filters.sortOrder || 'desc'
      query = query.orderBy(sortBy, sortOrder)
      
      // Apply pagination
      const limit = filters.limit || 50
      const offset = filters.offset || 0
      
      if (offset > 0) {
        query = query.offset(offset)
      }
      
      query = query.limit(limit + 1) // Get one extra to check if there are more
      
      const snapshot = await query.get()
      const entries = snapshot.docs.slice(0, limit).map(doc => doc.data() as EnhancedAuditLogEntry)
      const hasMore = snapshot.docs.length > limit
      
      return {
        entries,
        total: entries.length,
        hasMore,
        nextOffset: hasMore ? offset + limit : undefined
      }
      
    } catch (error) {
      console.error('🔍 [Audit] Error querying logs:', error)
      throw ErrorHandler.handle(error, 'Failed to query audit logs')
    }
  }
  
  /**
   * Get comprehensive audit statistics and analytics
   * 
   * Provides detailed analytics for security monitoring:
   * - Event count by type and severity
   * - Top active users
   * - Risk trend analysis over time
   * - Performance metrics
   * - Compliance reporting data
   * 
   * @param workspaceId - Workspace to analyze
   * @param days - Number of days to include in analysis (default: 30)
   * @returns Promise resolving to comprehensive statistics
   * 
   * @example
   * ```typescript
   * const stats = await auditLogger.getStatistics('ws456', 30)
   * 
   * console.log('Total events:', stats.totalEvents)
   * console.log('Failed logins:', stats.eventsByType['auth.login_failed'])
   * console.log('Critical events:', stats.eventsBySeverity['critical'])
   * console.log('Top users:', stats.topUsers)
   * console.log('Risk trends:', stats.riskTrends)
   * ```
   */
  async getStatistics(workspaceId: string, days: number = 30): Promise<{
    totalEvents: number
    eventsByType: Record<AuditEventType, number>
    eventsBySeverity: Record<AuditSeverity, number>
    topUsers: Array<{ userId: string; count: number }>
    riskTrends: Array<{ date: string; avgRisk: number }>
  }> {
    try {
      const db = await this.getFirestore()
      const startDate = new Date()
      startDate.setDate(startDate.getDate() - days)
      
      const query = db.collection('audit_logs')
        .where('workspaceId', '==', workspaceId)
        .where('createdAt', '>=', Timestamp.fromDate(startDate))
      
      const snapshot = await query.get()
      const entries = snapshot.docs.map(doc => doc.data() as EnhancedAuditLogEntry)
      
      // Calculate statistics
      const eventsByType: Record<string, number> = {}
      const eventsBySeverity: Record<string, number> = {}
      const userCounts: Record<string, number> = {}
      const riskByDate: Record<string, number[]> = {}
      
      entries.forEach(entry => {
        // Count by type
        eventsByType[entry.eventType] = (eventsByType[entry.eventType] || 0) + 1
        
        // Count by severity
        eventsBySeverity[entry.severity] = (eventsBySeverity[entry.severity] || 0) + 1
        
        // Count by user
        if (entry.userId) {
          userCounts[entry.userId] = (userCounts[entry.userId] || 0) + 1
        }
        
        // Risk trends
        const date = entry.createdAt.toDate().toISOString().split('T')[0]
        if (!riskByDate[date]) riskByDate[date] = []
        riskByDate[date].push(entry.metadata.risk?.score || 0)
      })
      
      // Top users
      const topUsers = Object.entries(userCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([userId, count]) => ({ userId, count }))
      
      // Risk trends
      const riskTrends = Object.entries(riskByDate)
        .sort(([a], [b]) => a.localeCompare(b))
        .map(([date, risks]) => ({
          date,
          avgRisk: risks.reduce((sum, risk) => sum + risk, 0) / risks.length
        }))
      
      return {
        totalEvents: entries.length,
        eventsByType: eventsByType as Record<AuditEventType, number>,
        eventsBySeverity: eventsBySeverity as Record<AuditSeverity, number>,
        topUsers,
        riskTrends
      }
      
    } catch (error) {
      console.error('🔍 [Audit] Error getting statistics:', error)
      throw ErrorHandler.handle(error, 'Failed to get audit statistics')
    }
  }
  
  /**
   * Clean up old audit logs based on retention policy
   * 
   * Implements compliance-aware log retention:
   * - Configurable retention periods
   * - Batch processing for performance
   * - Compliance consideration (default: 7 years)
   * - Graceful error handling
   * - Progress tracking and logging
   * 
   * @param workspaceId - Workspace to clean up
   * @param retentionDays - Number of days to retain logs (default: 2555 = 7 years)
   * @returns Promise resolving to number of cleaned up logs
   * 
   * @example
   * ```typescript
   * // Clean up logs older than 7 years (compliance default)
   * const cleanedCount = await auditLogger.cleanupOldLogs('ws456')
   * 
   * // Custom retention period (1 year)
   * const cleanedCount = await auditLogger.cleanupOldLogs('ws456', 365)
   * 
   * console.log(`Cleaned up ${cleanedCount} old audit logs`)
   * ```
   */
  async cleanupOldLogs(workspaceId: string, retentionDays: number = 2555): Promise<number> {
    try {
      const db = await this.getFirestore()
      const cutoffDate = new Date()
      cutoffDate.setDate(cutoffDate.getDate() - retentionDays)
      
      const query = db.collection('audit_logs')
        .where('workspaceId', '==', workspaceId)
        .where('createdAt', '<', Timestamp.fromDate(cutoffDate))
        .limit(500) // Process in batches
      
      const snapshot = await query.get()
      if (snapshot.empty) return 0
      
      const batch = db.batch()
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref)
      })
      
      await batch.commit()
      
      console.log(`🔍 [Audit] Cleaned up ${snapshot.size} old audit logs`)
      return snapshot.size
      
    } catch (error) {
      console.error('🔍 [Audit] Error cleaning up logs:', error)
      throw ErrorHandler.handle(error, 'Failed to cleanup audit logs')
    }
  }
}

// Export singleton instance for application-wide use
export const auditLogger = AuditLogger.getInstance()

/**
 * Convenience functions for common audit events
 * 
 * Provides simplified interfaces for the most common audit scenarios:
 * - Authentication events (login, logout, failures)
 * - Permission management (grant, revoke, role changes)
 * - Security events (access denied, suspicious activity)
 * - Data operations (access, modification, export)
 * 
 * Each function is pre-configured with appropriate event types and
 * common parameters to ensure consistent logging across the application.
 * 
 * @example
 * ```typescript
 * // Log successful login
 * await AuditEvents.loginSuccess(event, 'user123', 'ws456', 'session789')
 * 
 * // Log failed login attempt
 * await AuditEvents.loginFailed(event, '<EMAIL>', 'Invalid password')
 * 
 * // Log permission change
 * await AuditEvents.roleChanged('user123', 'ws456', 'user', 'admin', 'admin123')
 * ```
 */
export const AuditEvents = {
  // Authentication events
  /**
   * Log successful login event
   * @param event - H3 event object
   * @param userId - ID of the user who logged in
   * @param workspaceId - ID of the workspace being accessed
   * @param sessionId - Session identifier
   */
  loginSuccess: (event: H3Event, userId: string, workspaceId: string, sessionId: string) =>
    auditLogger.logEvent('auth.login', { event, userId, workspaceId, sessionId }),
  
  /**
   * Log failed login attempt
   * @param event - H3 event object
   * @param email - Email address of the failed login attempt
   * @param reason - Reason for the login failure
   */
  loginFailed: (event: H3Event, email: string, reason: string) =>
    auditLogger.logEvent('auth.login_failed', { event, metadata: { email, reason } }),
  
  /**
   * Log user logout event
   * @param event - H3 event object
   * @param userId - ID of the user who logged out
   * @param workspaceId - ID of the workspace being accessed
   * @param sessionId - Session identifier being terminated
   */
  logout: (event: H3Event, userId: string, workspaceId: string, sessionId: string) =>
    auditLogger.logEvent('auth.logout', { event, userId, workspaceId, sessionId }),
  
  // Permission events
  /**
   * Log permission granted event
   * @param userId - ID of the user receiving the permission
   * @param workspaceId - ID of the workspace
   * @param permission - The permission being granted
   * @param grantedBy - ID of the user granting the permission
   */
  permissionGranted: (userId: string, workspaceId: string, permission: Permission, grantedBy: string) =>
    auditLogger.logEvent('auth.permission_granted', { 
      userId, 
      workspaceId, 
      resource: 'permission',
      resourceId: permission,
      metadata: { grantedBy }
    }),
  
  /**
   * Log permission revoked event
   * @param userId - ID of the user losing the permission
   * @param workspaceId - ID of the workspace
   * @param permission - The permission being revoked
   * @param revokedBy - ID of the user revoking the permission
   */
  permissionRevoked: (userId: string, workspaceId: string, permission: Permission, revokedBy: string) =>
    auditLogger.logEvent('auth.permission_revoked', { 
      userId, 
      workspaceId, 
      resource: 'permission',
      resourceId: permission,
      metadata: { revokedBy }
    }),
  
  /**
   * Log role change event
   * @param userId - ID of the user whose role is changing
   * @param workspaceId - ID of the workspace
   * @param oldRole - Previous role
   * @param newRole - New role
   * @param changedBy - ID of the user making the change
   */
  roleChanged: (userId: string, workspaceId: string, oldRole: WorkspaceRole, newRole: WorkspaceRole, changedBy: string) =>
    auditLogger.logEvent('auth.role_changed', { 
      userId, 
      workspaceId, 
      resource: 'role',
      before: { role: oldRole },
      after: { role: newRole },
      metadata: { changedBy }
    }),
  
  // Security events
  /**
   * Log access denied event
   * @param event - H3 event object
   * @param userId - ID of the user whose access was denied
   * @param workspaceId - ID of the workspace
   * @param resource - Resource that was accessed
   * @param reason - Reason for access denial
   */
  accessDenied: (event: H3Event, userId: string, workspaceId: string, resource: string, reason: string) =>
    auditLogger.logEvent('auth.access_denied', { 
      event, 
      userId, 
      workspaceId, 
      resource, 
      metadata: { reason }
    }),
  
  /**
   * Log suspicious activity event
   * @param event - H3 event object
   * @param userId - ID of the user involved in suspicious activity
   * @param workspaceId - ID of the workspace
   * @param activity - Type of suspicious activity
   * @param details - Additional details about the activity
   */
  suspiciousActivity: (event: H3Event, userId: string, workspaceId: string, activity: string, details: any) =>
    auditLogger.logEvent('security.suspicious_activity', { 
      event, 
      userId, 
      workspaceId, 
      resource: 'security',
      metadata: { activity, details }
    }),
  
  /**
   * Log rate limit exceeded event
   * @param event - H3 event object
   * @param userId - ID of the user who exceeded the rate limit
   * @param endpoint - The endpoint that was rate limited
   * @param limit - The rate limit that was exceeded
   */
  rateLimitExceeded: (event: H3Event, userId: string, endpoint: string, limit: number) =>
    auditLogger.logEvent('security.rate_limit_exceeded', { 
      event, 
      userId, 
      resource: 'endpoint',
      resourceId: endpoint,
      metadata: { limit }
    }),
  
  // Data events
  /**
   * Log data access event
   * @param event - H3 event object
   * @param userId - ID of the user accessing the data
   * @param workspaceId - ID of the workspace
   * @param resource - Type of resource being accessed
   * @param resourceId - ID of the specific resource
   */
  dataAccessed: (event: H3Event, userId: string, workspaceId: string, resource: string, resourceId: string) =>
    auditLogger.logEvent('data.read', { 
      event, 
      userId, 
      workspaceId, 
      resource, 
      resourceId 
    }),
  
  /**
   * Log data modification event
   * @param event - H3 event object
   * @param userId - ID of the user modifying the data
   * @param workspaceId - ID of the workspace
   * @param resource - Type of resource being modified
   * @param resourceId - ID of the specific resource
   * @param before - Previous state of the data
   * @param after - New state of the data
   */
  dataModified: (event: H3Event, userId: string, workspaceId: string, resource: string, resourceId: string, before: any, after: any) =>
    auditLogger.logEvent('data.updated', { 
      event, 
      userId, 
      workspaceId, 
      resource, 
      resourceId, 
      before, 
      after 
    }),
  
  /**
   * Log data export event
   * @param event - H3 event object
   * @param userId - ID of the user exporting the data
   * @param workspaceId - ID of the workspace
   * @param resource - Type of resource being exported
   * @param format - Export format (CSV, JSON, PDF, etc.)
   */
  dataExported: (event: H3Event, userId: string, workspaceId: string, resource: string, format: string) =>
    auditLogger.logEvent('data.exported', { 
      event, 
      userId, 
      workspaceId, 
      resource, 
      metadata: { format }
    })
}