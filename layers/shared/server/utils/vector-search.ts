import { getFirestore } from 'firebase-admin/firestore'
import { initializeFirebaseAdmin } from './firebase-admin'
import { generateEmbedding } from './vertex-ai'
import { cosineSimilarity, euclideanDistance, normalizeVector } from './embeddings'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '../../utils/error-handler'
import type { H3Event } from 'h3'
import type { VectorEmbedding } from './embeddings'

/**
 * Vector Search Service
 * Provides comprehensive vector similarity search and hybrid search capabilities
 */

export interface VectorSearchQuery {
  query: string
  embedding?: number[]
  filters?: {
    workspaceId?: string
    userId?: string
    type?: VectorEmbedding['metadata']['type']
    tags?: string[]
    dateRange?: {
      start: string
      end: string
    }
    source?: string
  }
  options?: {
    limit?: number
    threshold?: number
    distance?: 'cosine' | 'euclidean' | 'dot'
    includeMetadata?: boolean
    rerank?: boolean
  }
}

export interface HybridSearchQuery extends VectorSearchQuery {
  textQuery?: string
  weights?: {
    vector: number
    text: number
  }
  textFields?: string[]
}

export interface VectorSearchResult {
  id: string
  content: string
  metadata: VectorEmbedding['metadata']
  score: number
  distance: number
  rank: number
  highlights?: string[]
}

export interface SearchAnalytics {
  query: string
  queryType: 'vector' | 'hybrid' | 'text'
  resultsCount: number
  executionTime: number
  threshold: number
  workspaceId: string
  userId: string
  timestamp: string
}

class VectorSearchService {
  private db: any = null
  private searchHistory: Map<string, SearchAnalytics[]> = new Map()

  constructor() {
    this.initializeFirebase()
  }

  /**
   * Initialize Firebase connection
   */
  private async initializeFirebase() {
    try {
      const app = await initializeFirebaseAdmin()
      this.db = getFirestore(app)
      console.log('🔥 [VectorSearch] Firebase initialized')
    } catch (error) {
      console.error('🔥 [VectorSearch] Firebase initialization failed:', error)
      throw ErrorHandler.handle(error, 'Failed to initialize Firebase for vector search')
    }
  }

  /**
   * Perform vector similarity search
   */
  async vectorSearch(
    event: H3Event,
    query: VectorSearchQuery
  ): Promise<VectorSearchResult[]> {
    try {
      const startTime = Date.now()
      console.log('🔥 [VectorSearch] Starting vector search:', query.query)

      // Ensure session context is available
      if (!event.context.session || !event.context.workspace) {
        throw new Error('Session context required for vector search')
      }

      // Generate query embedding if not provided
      let queryEmbedding = query.embedding
      if (!queryEmbedding) {
        const embeddingResponse = await generateEmbedding(query.query, {
          task_type: 'RETRIEVAL_QUERY'
        })
        queryEmbedding = embeddingResponse.embedding
      }

      // Normalize query embedding
      const normalizedQuery = normalizeVector(queryEmbedding)

      // Get Firebase instance
      if (!this.db) {
        await this.initializeFirebase()
      }

      // Build Firestore query
      let firestoreQuery = this.db.collection('embeddings')
        .where('metadata.workspaceId', '==', event.context.workspace.id)

      // Apply filters
      if (query.filters) {
        if (query.filters.userId) {
          firestoreQuery = firestoreQuery.where('metadata.userId', '==', query.filters.userId)
        }
        if (query.filters.type) {
          firestoreQuery = firestoreQuery.where('metadata.type', '==', query.filters.type)
        }
        if (query.filters.tags && query.filters.tags.length > 0) {
          firestoreQuery = firestoreQuery.where('metadata.tags', 'array-contains-any', query.filters.tags)
        }
        if (query.filters.source) {
          firestoreQuery = firestoreQuery.where('metadata.source', '==', query.filters.source)
        }
        if (query.filters.dateRange) {
          firestoreQuery = firestoreQuery
            .where('metadata.createdAt', '>=', query.filters.dateRange.start)
            .where('metadata.createdAt', '<=', query.filters.dateRange.end)
        }
      }

      // Execute query
      const snapshot = await firestoreQuery.limit(1000).get() // Get more for better filtering

      if (snapshot.empty) {
        console.log('🔥 [VectorSearch] No embeddings found for query')
        return []
      }

      // Calculate similarity scores
      const results: Array<VectorSearchResult & { embedding: number[] }> = []
      const threshold = query.options?.threshold || 0.3
      const distanceType = query.options?.distance || 'cosine'

      snapshot.forEach(doc => {
        const embedding = doc.data() as VectorEmbedding
        const normalizedEmbedding = normalizeVector(embedding.embedding)
        
        let score: number
        let distance: number

        switch (distanceType) {
          case 'cosine':
            score = cosineSimilarity(normalizedQuery, normalizedEmbedding)
            distance = 1 - score
            break
          case 'euclidean':
            distance = euclideanDistance(normalizedQuery, normalizedEmbedding)
            score = 1 / (1 + distance)
            break
          case 'dot':
            score = normalizedQuery.reduce((sum, val, i) => sum + val * normalizedEmbedding[i], 0)
            distance = 1 - score
            break
          default:
            throw new Error(`Unsupported distance type: ${distanceType}`)
        }

        if (score >= threshold) {
          results.push({
            id: embedding.id,
            content: embedding.content,
            metadata: embedding.metadata,
            score,
            distance,
            rank: 0, // Will be set after sorting
            embedding: embedding.embedding
          })
        }
      })

      // Sort by score (descending) and assign ranks
      results.sort((a, b) => b.score - a.score)
      results.forEach((result, index) => {
        result.rank = index + 1
      })

      // Apply limit
      const limit = query.options?.limit || 20
      const limitedResults = results.slice(0, limit)

      // Remove embedding from results if not needed
      const finalResults = limitedResults.map(({ embedding, ...result }) => result)

      // Apply reranking if requested
      let rerankedResults = finalResults
      if (query.options?.rerank && finalResults.length > 0) {
        rerankedResults = await this.rerankResults(query.query, finalResults)
      }

      // Record analytics
      const executionTime = Date.now() - startTime
      await this.recordSearchAnalytics(event, {
        query: query.query,
        queryType: 'vector',
        resultsCount: rerankedResults.length,
        executionTime,
        threshold,
        workspaceId: event.context.workspace.id,
        userId: event.context.user?.id || 'anonymous',
        timestamp: new Date().toISOString()
      })

      console.log(`🔥 [VectorSearch] Vector search completed: ${rerankedResults.length} results in ${executionTime}ms`)
      return rerankedResults

    } catch (error) {
      console.error('🔥 [VectorSearch] Vector search failed:', error)
      throw ErrorHandler.handle(error, 'Vector search failed')
    }
  }

  /**
   * Perform hybrid search combining vector and text search
   */
  async hybridSearch(
    event: H3Event,
    query: HybridSearchQuery
  ): Promise<VectorSearchResult[]> {
    try {
      const startTime = Date.now()
      console.log('🔥 [VectorSearch] Starting hybrid search:', query.query)

      // Ensure session context is available
      if (!event.context.session || !event.context.workspace) {
        throw new Error('Session context required for hybrid search')
      }

      // Set default weights
      const weights = query.weights || { vector: 0.7, text: 0.3 }
      const textFields = query.textFields || ['content', 'metadata.source']

      // Perform vector search
      const vectorResults = await this.vectorSearch(event, {
        ...query,
        options: {
          ...query.options,
          limit: (query.options?.limit || 20) * 2 // Get more results for reranking
        }
      })

      // Perform text search if text query is provided
      let textResults: VectorSearchResult[] = []
      if (query.textQuery) {
        textResults = await this.textSearch(event, query.textQuery, query.filters, textFields)
      }

      // Combine and rerank results
      const combinedResults = this.combineSearchResults(vectorResults, textResults, weights)

      // Apply final limit
      const limit = query.options?.limit || 20
      const finalResults = combinedResults.slice(0, limit)

      // Record analytics
      const executionTime = Date.now() - startTime
      await this.recordSearchAnalytics(event, {
        query: query.query,
        queryType: 'hybrid',
        resultsCount: finalResults.length,
        executionTime,
        threshold: query.options?.threshold || 0.3,
        workspaceId: event.context.workspace.id,
        userId: event.context.user?.id || 'anonymous',
        timestamp: new Date().toISOString()
      })

      console.log(`🔥 [VectorSearch] Hybrid search completed: ${finalResults.length} results in ${executionTime}ms`)
      return finalResults

    } catch (error) {
      console.error('🔥 [VectorSearch] Hybrid search failed:', error)
      throw ErrorHandler.handle(error, 'Hybrid search failed')
    }
  }

  /**
   * Perform text-based search
   */
  private async textSearch(
    event: H3Event,
    textQuery: string,
    filters?: VectorSearchQuery['filters'],
    searchFields: string[] = ['content']
  ): Promise<VectorSearchResult[]> {
    try {
      console.log('🔥 [VectorSearch] Performing text search:', textQuery)

      // Get Firebase instance
      if (!this.db) {
        await this.initializeFirebase()
      }

      // Build Firestore query
      let firestoreQuery = this.db.collection('embeddings')
        .where('metadata.workspaceId', '==', event.context.workspace.id)

      // Apply filters
      if (filters) {
        if (filters.userId) {
          firestoreQuery = firestoreQuery.where('metadata.userId', '==', filters.userId)
        }
        if (filters.type) {
          firestoreQuery = firestoreQuery.where('metadata.type', '==', filters.type)
        }
      }

      // Execute query
      const snapshot = await firestoreQuery.get()

      if (snapshot.empty) {
        return []
      }

      // Filter results by text matching
      const results: VectorSearchResult[] = []
      const queryTerms = textQuery.toLowerCase().split(/\s+/)

      snapshot.forEach(doc => {
        const embedding = doc.data() as VectorEmbedding
        let textScore = 0
        let highlights: string[] = []

        // Search in specified fields
        for (const field of searchFields) {
          const fieldValue = this.getNestedValue(embedding, field)
          if (fieldValue && typeof fieldValue === 'string') {
            const fieldText = fieldValue.toLowerCase()
            const fieldScore = this.calculateTextScore(fieldText, queryTerms)
            textScore = Math.max(textScore, fieldScore)

            // Extract highlights
            if (fieldScore > 0) {
              highlights.push(...this.extractHighlights(fieldValue, queryTerms))
            }
          }
        }

        if (textScore > 0) {
          results.push({
            id: embedding.id,
            content: embedding.content,
            metadata: embedding.metadata,
            score: textScore,
            distance: 1 - textScore,
            rank: 0, // Will be set after sorting
            highlights: highlights.slice(0, 3) // Limit highlights
          })
        }
      })

      // Sort by score and assign ranks
      results.sort((a, b) => b.score - a.score)
      results.forEach((result, index) => {
        result.rank = index + 1
      })

      return results

    } catch (error) {
      console.error('🔥 [VectorSearch] Text search failed:', error)
      throw ErrorHandler.handle(error, 'Text search failed')
    }
  }

  /**
   * Find similar documents to a given document
   */
  async findSimilar(
    event: H3Event,
    documentId: string,
    options: {
      limit?: number
      threshold?: number
      excludeIds?: string[]
      sameTypeOnly?: boolean
    } = {}
  ): Promise<VectorSearchResult[]> {
    try {
      console.log('🔥 [VectorSearch] Finding similar documents to:', documentId)

      // Ensure session context is available
      if (!event.context.session || !event.context.workspace) {
        throw new Error('Session context required for similarity search')
      }

      // Get Firebase instance
      if (!this.db) {
        await this.initializeFirebase()
      }

      // Get the source document
      const sourceDoc = await this.db.collection('embeddings').doc(documentId).get()
      if (!sourceDoc.exists) {
        throw new Error('Source document not found')
      }

      const sourceEmbedding = sourceDoc.data() as VectorEmbedding

      // Verify workspace access
      if (sourceEmbedding.metadata.workspaceId !== event.context.workspace.id) {
        throw new Error('Access denied to source document')
      }

      // Perform vector search using the source document's embedding
      const searchQuery: VectorSearchQuery = {
        query: sourceEmbedding.content,
        embedding: sourceEmbedding.embedding,
        filters: {
          workspaceId: event.context.workspace.id,
          ...(options.sameTypeOnly && { type: sourceEmbedding.metadata.type })
        },
        options: {
          limit: (options.limit || 10) + 1, // +1 to account for the source document
          threshold: options.threshold || 0.5
        }
      }

      const results = await this.vectorSearch(event, searchQuery)

      // Exclude the source document and any specified IDs
      const excludeIds = new Set([documentId, ...(options.excludeIds || [])])
      const filteredResults = results
        .filter(result => !excludeIds.has(result.id))
        .slice(0, options.limit || 10)

      console.log(`🔥 [VectorSearch] Found ${filteredResults.length} similar documents`)
      return filteredResults

    } catch (error) {
      console.error('🔥 [VectorSearch] Similarity search failed:', error)
      throw ErrorHandler.handle(error, 'Similarity search failed')
    }
  }

  /**
   * Query expansion using semantic similarity
   */
  async expandQuery(query: string, workspaceId: string): Promise<string[]> {
    try {
      console.log('🔥 [VectorSearch] Expanding query:', query)

      // Generate embedding for the query
      const embeddingResponse = await generateEmbedding(query, {
        task_type: 'SEMANTIC_SIMILARITY'
      })

      // Get Firebase instance
      if (!this.db) {
        await this.initializeFirebase()
      }

      // Find similar content
      const snapshot = await this.db.collection('embeddings')
        .where('metadata.workspaceId', '==', workspaceId)
        .limit(100)
        .get()

      const expandedTerms: string[] = []
      const queryEmbedding = normalizeVector(embeddingResponse.embedding)

      snapshot.forEach(doc => {
        const embedding = doc.data() as VectorEmbedding
        const docEmbedding = normalizeVector(embedding.embedding)
        const similarity = cosineSimilarity(queryEmbedding, docEmbedding)

        if (similarity > 0.7) {
          // Extract key terms from similar content
          const terms = this.extractKeyTerms(embedding.content, query)
          expandedTerms.push(...terms)
        }
      })

      // Remove duplicates and limit results
      const uniqueTerms = [...new Set(expandedTerms)]
        .filter(term => term.toLowerCase() !== query.toLowerCase())
        .slice(0, 5)

      console.log(`🔥 [VectorSearch] Query expanded with ${uniqueTerms.length} terms`)
      return uniqueTerms

    } catch (error) {
      console.error('🔥 [VectorSearch] Query expansion failed:', error)
      return []
    }
  }

  /**
   * Rerank search results using advanced scoring
   */
  private async rerankResults(
    query: string,
    results: VectorSearchResult[]
  ): Promise<VectorSearchResult[]> {
    try {
      // Simple reranking based on content quality and freshness
      const rerankedResults = results.map(result => {
        let rerankScore = result.score

        // Boost score based on content length (optimal range)
        const contentLength = result.content.length
        if (contentLength >= 100 && contentLength <= 2000) {
          rerankScore *= 1.1
        }

        // Boost score based on recency
        const createdAt = new Date(result.metadata.createdAt)
        const daysSinceCreation = (Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24)
        if (daysSinceCreation <= 30) {
          rerankScore *= 1.05
        }

        // Boost score based on document type
        if (result.metadata.type === 'document') {
          rerankScore *= 1.02
        }

        return {
          ...result,
          score: rerankScore
        }
      })

      // Re-sort and update ranks
      rerankedResults.sort((a, b) => b.score - a.score)
      rerankedResults.forEach((result, index) => {
        result.rank = index + 1
      })

      return rerankedResults

    } catch (error) {
      console.error('🔥 [VectorSearch] Reranking failed:', error)
      return results
    }
  }

  /**
   * Combine vector and text search results
   */
  private combineSearchResults(
    vectorResults: VectorSearchResult[],
    textResults: VectorSearchResult[],
    weights: { vector: number; text: number }
  ): VectorSearchResult[] {
    const combinedMap = new Map<string, VectorSearchResult>()

    // Add vector results
    for (const result of vectorResults) {
      combinedMap.set(result.id, {
        ...result,
        score: result.score * weights.vector
      })
    }

    // Add or merge text results
    for (const result of textResults) {
      const existing = combinedMap.get(result.id)
      if (existing) {
        // Combine scores
        existing.score = (existing.score || 0) + (result.score * weights.text)
        existing.highlights = [...(existing.highlights || []), ...(result.highlights || [])]
      } else {
        combinedMap.set(result.id, {
          ...result,
          score: result.score * weights.text
        })
      }
    }

    // Convert to array, sort, and update ranks
    const combinedResults = Array.from(combinedMap.values())
    combinedResults.sort((a, b) => b.score - a.score)
    combinedResults.forEach((result, index) => {
      result.rank = index + 1
    })

    return combinedResults
  }

  /**
   * Calculate text search score
   */
  private calculateTextScore(text: string, queryTerms: string[]): number {
    let score = 0
    for (const term of queryTerms) {
      const termCount = (text.match(new RegExp(term, 'gi')) || []).length
      score += termCount * (1 / Math.sqrt(text.length))
    }
    return Math.min(score, 1)
  }

  /**
   * Extract highlights from text
   */
  private extractHighlights(text: string, queryTerms: string[]): string[] {
    const highlights: string[] = []
    const contextLength = 100

    for (const term of queryTerms) {
      const regex = new RegExp(`\\b${term}\\b`, 'gi')
      const matches = text.matchAll(regex)
      
      for (const match of matches) {
        const start = Math.max(0, match.index! - contextLength)
        const end = Math.min(text.length, match.index! + term.length + contextLength)
        let highlight = text.substring(start, end)
        
        // Add ellipsis if truncated
        if (start > 0) highlight = '...' + highlight
        if (end < text.length) highlight = highlight + '...'
        
        highlights.push(highlight)
      }
    }

    return highlights
  }

  /**
   * Extract key terms from content
   */
  private extractKeyTerms(content: string, originalQuery: string): string[] {
    const words = content.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3)
      .filter(word => !word.includes(originalQuery.toLowerCase()))

    // Simple frequency analysis
    const wordCounts: Record<string, number> = {}
    for (const word of words) {
      wordCounts[word] = (wordCounts[word] || 0) + 1
    }

    return Object.entries(wordCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word)
  }

  /**
   * Get nested value from object
   */
  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  /**
   * Record search analytics
   */
  private async recordSearchAnalytics(event: H3Event, analytics: SearchAnalytics) {
    try {
      const workspaceId = analytics.workspaceId
      
      // Store in memory for quick access
      if (!this.searchHistory.has(workspaceId)) {
        this.searchHistory.set(workspaceId, [])
      }
      
      const history = this.searchHistory.get(workspaceId)!
      history.push(analytics)
      
      // Keep only last 100 searches per workspace
      if (history.length > 100) {
        history.shift()
      }

      // Store in Firestore for persistence
      if (this.db) {
        await this.db.collection('search_analytics').add({
          ...analytics,
          sessionId: event.context.session?.id
        })
      }
    } catch (error) {
      console.error('🔥 [VectorSearch] Failed to record analytics:', error)
      // Don't throw error as this is not critical
    }
  }

  /**
   * Get search analytics for workspace
   */
  async getSearchAnalytics(workspaceId: string): Promise<SearchAnalytics[]> {
    return this.searchHistory.get(workspaceId) || []
  }

  /**
   * Clear search history
   */
  clearSearchHistory(workspaceId: string) {
    this.searchHistory.delete(workspaceId)
  }
}

// Export singleton instance
export const vectorSearchService = new VectorSearchService()

// Export utility functions
export async function performVectorSearch(
  event: H3Event,
  query: VectorSearchQuery
): Promise<VectorSearchResult[]> {
  return await vectorSearchService.vectorSearch(event, query)
}

export async function performHybridSearch(
  event: H3Event,
  query: HybridSearchQuery
): Promise<VectorSearchResult[]> {
  return await vectorSearchService.hybridSearch(event, query)
}

export async function findSimilarDocuments(
  event: H3Event,
  documentId: string,
  options: Parameters<typeof vectorSearchService.findSimilar>[2] = {}
): Promise<VectorSearchResult[]> {
  return await vectorSearchService.findSimilar(event, documentId, options)
}

export async function expandSearchQuery(
  query: string,
  workspaceId: string
): Promise<string[]> {
  return await vectorSearchService.expandQuery(query, workspaceId)
}

export async function getVectorSearchAnalytics(workspaceId: string): Promise<SearchAnalytics[]> {
  return await vectorSearchService.getSearchAnalytics(workspaceId)
}