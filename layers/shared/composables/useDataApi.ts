/**
 * @fileoverview Centralized Data API Composable - Universal Data Access Layer
 * 
 * Comprehensive data management system providing a unified interface for all data operations
 * across the PIB-METHOD architecture. Features multi-tenant workspace isolation, optimistic
 * updates, intelligent caching, batch operations, and real-time subscriptions.
 * 
 * Key Features:
 * - Unified CRUD operations for all entity types
 * - Multi-tenant workspace scoping and isolation
 * - Optimistic updates with automatic rollback
 * - Intelligent caching with TTL and invalidation
 * - Batch operations for efficient bulk processing
 * - Real-time subscriptions and live updates
 * - Advanced validation with custom rules
 * - Import/export capabilities with format support
 * - Performance monitoring and analytics
 * 
 * Security Features:
 * - Automatic workspace boundary enforcement
 * - Profile-based access control
 * - Input validation and sanitization
 * - Audit trail generation
 * - CSRF protection for sensitive operations
 * 
 * Performance Optimizations:
 * - Request deduplication and batching
 * - Intelligent caching strategies
 * - Optimistic UI updates
 * - Background sync for offline support
 * - Connection pooling and retry logic
 * 
 * Multi-Tenant Architecture:
 * - Workspace-scoped data isolation
 * - Cross-workspace data sharing controls
 * - Tenant-aware caching and optimization
 * - Resource usage tracking per workspace
 * 
 * @example
 * ```typescript
 * // Initialize data API
 * const dataApi = useDataApi()
 * 
 * // Generic operations
 * const user = await dataApi.create('users', {
 *   email: '<EMAIL>',
 *   name: 'John Doe'
 * })
 * 
 * // Entity-specific API
 * const usersApi = dataApi.getEntityAPI<User>('users')
 * const users = await usersApi.list({
 *   filters: { role: 'admin' },
 *   pagination: { limit: 10 }
 * })
 * 
 * // Batch operations
 * const results = await dataApi.batch([
 *   { operation: 'create', collection: 'posts', data: { title: 'Post 1' } },
 *   { operation: 'update', collection: 'posts', id: '123', data: { status: 'published' } },
 *   { operation: 'delete', collection: 'posts', id: '456' }
 * ])
 * 
 * // Real-time subscriptions
 * const unsubscribe = usersApi.subscribe((users) => {
 *   console.log('Users updated:', users.length)
 * })
 * ```
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 * @version 2.0.0
 */

import type { Ref } from 'vue'
import { v4 as uuidv4 } from 'uuid'
import type {
  BaseEntity,
  DataAPI,
  EntityAPI,
  CrudOptions,
  DataQuery,
  DataResult,
  VectorSearchConfig,
  BatchOperation,
  BatchResult,
  ValidationResult,
  ExportConfig,
  ImportConfig,
  ImportResult,
  SubscriptionOptions,
  CollectionConfig
} from '~/types/data-api'
import type { User, Workspace, Profile } from '~/types/auth'
import { ErrorHandler } from '~/utils/error-handler'

/**
 * Centralized Data API Composable
 * 
 * Creates a comprehensive data management interface with unified CRUD operations,
 * workspace isolation, caching, validation, and real-time capabilities.
 * Serves as the primary data access layer for all application entities.
 * 
 * @function useDataApi
 * @returns {DataAPI} Complete data API interface with all operations
 * 
 * @example
 * ```typescript
 * // Basic usage
 * const dataApi = useDataApi()
 * 
 * // Create documents
 * const doc = await dataApi.create('documents', {
 *   title: 'My Document',
 *   content: 'Document content...'
 * })
 * 
 * // Query with advanced filters
 * const results = await dataApi.list('posts', {
 *   filters: {
 *     status: 'published',
 *     createdAt: { $gte: '2024-01-01' }
 *   },
 *   sort: [{ field: 'createdAt', direction: 'desc' }]
 * })
 * 
 * // Get entity-specific API
 * const usersApi = dataApi.getEntityAPI<User>('users')
 * const user = await usersApi.read('user_123')
 * 
 * // Monitor API state
 * watch(dataApi.isLoading, (loading) => {
 *   if (loading) console.log('API operation in progress')
 * })
 * 
 * watch(dataApi.error, (error) => {
 *   if (error) console.error('API error:', error)
 * })
 * ```
 * 
 * @throws {Error} When workspace context is missing
 * @throws {Error} When authentication is required but not available
 * 
 * @see {@link EntityAPI} for entity-specific operations
 * @see {@link CrudOptions} for operation configuration
 */
export function useDataApi(): DataAPI {
  const { currentWorkspace, currentProfile } = useAuth()
  const { $fetch } = useNuxtApp()
  
  // Global state
  const isLoading: Ref<boolean> = ref(false)
  const error: Ref<string | null> = ref(null)
  const cache = new Map<string, any>()
  const subscriptions = new Map<string, (() => void)[]>()
  const collectionConfigs = new Map<string, CollectionConfig>()

  /**
   * Set loading state
   */
  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  /**
   * Set error state
   */
  const setError = (err: string | null) => {
    error.value = err
    if (err) {
      console.error('🔥 [DataAPI] Error:', err)
    }
  }

  /**
   * Generate cache key for requests
   */
  const getCacheKey = (collection: string, operation: string, params: any): string => {
    return `${collection}:${operation}:${JSON.stringify(params)}`
  }

  /**
   * Get effective options with workspace context
   */
  const getEffectiveOptions = (options: CrudOptions = {}): CrudOptions => {
    return {
      workspaceId: options.workspaceId || currentWorkspace.value?.id,
      profileId: options.profileId || currentProfile.value?.id,
      showSuccessToast: options.showSuccessToast ?? true,
      showErrorToast: options.showErrorToast ?? true,
      includeDeleted: options.includeDeleted ?? false,
      useOptimisticUpdate: options.useOptimisticUpdate ?? false,
      skipValidation: options.skipValidation ?? false,
      ...options
    }
  }

  /**
   * Show toast notification
   */
  const showToast = (message: string, type: 'success' | 'error' = 'success') => {
    // Integration with your toast system
    // For now, just console log
    console.log(`🔥 [DataAPI] ${type.toUpperCase()}: ${message}`)
  }

  /**
   * Create a new entity with workspace scoping and validation
   * 
   * Creates a new document in the specified collection with automatic
   * workspace isolation, validation, audit trail generation, and optional
   * optimistic updates for improved user experience.
   * 
   * @async
   * @function create
   * @template T - Entity type extending BaseEntity
   * @param {string} collection - Collection name for the new entity
   * @param {Omit<T, keyof BaseEntity>} data - Entity data excluding base fields
   * @param {CrudOptions} [options={}] - Creation options and configuration
   * @returns {Promise<T>} Created entity with generated metadata
   * 
   * @example
   * ```typescript
   * // Basic entity creation
   * const user = await create<User>('users', {
   *   email: '<EMAIL>',
   *   name: 'John Doe',
   *   role: 'user'
   * })
   * 
   * // With optimistic updates
   * const post = await create<Post>('posts', {
   *   title: 'New Blog Post',
   *   content: 'Post content...'
   * }, {
   *   useOptimisticUpdate: true,
   *   showSuccessToast: true
   * })
   * 
   * // With validation and embedding
   * const document = await create<Document>('documents', {
   *   title: 'Research Paper',
   *   content: 'Academic content...'
   * }, {
   *   skipValidation: false,
   *   embed: ['title', 'content']
   * })
   * ```
   * 
   * @throws {Error} When validation fails
   * @throws {Error} When workspace access is denied
   * @throws {Error} When server operation fails
   */
  const create = async <T extends BaseEntity>(
    collection: string,
    data: Omit<T, keyof BaseEntity>,
    options: CrudOptions = {}
  ): Promise<T> => {
    const effectiveOptions = getEffectiveOptions(options)
    
    try {
      setLoading(true)
      setError(null)

      // Validate data if not skipped
      if (!effectiveOptions.skipValidation) {
        const config = collectionConfigs.get(collection)
        if (config && config.validationRules) {
          const validationResult = await validateData(data, config.validationRules)
          if (!validationResult.isValid) {
            throw new Error(`Validation failed: ${validationResult.errors.map(e => e.message).join(', ')}`)
          }
        }
      }

      const newId = uuidv4()
      const newData = { ...data, id: newId }

      // Optimistic update
      let optimisticData: T | null = null
      if (effectiveOptions.useOptimisticUpdate) {
        optimisticData = {
          ...newData,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          deletedAt: null,
          workspaceIds: [effectiveOptions.workspaceId || ''],
          profileIds: [effectiveOptions.profileId || ''],
          createdBy: effectiveOptions.profileId || '',
          updatedBy: effectiveOptions.profileId || ''
        } as T
        
        // Update cache optimistically
        const cacheKey = getCacheKey(collection, 'read', { id: newId })
        cache.set(cacheKey, optimisticData)
      }

      // Call server API
      const response = await $fetch('/api/data/write', {
        method: 'POST',
        body: {
          collection,
          data: newData,
          embed: effectiveOptions.embed || []
        }
      })

      const result = response.data as T

      // Update cache with real data
      const cacheKey = getCacheKey(collection, 'read', { id: result.id })
      cache.set(cacheKey, result)

      // Clear list cache
      clearListCache(collection)

      // Show success toast
      if (effectiveOptions.showSuccessToast) {
        showToast(
          effectiveOptions.customSuccessMessage || 'Created successfully',
          'success'
        )
      }

      return result
    } catch (err: any) {
      // Rollback optimistic update
      if (effectiveOptions.useOptimisticUpdate) {
        const cacheKey = getCacheKey(collection, 'read', { id: newId })
        cache.delete(cacheKey)
      }

      const appError = ErrorHandler.handle(err)
      setError(appError.message)

      if (effectiveOptions.showErrorToast) {
        showToast(
          effectiveOptions.customErrorMessage || `Failed to create: ${appError.message}`,
          'error'
        )
      }

      throw appError
    } finally {
      setLoading(false)
    }
  }

  /**
   * Read an entity by ID with workspace access validation
   * 
   * Retrieves a single entity from the collection with automatic workspace
   * boundary checking, caching, and access control validation.
   * 
   * @async
   * @function read
   * @template T - Entity type extending BaseEntity
   * @param {string} collection - Collection name to read from
   * @param {string} id - Entity ID to retrieve
   * @param {CrudOptions} [options={}] - Read options and configuration
   * @returns {Promise<T | null>} Entity data or null if not found/accessible
   * 
   * @example
   * ```typescript
   * // Basic entity read
   * const user = await read<User>('users', 'user_123')
   * if (user) {
   *   console.log('User found:', user.name)
   * }
   * 
   * // Include soft-deleted entities
   * const deletedPost = await read<Post>('posts', 'post_456', {
   *   includeDeleted: true
   * })
   * 
   * // Read with custom workspace
   * const document = await read<Document>('documents', 'doc_789', {
   *   workspaceId: 'other_workspace'
   * })
   * 
   * // Silent read (no error notifications)
   * const profile = await read<Profile>('profiles', 'profile_012', {
   *   showErrorToast: false
   * })
   * ```
   * 
   * @throws {Error} When entity ID is invalid
   * @throws {Error} When workspace access is denied
   * @throws {Error} When server operation fails
   */
  const read = async <T extends BaseEntity>(
    collection: string,
    id: string,
    options: CrudOptions = {}
  ): Promise<T | null> => {
    const effectiveOptions = getEffectiveOptions(options)
    
    try {
      setLoading(true)
      setError(null)

      // Check cache first
      const cacheKey = getCacheKey(collection, 'read', { id })
      if (cache.has(cacheKey)) {
        return cache.get(cacheKey) as T
      }

      // Call server API
      const response = await $fetch('/api/data/read', {
        method: 'POST',
        body: {
          collection,
          id,
          includeDeleted: effectiveOptions.includeDeleted
        }
      })

      const result = response.data as T | null

      // Cache result
      if (result) {
        cache.set(cacheKey, result)
      }

      return result
    } catch (err: any) {
      const appError = ErrorHandler.handle(err)
      setError(appError.message)

      if (effectiveOptions.showErrorToast) {
        showToast(
          effectiveOptions.customErrorMessage || `Failed to read: ${appError.message}`,
          'error'
        )
      }

      throw appError
    } finally {
      setLoading(false)
    }
  }

  /**
   * Update an entity with validation and optimistic updates
   * 
   * Updates an existing entity with workspace validation, optimistic UI updates,
   * audit trail generation, and optional re-embedding for vector search.
   * 
   * @async
   * @function update
   * @template T - Entity type extending BaseEntity
   * @param {string} collection - Collection name containing the entity
   * @param {string} id - Entity ID to update
   * @param {Partial<T>} data - Partial entity data to update
   * @param {CrudOptions} [options={}] - Update options and configuration
   * @returns {Promise<T>} Updated entity with new metadata
   * 
   * @example
   * ```typescript
   * // Basic entity update
   * const updatedUser = await update<User>('users', 'user_123', {
   *   name: 'John Smith',
   *   lastLoginAt: new Date().toISOString()
   * })
   * 
   * // Optimistic update with rollback
   * const updatedPost = await update<Post>('posts', 'post_456', {
   *   title: 'Updated Title',
   *   status: 'published'
   * }, {
   *   useOptimisticUpdate: true,
   *   showSuccessToast: true
   * })
   * 
   * // Update with re-embedding
   * const updatedDoc = await update<Document>('documents', 'doc_789', {
   *   content: 'Updated content...'
   * }, {
   *   embed: ['content']
   * })
   * ```
   * 
   * @throws {Error} When entity doesn't exist or access denied
   * @throws {Error} When validation fails
   * @throws {Error} When server operation fails
   */
  const update = async <T extends BaseEntity>(
    collection: string,
    id: string,
    data: Partial<T>,
    options: CrudOptions = {}
  ): Promise<T> => {
    const effectiveOptions = getEffectiveOptions(options)
    
    try {
      setLoading(true)
      setError(null)

      // Get current data for optimistic update
      const currentData = await read<T>(collection, id, { showErrorToast: false })
      if (!currentData) {
        throw new Error(`Document not found: ${id}`)
      }

      // Optimistic update
      let optimisticData: T | null = null
      if (effectiveOptions.useOptimisticUpdate) {
        optimisticData = {
          ...currentData,
          ...data,
          updatedAt: new Date().toISOString(),
          updatedBy: effectiveOptions.profileId || ''
        } as T
        
        // Update cache optimistically
        const cacheKey = getCacheKey(collection, 'read', { id })
        cache.set(cacheKey, optimisticData)
      }

      // Call server API
      const response = await $fetch('/api/data/update', {
        method: 'POST',
        body: {
          collection,
          id,
          data,
          embed: effectiveOptions.embed || []
        }
      })

      const result = response.data as T

      // Update cache with real data
      const cacheKey = getCacheKey(collection, 'read', { id })
      cache.set(cacheKey, result)

      // Clear list cache
      clearListCache(collection)

      // Show success toast
      if (effectiveOptions.showSuccessToast) {
        showToast(
          effectiveOptions.customSuccessMessage || 'Updated successfully',
          'success'
        )
      }

      return result
    } catch (err: any) {
      // Rollback optimistic update
      if (effectiveOptions.useOptimisticUpdate && currentData) {
        const cacheKey = getCacheKey(collection, 'read', { id })
        cache.set(cacheKey, currentData)
      }

      const appError = ErrorHandler.handle(err)
      setError(appError.message)

      if (effectiveOptions.showErrorToast) {
        showToast(
          effectiveOptions.customErrorMessage || `Failed to update: ${appError.message}`,
          'error'
        )
      }

      throw appError
    } finally {
      setLoading(false)
    }
  }

  /**
   * Delete an entity with soft delete support and workspace validation
   * 
   * Removes an entity from the collection with optional soft delete behavior,
   * workspace access validation, and optimistic UI updates.
   * 
   * @async
   * @function remove
   * @param {string} collection - Collection name containing the entity
   * @param {string} id - Entity ID to delete
   * @param {CrudOptions & { hardDelete?: boolean }} [options={}] - Delete configuration
   * @returns {Promise<{id: string}>} Deleted entity ID
   * 
   * @example
   * ```typescript
   * // Soft delete (default)
   * await remove('posts', 'post_123')
   * 
   * // Hard delete (permanent)
   * await remove('temp_files', 'file_456', {
   *   hardDelete: true
   * })
   * 
   * // Optimistic delete with confirmation
   * await remove('comments', 'comment_789', {
   *   useOptimisticUpdate: true,
   *   showSuccessToast: true,
   *   customSuccessMessage: 'Comment deleted successfully'
   * })
   * ```
   * 
   * @throws {Error} When entity doesn't exist or access denied
   * @throws {Error} When deletion fails
   */
  const remove = async (
    collection: string,
    id: string,
    options: CrudOptions & { hardDelete?: boolean } = {}
  ): Promise<{ id: string }> => {
    const effectiveOptions = getEffectiveOptions(options)
    
    try {
      setLoading(true)
      setError(null)

      // Get current data for potential rollback
      const currentData = await read(collection, id, { showErrorToast: false })
      if (!currentData) {
        throw new Error(`Document not found: ${id}`)
      }

      // Optimistic delete
      if (effectiveOptions.useOptimisticUpdate) {
        const cacheKey = getCacheKey(collection, 'read', { id })
        cache.delete(cacheKey)
      }

      // Call server API
      await $fetch('/api/data/delete', {
        method: 'POST',
        body: {
          collection,
          id,
          hardDelete: options.hardDelete || false
        }
      })

      // Clear cache
      const cacheKey = getCacheKey(collection, 'read', { id })
      cache.delete(cacheKey)

      // Clear list cache
      clearListCache(collection)

      // Show success toast
      if (effectiveOptions.showSuccessToast) {
        showToast(
          effectiveOptions.customSuccessMessage || 'Deleted successfully',
          'success'
        )
      }

      return { id }
    } catch (err: any) {
      // Rollback optimistic delete
      if (effectiveOptions.useOptimisticUpdate && currentData) {
        const cacheKey = getCacheKey(collection, 'read', { id })
        cache.set(cacheKey, currentData)
      }

      const appError = ErrorHandler.handle(err)
      setError(appError.message)

      if (effectiveOptions.showErrorToast) {
        showToast(
          effectiveOptions.customErrorMessage || `Failed to delete: ${appError.message}`,
          'error'
        )
      }

      throw appError
    } finally {
      setLoading(false)
    }
  }

  /**
   * Query entities with advanced filtering, sorting, and pagination
   * 
   * Retrieves multiple entities with comprehensive query capabilities including
   * filtering, sorting, pagination, vector search, and workspace scoping.
   * 
   * @async
   * @function list
   * @template T - Entity type extending BaseEntity
   * @param {string} collection - Collection name to query
   * @param {DataQuery} [query={}] - Query configuration with filters and options
   * @param {CrudOptions} [options={}] - Operation options and configuration
   * @returns {Promise<DataResult<T>>} Query results with metadata
   * 
   * @example
   * ```typescript
   * // Basic listing
   * const { data: users } = await list<User>('users')
   * 
   * // Advanced filtering and sorting
   * const { data: posts, meta } = await list<Post>('posts', {
   *   filters: {
   *     status: 'published',
   *     createdAt: { $gte: '2024-01-01' },
   *     tags: { $contains: 'featured' }
   *   },
   *   sort: [{ field: 'createdAt', direction: 'desc' }],
   *   pagination: { limit: 20, startAfter: 'last_doc_id' }
   * })
   * 
   * // Vector search
   * const { data: documents } = await list<Document>('documents', {
   *   vectorSearch: {
   *     query: 'machine learning research',
   *     field: 'content_embedding',
   *     threshold: 0.8
   *   }
   * })
   * 
   * console.log(`Found ${meta.total} results, hasMore: ${meta.hasMore}`)
   * ```
   * 
   * @throws {Error} When query is invalid
   * @throws {Error} When vector search fails
   * @throws {Error} When server operation fails
   */
  const list = async <T extends BaseEntity>(
    collection: string,
    query: DataQuery = {},
    options: CrudOptions = {}
  ): Promise<DataResult<T>> => {
    const effectiveOptions = getEffectiveOptions(options)
    
    try {
      setLoading(true)
      setError(null)

      // Check cache first
      const cacheKey = getCacheKey(collection, 'list', { query, options: effectiveOptions })
      if (cache.has(cacheKey)) {
        return cache.get(cacheKey) as DataResult<T>
      }

      // Build request body
      const requestBody: any = {
        collection,
        filters: query.filters || {},
        limit: query.pagination?.limit || 20,
        orderBy: query.sort?.[0]?.field || 'createdAt',
        orderDirection: query.sort?.[0]?.direction || 'desc',
        startAfter: query.pagination?.startAfter || null
      }

      // Add vector search if specified
      if (query.vectorSearch) {
        requestBody.vec = query.vectorSearch
      }

      // Call server API
      const response = await $fetch('/api/data/read', {
        method: 'POST',
        body: requestBody
      })

      const result: DataResult<T> = {
        statusCode: response.statusCode || 200,
        data: response.data || [],
        meta: {
          total: response.data?.length || 0,
          hasMore: (response.data?.length || 0) >= (query.pagination?.limit || 20)
        }
      }

      // Cache result
      cache.set(cacheKey, result)

      return result
    } catch (err: any) {
      const appError = ErrorHandler.handle(err)
      setError(appError.message)

      if (effectiveOptions.showErrorToast) {
        showToast(
          effectiveOptions.customErrorMessage || `Failed to list: ${appError.message}`,
          'error'
        )
      }

      throw appError
    } finally {
      setLoading(false)
    }
  }

  /**
   * Execute multiple operations in a single batch
   * 
   * Performs multiple create, update, and delete operations efficiently
   * with atomic success/failure tracking and detailed error reporting.
   * 
   * @async
   * @function batch
   * @param {BatchOperation[]} operations - Array of operations to execute
   * @returns {Promise<BatchResult>} Batch execution results with success/error details
   * 
   * @example
   * ```typescript
   * // Mixed batch operations
   * const result = await batch([
   *   {
   *     operation: 'create',
   *     collection: 'posts',
   *     data: { title: 'New Post', content: 'Content...' }
   *   },
   *   {
   *     operation: 'update',
   *     collection: 'users',
   *     id: 'user_123',
   *     data: { lastLoginAt: new Date().toISOString() }
   *   },
   *   {
   *     operation: 'delete',
   *     collection: 'temp_files',
   *     id: 'file_456'
   *   }
   * ])
   * 
   * console.log(`Batch completed: ${result.successCount}/${result.totalCount} operations succeeded`)
   * 
   * // Handle individual operation results
   * result.operations.forEach((op, index) => {
   *   if (op.success) {
   *     console.log(`Operation ${index} succeeded`)
   *   } else {
   *     console.error(`Operation ${index} failed:`, op.error)
   *   }
   * })
   * ```
   * 
   * @throws {Error} When operations array is empty
   * @throws {Error} When batch processing fails completely
   */
  const batch = async (operations: BatchOperation[]): Promise<BatchResult> => {
    try {
      setLoading(true)
      setError(null)

      const results: BatchResult = {
        success: true,
        operations: [],
        totalCount: operations.length,
        successCount: 0,
        errorCount: 0
      }

      for (const operation of operations) {
        try {
          let result: any
          switch (operation.operation) {
            case 'create':
              result = await create(operation.collection, operation.data!)
              break
            case 'update':
              result = await update(operation.collection, operation.id!, operation.data!)
              break
            case 'delete':
              result = await remove(operation.collection, operation.id!)
              break
            default:
              throw new Error(`Unknown operation: ${operation.operation}`)
          }

          results.operations.push({
            operation: operation.operation,
            id: operation.id || result.id,
            success: true
          })
          results.successCount++
        } catch (error: any) {
          results.operations.push({
            operation: operation.operation,
            id: operation.id,
            success: false,
            error: error.message
          })
          results.errorCount++
          results.success = false
        }
      }

      return results
    } catch (err: any) {
      const appError = ErrorHandler.handle(err)
      setError(appError.message)
      throw appError
    } finally {
      setLoading(false)
    }
  }

  /**
   * Data validation
   */
  const validateData = async (data: any, rules: any[]): Promise<ValidationResult> => {
    const errors: { field: string; message: string }[] = []

    for (const rule of rules) {
      const value = data[rule.field]
      
      switch (rule.type) {
        case 'required':
          if (!value) {
            errors.push({ field: rule.field, message: rule.message || `${rule.field} is required` })
          }
          break
        case 'email':
          if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            errors.push({ field: rule.field, message: rule.message || `${rule.field} must be a valid email` })
          }
          break
        case 'min':
          if (value && value.length < rule.value) {
            errors.push({ field: rule.field, message: rule.message || `${rule.field} must be at least ${rule.value} characters` })
          }
          break
        case 'max':
          if (value && value.length > rule.value) {
            errors.push({ field: rule.field, message: rule.message || `${rule.field} must be at most ${rule.value} characters` })
          }
          break
        case 'custom':
          if (rule.validator && !(await rule.validator(value))) {
            errors.push({ field: rule.field, message: rule.message || `${rule.field} validation failed` })
          }
          break
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Clear list cache for a collection
   */
  const clearListCache = (collection: string) => {
    const keysToDelete = Array.from(cache.keys()).filter(key => 
      key.startsWith(`${collection}:list:`)
    )
    keysToDelete.forEach(key => cache.delete(key))
  }

  /**
   * Clear all cache
   */
  const clearCache = (collection?: string) => {
    if (collection) {
      const keysToDelete = Array.from(cache.keys()).filter(key => 
        key.startsWith(`${collection}:`)
      )
      keysToDelete.forEach(key => cache.delete(key))
    } else {
      cache.clear()
    }
  }

  /**
   * Create an entity-specific API interface
   * 
   * Generates a specialized API interface for a specific entity type with
   * all CRUD operations, batch processing, subscriptions, and utility methods
   * pre-configured for the entity collection.
   * 
   * @function getEntityAPI
   * @template T - Entity type extending BaseEntity
   * @param {string} collection - Collection name for the entity
   * @returns {EntityAPI<T>} Complete entity-specific API interface
   * 
   * @example
   * ```typescript
   * // Get user-specific API
   * const usersApi = getEntityAPI<User>('users')
   * 
   * // Use entity-specific methods
   * const user = await usersApi.create({
   *   email: '<EMAIL>',
   *   name: 'John Doe'
   * })
   * 
   * const users = await usersApi.list({
   *   filters: { role: 'admin' }
   * })
   * 
   * // Real-time subscriptions
   * const unsubscribe = usersApi.subscribe((users) => {
   *   console.log('Users updated:', users.length)
   * })
   * 
   * // Batch operations
   * const newUsers = await usersApi.createMany([
   *   { email: '<EMAIL>', name: 'User 1' },
   *   { email: '<EMAIL>', name: 'User 2' }
   * ])
   * 
   * // Utility methods
   * const exists = await usersApi.exists('user_123')
   * const duplicate = await usersApi.duplicate('user_456', { name: 'Copy of User' })
   * ```
   * 
   * @see {@link EntityAPI} for complete interface documentation
   */
  const getEntityAPI = <T extends BaseEntity>(collection: string): EntityAPI<T> => {
    return {
      // CRUD operations
      create: (data: Omit<T, keyof BaseEntity>, options?: CrudOptions) => 
        create<T>(collection, data, options),
      read: (id: string, options?: CrudOptions) => 
        read<T>(collection, id, options),
      update: (id: string, data: Partial<T>, options?: CrudOptions) => 
        update<T>(collection, id, data, options),
      delete: (id: string, options?: CrudOptions) => 
        remove(collection, id, options),

      // Query operations
      list: (query?: DataQuery, options?: CrudOptions) => 
        list<T>(collection, query, options),
      count: async (filters?: any, options?: CrudOptions) => {
        const result = await list<T>(collection, { filters }, options)
        return result.meta?.total || 0
      },
      search: async (searchQuery: string, config?: VectorSearchConfig, options?: CrudOptions) => {
        const result = await list<T>(collection, { vectorSearch: { query: searchQuery, ...config } }, options)
        return result
      },

      // Batch operations
      createMany: async (items: Omit<T, keyof BaseEntity>[], options?: CrudOptions) => {
        const results: T[] = []
        for (const item of items) {
          const result = await create<T>(collection, item, options)
          results.push(result)
        }
        return results
      },
      updateMany: async (updates: { id: string; data: Partial<T> }[], options?: CrudOptions) => {
        const results: T[] = []
        for (const { id, data } of updates) {
          const result = await update<T>(collection, id, data, options)
          results.push(result)
        }
        return results
      },
      deleteMany: async (ids: string[], options?: CrudOptions) => {
        for (const id of ids) {
          await remove(collection, id, options)
        }
        return { ids }
      },

      // Real-time subscriptions (mock implementation)
      subscribe: (callback: (data: T[]) => void, options?: SubscriptionOptions) => {
        // This would integrate with your real-time system
        console.warn('Real-time subscriptions not implemented yet')
        return () => {}
      },
      subscribeToOne: (id: string, callback: (data: T | null) => void) => {
        // This would integrate with your real-time system
        console.warn('Real-time subscriptions not implemented yet')
        return () => {}
      },

      // Import/Export (mock implementation)
      export: async (config: ExportConfig) => {
        throw new Error('Export not implemented yet')
      },
      import: async (config: ImportConfig) => {
        throw new Error('Import not implemented yet')
      },

      // Validation
      validate: async (data: Partial<T>) => {
        const config = collectionConfigs.get(collection)
        if (config && config.validationRules) {
          return await validateData(data, config.validationRules)
        }
        return { isValid: true, errors: [] }
      },

      // Utility
      exists: async (id: string) => {
        const result = await read<T>(collection, id, { showErrorToast: false })
        return result !== null
      },
      duplicate: async (id: string, modifications?: Partial<T>) => {
        const original = await read<T>(collection, id)
        if (!original) {
          throw new Error('Original document not found')
        }
        
        const { id: _, createdAt, updatedAt, createdBy, updatedBy, ...dataToClone } = original
        return await create<T>(collection, {
          ...dataToClone,
          ...modifications
        } as Omit<T, keyof BaseEntity>)
      },
      restore: async (id: string) => {
        return await update<T>(collection, id, { deletedAt: null } as Partial<T>)
      }
    }
  }

  /**
   * Configure collection
   */
  const configureCollection = <T extends BaseEntity>(config: CollectionConfig<T>) => {
    collectionConfigs.set(config.name, config)
  }

  /**
   * Get metrics
   */
  const getMetrics = async () => {
    return {
      cacheSize: cache.size,
      subscriptions: subscriptions.size,
      collections: collectionConfigs.size
    }
  }

  return {
    // Generic operations
    create,
    read,
    update,
    delete: remove,
    list,

    // Batch operations
    batch,

    // Entity-specific APIs
    getEntityAPI,

    // Configuration
    configureCollection,

    // State management
    isLoading,
    error,

    // Utilities
    clearCache,
    getMetrics
  }
}

/**
 * Pre-configured entity APIs for common collections
 */
export const useUsersApi = () => {
  const dataApi = useDataApi()
  return dataApi.getEntityAPI<User>('users')
}

export const useWorkspacesApi = () => {
  const dataApi = useDataApi()
  return dataApi.getEntityAPI<Workspace>('workspaces')
}

export const useProfilesApi = () => {
  const dataApi = useDataApi()
  return dataApi.getEntityAPI<Profile>('profiles')
}

// Export default for backwards compatibility
export default useDataApi