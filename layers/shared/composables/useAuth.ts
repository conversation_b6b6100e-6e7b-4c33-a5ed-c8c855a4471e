import type { User as FirebaseUser } from 'firebase/auth'
import {
  createUserWithEmailAndPassword,
  getAuth,
  onAuthStateChanged,
  sendPasswordResetEmail,
  signInWithEmailAndPassword,
  signInWithPopup,
  signOut,
  updateProfile,
  type UserCredential
} from 'firebase/auth'
import type {
  UserSession,
  SessionUser,
  SessionWorkspace,
  SessionProfile,
  SessionPayload,
  WorkspaceSwitchPayload
} from '~/types/session'
import type { Permission, WorkspaceRole, ROLE_PERMISSIONS } from '~/types/auth'
import { ErrorHandler } from '~/utils/error-handler'

/**
 * Enhanced authentication state with multi-tenant support
 */
interface AuthState {
  // Core authentication
  user: SessionUser | null
  isLoading: boolean
  isAuthenticated: boolean
  isInitialized: boolean
  error: string | null
  
  // Multi-tenant context
  currentWorkspace: SessionWorkspace | null
  currentProfile: SessionProfile | null
  workspaces: SessionWorkspace[]
  
  // Session management
  sessionId: string | null
  sessionExpiresAt: string | null
  
  // Firebase token state
  firebaseToken: {
    idToken?: string
    expirationTime?: number
  } | null
}

/**
 * Global auth state - reactive singleton
 */
const authState = reactive<AuthState>({
  user: null,
  isLoading: true,
  isAuthenticated: false,
  isInitialized: false,
  error: null,
  currentWorkspace: null,
  currentProfile: null,
  workspaces: [],
  sessionId: null,
  sessionExpiresAt: null,
  firebaseToken: null
})

/**
 * Auth state change listeners
 */
const authListeners = new Set<(state: AuthState) => void>()
const workspaceListeners = new Set<(workspace: SessionWorkspace | null) => void>()

/**
 * Enhanced authentication composable with multi-tenant support
 */
export const useAuth = () => {
  const { $auth, $googleProvider, $retryFirebaseOperation } = useNuxtApp()
  const router = useRouter()
  
  /**
   * Set user and trigger session creation
   */
  const setUser = async (firebaseUser: FirebaseUser | null) => {
    try {
      authState.isLoading = true
      authState.error = null

      if (firebaseUser) {
        // Get Firebase ID token
        const idToken = await firebaseUser.getIdToken()
        const tokenResult = await firebaseUser.getIdTokenResult()
        
        authState.firebaseToken = {
          idToken,
          expirationTime: new Date(tokenResult.expirationTime).getTime()
        }

        // Create session user object
        const sessionUser: SessionUser = {
          id: firebaseUser.uid,
          email: firebaseUser.email || '',
          displayName: firebaseUser.displayName || '',
          photoURL: firebaseUser.photoURL || undefined,
          emailVerified: firebaseUser.emailVerified,
          lastLoginAt: new Date().toISOString()
        }

        // Load or create user's workspace context
        await loadUserSession(sessionUser, idToken)
      } else {
        // Clear auth state
        await clearAuthState()
      }
    } catch (error: any) {
      console.error('🔥 [Auth] Error setting user:', error)
      setError('Failed to initialize user session')
      await clearAuthState()
    } finally {
      authState.isLoading = false
      authState.isInitialized = true
      notifyAuthListeners()
    }
  }

  /**
   * Load user session data from server
   */
  const loadUserSession = async (sessionUser: SessionUser, idToken: string) => {
    try {
      // Call server to establish session and get workspace context
      const sessionData = await $fetch('/api/auth/session', {
        method: 'POST',
        body: {
          user: sessionUser,
          token: { idToken, expirationTime: authState.firebaseToken?.expirationTime }
        }
      }) as UserSession

      // Update auth state with session data
      authState.user = sessionUser
      authState.currentWorkspace = sessionData.currentWorkspace
      authState.currentProfile = sessionData.currentProfile
      authState.workspaces = sessionData.workspaces || []
      authState.sessionId = sessionData.sessionId
      authState.sessionExpiresAt = sessionData.expiresAt
      authState.isAuthenticated = true

      console.log('🔥 [Auth] Session loaded:', {
        userId: sessionUser.id,
        workspaceId: sessionData.currentWorkspace?.id,
        profileId: sessionData.currentProfile?.id
      })
    } catch (error: any) {
      console.error('🔥 [Auth] Failed to load session:', error)
      throw new Error('Failed to establish user session')
    }
  }

  /**
   * Clear all authentication state
   */
  const clearAuthState = async () => {
    // Clear server session
    try {
      if (authState.sessionId) {
        await $fetch('/api/auth/signout', { method: 'POST' })
      }
    } catch (error) {
      console.warn('🔥 [Auth] Failed to clear server session:', error)
    }

    // Clear client state
    authState.user = null
    authState.currentWorkspace = null
    authState.currentProfile = null
    authState.workspaces = []
    authState.sessionId = null
    authState.sessionExpiresAt = null
    authState.firebaseToken = null
    authState.isAuthenticated = false
    authState.error = null
  }

  /**
   * Set error state
   */
  const setError = (error: string | null) => {
    authState.error = error
    if (error) {
      console.error('🔥 [Auth] Error:', error)
    }
  }

  /**
   * Enhanced sign up with workspace creation
   */
  const signUp = async (
    email: string, 
    password: string, 
    displayName?: string,
    workspaceName?: string
  ): Promise<UserCredential> => {
    try {
      authState.isLoading = true
      authState.error = null
      
      const userCredential = await $retryFirebaseOperation(async () => 
        createUserWithEmailAndPassword($auth, email, password)
      )
      
      if (displayName && userCredential.user) {
        await updateProfile(userCredential.user, { displayName })
      }

      // Get ID token for server communication
      const idToken = await userCredential.user.getIdToken()

      // Call server signup endpoint to create user + workspace + profile
      await $fetch('/api/auth/signup', {
        method: 'POST',
        body: {
          user: {
            id: userCredential.user.uid,
            email: userCredential.user.email,
            displayName: displayName || userCredential.user.displayName || '',
            emailVerified: userCredential.user.emailVerified
          },
          workspaceName: workspaceName || `${displayName || 'My'} Workspace`,
          token: { idToken }
        }
      })
      
      return userCredential
    } catch (error: any) {
      const appError = ErrorHandler.handle(error)
      setError(appError.message)
      throw appError
    } finally {
      authState.isLoading = false
    }
  }

  /**
   * Enhanced sign in with session restoration
   */
  const signIn = async (email: string, password: string): Promise<UserCredential> => {
    try {
      authState.isLoading = true
      authState.error = null
      
      const userCredential = await $retryFirebaseOperation(async () =>
        signInWithEmailAndPassword($auth, email, password)
      )

      // Session will be automatically created by the auth state listener
      return userCredential
    } catch (error: any) {
      const appError = ErrorHandler.handle(error)
      setError(appError.message)
      throw appError
    } finally {
      authState.isLoading = false
    }
  }

  /**
   * Google OAuth sign in
   */
  const signInWithGoogle = async (): Promise<UserCredential> => {
    try {
      authState.isLoading = true
      authState.error = null

      const userCredential = await $retryFirebaseOperation(async () =>
        signInWithPopup($auth, $googleProvider)
      )

      return userCredential
    } catch (error: any) {
      const appError = ErrorHandler.handle(error)
      setError(appError.message)
      throw appError
    } finally {
      authState.isLoading = false
    }
  }

  /**
   * Enhanced Google OAuth sign up with workspace creation
   */
  const signUpWithGoogle = async (
    workspaceName?: string
  ): Promise<UserCredential> => {
    try {
      authState.isLoading = true
      authState.error = null

      const userCredential = await $retryFirebaseOperation(async () =>
        signInWithPopup($auth, $googleProvider)
      )

      // Get ID token for server communication
      const idToken = await userCredential.user.getIdToken()

      // Call server signup endpoint to create user + workspace + profile
      await $fetch('/api/auth/signup', {
        method: 'POST',
        body: {
          user: {
            id: userCredential.user.uid,
            email: userCredential.user.email,
            displayName: userCredential.user.displayName || '',
            emailVerified: userCredential.user.emailVerified
          },
          workspaceName: workspaceName || `${userCredential.user.displayName || 'My'} Workspace`,
          token: { idToken }
        }
      })

      return userCredential
    } catch (error: any) {
      const appError = ErrorHandler.handle(error)
      setError(appError.message)
      throw appError
    } finally {
      authState.isLoading = false
    }
  }

  /**
   * Enhanced logout with session cleanup
   */
  const logout = async (): Promise<void> => {
    try {
      authState.isLoading = true
      authState.error = null
      
      await signOut($auth)
      // State cleanup handled by auth state listener
    } catch (error: any) {
      const appError = ErrorHandler.handle(error)
      setError(appError.message)
      throw appError
    } finally {
      authState.isLoading = false
    }
  }

  /**
   * Send password reset email
   */
  const sendPasswordReset = async (email: string): Promise<void> => {
    try {
      authState.isLoading = true
      authState.error = null
      
      await $retryFirebaseOperation(async () =>
        sendPasswordResetEmail($auth, email)
      )
    } catch (error: any) {
      const appError = ErrorHandler.handle(error)
      setError(appError.message)
      throw appError
    } finally {
      authState.isLoading = false
    }
  }

  /**
   * Switch to a different workspace
   */
  const switchWorkspace = async (workspaceId: string): Promise<void> => {
    try {
      authState.isLoading = true
      authState.error = null

      if (!authState.user) {
        throw new Error('User not authenticated')
      }

      // Find the target workspace
      const targetWorkspace = authState.workspaces.find(w => w.id === workspaceId)
      if (!targetWorkspace) {
        throw new Error('Workspace not found or access denied')
      }

      // Call server to switch workspace context
      const sessionData = await $fetch('/api/auth/workspace/switch', {
        method: 'POST',
        body: {
          workspaceId,
          userId: authState.user.id
        } as WorkspaceSwitchPayload
      }) as UserSession

      // Update current workspace and profile
      authState.currentWorkspace = sessionData.currentWorkspace
      authState.currentProfile = sessionData.currentProfile

      console.log('🔥 [Auth] Workspace switched:', {
        workspaceId,
        profileId: sessionData.currentProfile?.id
      })

      // Notify workspace change listeners
      notifyWorkspaceListeners()

      // Refresh current page to load workspace-specific data
      await router.replace(router.currentRoute.value.fullPath)
    } catch (error: any) {
      const appError = ErrorHandler.handle(error)
      setError(appError.message)
      throw appError
    } finally {
      authState.isLoading = false
    }
  }

  /**
   * Check if user has a specific role in current workspace
   */
  const hasRole = (role: WorkspaceRole): boolean => {
    return authState.currentProfile?.role === role
  }

  /**
   * Check if user has a specific permission in current workspace
   */
  const hasPermission = (permission: Permission): boolean => {
    if (!authState.currentProfile) return false
    
    // Check explicit permissions
    if (authState.currentProfile.permissions.includes(permission)) {
      return true
    }

    // Check role-based permissions
    const rolePermissions = ROLE_PERMISSIONS[authState.currentProfile.role as WorkspaceRole] || []
    return rolePermissions.includes(permission)
  }

  /**
   * Check if user has any of the specified permissions
   */
  const hasAnyPermission = (permissions: Permission[]): boolean => {
    return permissions.some(permission => hasPermission(permission))
  }

  /**
   * Check if user has all of the specified permissions
   */
  const hasAllPermissions = (permissions: Permission[]): boolean => {
    return permissions.every(permission => hasPermission(permission))
  }

  /**
   * Refresh current session
   */
  const refreshSession = async (): Promise<void> => {
    try {
      if (!authState.user || !$auth.currentUser) {
        throw new Error('No active session to refresh')
      }

      const idToken = await $auth.currentUser.getIdToken(true) // Force refresh
      await loadUserSession(authState.user, idToken)
    } catch (error: any) {
      const appError = ErrorHandler.handle(error)
      setError(appError.message)
      throw appError
    }
  }

  /**
   * Check if current session is expired
   */
  const isSessionExpired = (): boolean => {
    if (!authState.sessionExpiresAt) return true
    return new Date(authState.sessionExpiresAt) <= new Date()
  }

  /**
   * Get current workspace permissions
   */
  const getCurrentPermissions = (): Permission[] => {
    if (!authState.currentProfile) return []
    
    const rolePermissions = ROLE_PERMISSIONS[authState.currentProfile.role as WorkspaceRole] || []
    const customPermissions = authState.currentProfile.permissions as Permission[]
    
    return [...new Set([...rolePermissions, ...customPermissions])]
  }

  /**
   * Event listeners for auth state changes
   */
  const onAuthStateChange = (callback: (state: AuthState) => void): (() => void) => {
    authListeners.add(callback)
    return () => authListeners.delete(callback)
  }

  const onWorkspaceChange = (callback: (workspace: SessionWorkspace | null) => void): (() => void) => {
    workspaceListeners.add(callback)
    return () => workspaceListeners.delete(callback)
  }

  /**
   * Notify listeners of state changes
   */
  const notifyAuthListeners = () => {
    authListeners.forEach(callback => callback(authState))
  }

  const notifyWorkspaceListeners = () => {
    workspaceListeners.forEach(callback => callback(authState.currentWorkspace))
  }

  // Initialize auth state listener on composable creation
  if (process.client && !authState.isInitialized) {
    console.log('🔥 [Auth] Initializing Firebase auth listener...')
    onAuthStateChanged($auth, setUser)
  }

  return {
    // State (readonly refs)
    user: readonly(toRef(authState, 'user')),
    isLoading: readonly(toRef(authState, 'isLoading')),
    isAuthenticated: readonly(toRef(authState, 'isAuthenticated')),
    isInitialized: readonly(toRef(authState, 'isInitialized')),
    error: readonly(toRef(authState, 'error')),
    
    // Multi-tenant state
    currentWorkspace: readonly(toRef(authState, 'currentWorkspace')),
    currentProfile: readonly(toRef(authState, 'currentProfile')),
    workspaces: readonly(toRef(authState, 'workspaces')),
    
    // Session state
    sessionId: readonly(toRef(authState, 'sessionId')),
    sessionExpiresAt: readonly(toRef(authState, 'sessionExpiresAt')),
    
    // Authentication actions
    signUp,
    signIn,
    signInWithGoogle,
    signUpWithGoogle,
    logout,
    sendPasswordReset,
    setError,
    
    // Multi-tenant actions
    switchWorkspace,
    refreshSession,
    
    // Permission utilities
    hasRole,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    getCurrentPermissions,
    
    // Session utilities
    isSessionExpired,
    
    // Event listeners
    onAuthStateChange,
    onWorkspaceChange
  }
}