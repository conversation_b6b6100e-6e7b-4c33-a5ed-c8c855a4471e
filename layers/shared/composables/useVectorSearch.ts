import { ref, computed, watch } from 'vue'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '~/utils/error-handler'

/**
 * @fileoverview Vector Search Composable - Comprehensive semantic search and AI-powered content discovery
 * 
 * This composable provides a complete interface for vector-based semantic search capabilities
 * within the PIB-METHOD multi-tenant architecture. It integrates with Firebase Firestore,
 * AI embeddings services, and provides real-time search functionality with workspace isolation.
 * 
 * Key Features:
 * - Semantic vector search with embeddings
 * - Hybrid search combining vector and text search
 * - Multi-tenant workspace scoping
 * - Real-time search suggestions and history
 * - Performance monitoring and analytics
 * - Batch embedding operations
 * - Search result ranking and filtering
 * 
 * Security Considerations:
 * - All searches are automatically scoped to the current workspace
 * - Search queries and results are validated for workspace access
 * - Embedding generation includes metadata validation
 * - Search history is workspace-isolated
 * 
 * Performance Optimizations:
 * - Search result caching with TTL
 * - Debounced query suggestions
 * - Batch processing for multiple embeddings
 * - Optimized similarity calculations
 * 
 * Integration Patterns:
 * - Works with useAuth() for workspace context
 * - Integrates with server-side AI APIs
 * - Compatible with Firestore composables
 * - Supports real-time updates
 * 
 * @example
 * ```typescript
 * // Basic vector search
 * const { search, state, hasResults } = useVectorSearch()
 * 
 * await search('find documents about machine learning', {
 *   type: 'document',
 *   tags: ['research']
 * }, {
 *   limit: 10,
 *   threshold: 0.7
 * })
 * 
 * // Hybrid search with text fallback
 * await hybridSearch(
 *   'semantic query',
 *   'keyword fallback',
 *   { type: 'article' },
 *   { weights: { vector: 0.8, text: 0.2 } }
 * )
 * ```
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 * @version 1.2.0
 */

/**
 * Configuration options for vector search operations
 * 
 * @interface VectorSearchOptions
 * @example
 * ```typescript
 * const options: VectorSearchOptions = {
 *   limit: 20,
 *   threshold: 0.8,
 *   distance: 'cosine',
 *   includeMetadata: true,
 *   rerank: true
 * }
 * ```
 */
export interface VectorSearchOptions {
  /** Maximum number of results to return (default: 10, max: 100) */
  limit?: number
  /** Minimum similarity threshold (0-1, higher = more similar) */
  threshold?: number
  /** Distance metric for similarity calculation */
  distance?: 'cosine' | 'euclidean' | 'dot'
  /** Include full metadata in results */
  includeMetadata?: boolean
  /** Re-rank results using additional scoring */
  rerank?: boolean
}

/**
 * Filtering options for vector search results
 * 
 * Filters are applied before vector similarity calculation to improve
 * performance and relevance. All filters respect workspace boundaries.
 * 
 * @interface VectorSearchFilters
 * @example
 * ```typescript
 * const filters: VectorSearchFilters = {
 *   type: 'document',
 *   tags: ['research', 'ml'],
 *   source: 'uploads',
 *   dateRange: {
 *     start: '2024-01-01',
 *     end: '2024-12-31'
 *   }
 * }
 * ```
 */
export interface VectorSearchFilters {
  /** Document type filter (e.g., 'document', 'note', 'article') */
  type?: string
  /** Array of tags for tag-based filtering */
  tags?: string[]
  /** Source system or upload origin */
  source?: string
  /** Date range filter for temporal scoping */
  dateRange?: {
    /** Start date in ISO 8601 format */
    start: string
    /** End date in ISO 8601 format */
    end: string
  }
}

/**
 * Single vector search result with similarity scoring and metadata
 * 
 * Each result includes the original content, comprehensive metadata,
 * and various similarity metrics for ranking and display purposes.
 * 
 * @interface VectorSearchResult
 * @example
 * ```typescript
 * const result: VectorSearchResult = {
 *   id: 'doc_123',
 *   content: 'Machine learning fundamentals...',
 *   metadata: {
 *     workspaceId: 'ws_456',
 *     userId: 'user_789',
 *     profileId: 'profile_012',
 *     type: 'document',
 *     source: 'upload',
 *     tags: ['ml', 'research'],
 *     createdAt: '2024-01-15T10:30:00Z',
 *     updatedAt: '2024-01-16T14:20:00Z'
 *   },
 *   score: 0.87,
 *   distance: 0.13,
 *   rank: 1,
 *   highlights: ['machine learning', 'neural networks']
 * }
 * ```
 */
export interface VectorSearchResult {
  /** Unique document identifier */
  id: string
  /** Full text content of the document */
  content: string
  /** Multi-tenant metadata with workspace isolation */
  metadata: {
    /** Workspace ID for tenant isolation */
    workspaceId: string
    /** User ID of document owner */
    userId: string
    /** Profile ID of document creator */
    profileId: string
    /** Document type classification */
    type: string
    /** Source system or origin */
    source?: string
    /** Associated tags for categorization */
    tags?: string[]
    /** Document creation timestamp */
    createdAt: string
    /** Last modification timestamp */
    updatedAt: string
  }
  /** Similarity score (0-1, higher = more similar) */
  score: number
  /** Vector distance (lower = more similar) */
  distance: number
  /** Ranking position in result set */
  rank: number
  /** Highlighted text segments matching the query */
  highlights?: string[]
}

/**
 * Reactive state management for vector search operations
 * 
 * Tracks the complete search lifecycle including query state,
 * results, loading status, and performance metrics.
 * 
 * @interface SearchState
 * @example
 * ```typescript
 * const state: SearchState = {
 *   query: 'machine learning algorithms',
 *   results: [],
 *   isLoading: false,
 *   error: null,
 *   hasSearched: true,
 *   searchType: 'vector',
 *   executionTime: 245,
 *   total: 15
 * }
 * ```
 */
export interface SearchState {
  /** Current search query string */
  query: string
  /** Array of search results */
  results: VectorSearchResult[]
  /** Loading state indicator */
  isLoading: boolean
  /** Error message if search failed */
  error: string | null
  /** Whether any search has been performed */
  hasSearched: boolean
  /** Type of search performed */
  searchType: 'vector' | 'hybrid' | 'text'
  /** Search execution time in milliseconds */
  executionTime: number
  /** Total number of results found */
  total: number
}

/**
 * Result structure for embedding generation operations
 * 
 * Contains the generated embedding vector along with metadata
 * and dimensional information for vector storage and retrieval.
 * 
 * @interface EmbeddingResult
 * @example
 * ```typescript
 * const embedding: EmbeddingResult = {
 *   id: 'emb_123',
 *   content: 'Original text content',
 *   dimensions: 768,
 *   metadata: {
 *     model: 'text-embedding-ada-002',
 *     tokens: 42,
 *     language: 'en'
 *   }
 * }
 * ```
 */
export interface EmbeddingResult {
  /** Unique identifier for the embedding */
  id: string
  /** Original text content that was embedded */
  content: string
  /** Vector dimensionality (e.g., 768 for OpenAI, 1024 for others) */
  dimensions: number
  /** Additional metadata about the embedding generation */
  metadata: {
    /** AI model used for embedding generation */
    model?: string
    /** Number of tokens processed */
    tokens?: number
    /** Content language code */
    language?: string
    /** Processing timestamp */
    timestamp?: string
    /** Any additional provider-specific metadata */
    [key: string]: any
  }
}

/**
 * Vector Search Composable
 * 
 * Provides comprehensive semantic search capabilities with multi-tenant support,
 * real-time suggestions, performance monitoring, and workspace isolation.
 * 
 * This composable integrates with AI embedding services and Firestore to deliver
 * fast, accurate semantic search across user content within workspace boundaries.
 * 
 * @function useVectorSearch
 * @returns {Object} Vector search interface with state and methods
 * 
 * @example
 * ```typescript
 * // Basic usage
 * const {
 *   search,
 *   hybridSearch,
 *   state,
 *   hasResults,
 *   clearSearch
 * } = useVectorSearch()
 * 
 * // Perform semantic search
 * const results = await search('find machine learning papers', {
 *   type: 'research'
 * }, {
 *   limit: 20,
 *   threshold: 0.7
 * })
 * 
 * // Monitor search state
 * watchEffect(() => {
 *   if (state.value.isLoading) {
 *     console.log('Searching...')
 *   }
 *   if (hasResults.value) {
 *     console.log(`Found ${state.value.total} results`)
 *   }
 * })
 * ```
 * 
 * @throws {Error} When search query is empty or invalid
 * @throws {Error} When workspace context is missing
 * @throws {Error} When AI service is unavailable
 * 
 * @see {@link VectorSearchOptions} for search configuration
 * @see {@link VectorSearchFilters} for filtering options
 * @see {@link VectorSearchResult} for result structure
 */
export function useVectorSearch() {
  // State
  const state = ref<SearchState>({
    query: '',
    results: [],
    isLoading: false,
    error: null,
    hasSearched: false,
    searchType: 'vector',
    executionTime: 0,
    total: 0
  })

  const searchHistory = ref<Array<{
    query: string
    timestamp: string
    resultsCount: number
  }>>([])

  // Computed
  const hasResults = computed(() => state.value.results.length > 0)
  const isEmpty = computed(() => state.value.hasSearched && state.value.results.length === 0)
  const isReady = computed(() => !state.value.isLoading && state.value.query.length > 0)

  /**
   * Perform semantic vector search with workspace scoping
   * 
   * Executes a vector-based semantic search against the current workspace's
   * content using AI embeddings for similarity matching. Results are automatically
   * filtered by workspace access and ranked by semantic similarity.
   * 
   * @async
   * @function search
   * @param {string} query - The search query text for semantic matching
   * @param {VectorSearchFilters} [filters={}] - Optional filters for result refinement
   * @param {VectorSearchOptions} [options={}] - Search configuration options
   * @returns {Promise<VectorSearchResult[]>} Array of ranked search results
   * 
   * @example
   * ```typescript
   * // Basic search
   * const results = await search('machine learning fundamentals')
   * 
   * // Advanced search with filters and options
   * const results = await search(
   *   'neural network architectures',
   *   {
   *     type: 'research',
   *     tags: ['ai', 'deep-learning'],
   *     dateRange: { start: '2024-01-01', end: '2024-12-31' }
   *   },
   *   {
   *     limit: 25,
   *     threshold: 0.8,
   *     distance: 'cosine',
   *     rerank: true
   *   }
   * )
   * ```
   * 
   * @throws {Error} When query is empty or only whitespace
   * @throws {Error} When workspace context is missing
   * @throws {Error} When AI embedding service fails
   * @throws {Error} When database query fails
   */
  const search = async (
    query: string,
    filters: VectorSearchFilters = {},
    options: VectorSearchOptions = {}
  ) => {
    if (!query.trim()) {
      throw new Error('Query cannot be empty')
    }

    state.value.isLoading = true
    state.value.error = null
    state.value.query = query

    try {
      const startTime = Date.now()
      
      const response = await $fetch('/api/ai/search', {
        method: 'POST',
        body: {
          query,
          type: 'vector',
          filters,
          ...options
        }
      })

      if (!response.success) {
        throw new Error(response.message || 'Search failed')
      }

      const executionTime = Date.now() - startTime

      state.value.results = response.data.results
      state.value.total = response.data.total
      state.value.executionTime = executionTime
      state.value.hasSearched = true
      state.value.searchType = 'vector'

      // Add to search history
      searchHistory.value.unshift({
        query,
        timestamp: new Date().toISOString(),
        resultsCount: response.data.total
      })

      // Keep only last 10 searches
      if (searchHistory.value.length > 10) {
        searchHistory.value.pop()
      }

      return response.data.results

    } catch (error) {
      console.error('🔥 [VectorSearch] Search failed:', error)
      const appError = ErrorHandler.handle(error)
      state.value.error = appError.message
      throw appError
    } finally {
      state.value.isLoading = false
    }
  }

  /**
   * Perform hybrid search combining vector and text search
   * 
   * Combines semantic vector search with traditional text search for improved
   * accuracy and coverage. Results are merged and re-ranked based on weighted
   * scores from both search methods.
   * 
   * @async
   * @function hybridSearch
   * @param {string} query - Primary semantic search query
   * @param {string} textQuery - Traditional text search query
   * @param {VectorSearchFilters} [filters={}] - Result filtering options
   * @param {Object} [options={}] - Extended search configuration
   * @param {Object} [options.weights] - Scoring weights for vector vs text
   * @param {string[]} [options.textFields] - Fields to search for text query
   * @returns {Promise<VectorSearchResult[]>} Merged and ranked results
   * 
   * @example
   * ```typescript
   * // Combine semantic and keyword search
   * const results = await hybridSearch(
   *   'machine learning concepts',    // Semantic query
   *   'neural networks OR deep learning', // Text query
   *   { type: 'tutorial' },
   *   {
   *     weights: { vector: 0.7, text: 0.3 },
   *     textFields: ['title', 'content', 'tags'],
   *     limit: 15
   *   }
   * )
   * ```
   * 
   * @throws {Error} When either query is empty
   * @throws {Error} When workspace context is missing
   * @throws {Error} When hybrid search service fails
   */
  const hybridSearch = async (
    query: string,
    textQuery: string,
    filters: VectorSearchFilters = {},
    options: VectorSearchOptions & {
      weights?: { vector: number; text: number }
      textFields?: string[]
    } = {}
  ) => {
    if (!query.trim() || !textQuery.trim()) {
      throw new Error('Both query and text query are required')
    }

    state.value.isLoading = true
    state.value.error = null
    state.value.query = `${query} | ${textQuery}`

    try {
      const startTime = Date.now()
      
      const response = await $fetch('/api/ai/search', {
        method: 'POST',
        body: {
          query,
          textQuery,
          type: 'hybrid',
          filters,
          weights: options.weights || { vector: 0.7, text: 0.3 },
          textFields: options.textFields || ['content', 'metadata.source'],
          ...options
        }
      })

      if (!response.success) {
        throw new Error(response.message || 'Hybrid search failed')
      }

      const executionTime = Date.now() - startTime

      state.value.results = response.data.results
      state.value.total = response.data.total
      state.value.executionTime = executionTime
      state.value.hasSearched = true
      state.value.searchType = 'hybrid'

      // Add to search history
      searchHistory.value.unshift({
        query: state.value.query,
        timestamp: new Date().toISOString(),
        resultsCount: response.data.total
      })

      return response.data.results

    } catch (error) {
      console.error('🔥 [VectorSearch] Hybrid search failed:', error)
      const appError = ErrorHandler.handle(error)
      state.value.error = appError.message
      throw appError
    } finally {
      state.value.isLoading = false
    }
  }

  /**
   * Find documents similar to a given document
   * 
   * Uses the embedding vector of an existing document to find semantically
   * similar content within the workspace. Useful for content recommendations
   * and related document discovery.
   * 
   * @async
   * @function findSimilar
   * @param {string} documentId - ID of the reference document
   * @param {Object} [options={}] - Similarity search configuration
   * @param {number} [options.limit=10] - Maximum results to return
   * @param {number} [options.threshold=0.5] - Minimum similarity threshold
   * @param {string[]} [options.excludeIds] - Document IDs to exclude
   * @param {boolean} [options.sameTypeOnly=false] - Only find same document type
   * @returns {Promise<VectorSearchResult[]>} Similar documents ranked by similarity
   * 
   * @example
   * ```typescript
   * // Find documents similar to a research paper
   * const similar = await findSimilar('doc_123', {
   *   limit: 10,
   *   threshold: 0.7,
   *   excludeIds: ['doc_456', 'doc_789'],
   *   sameTypeOnly: true
   * })
   * ```
   * 
   * @throws {Error} When document ID is invalid or not found
   * @throws {Error} When user lacks access to reference document
   * @throws {Error} When similarity service fails
   */
  const findSimilar = async (
    documentId: string,
    options: {
      limit?: number
      threshold?: number
      excludeIds?: string[]
      sameTypeOnly?: boolean
    } = {}
  ) => {
    state.value.isLoading = true
    state.value.error = null

    try {
      const response = await $fetch('/api/ai/similar', {
        method: 'POST',
        body: {
          documentId,
          ...options
        }
      })

      if (!response.success) {
        throw new Error(response.message || 'Similar search failed')
      }

      return response.data.results

    } catch (error) {
      console.error('🔥 [VectorSearch] Similar search failed:', error)
      const appError = ErrorHandler.handle(error)
      state.value.error = appError.message
      throw appError
    } finally {
      state.value.isLoading = false
    }
  }

  /**
   * Generate AI embedding vector for text content
   * 
   * Creates a high-dimensional vector representation of text content using
   * AI embedding models. The embedding can be used for semantic search,
   * similarity comparison, and content clustering.
   * 
   * @async
   * @function generateEmbedding
   * @param {string} content - Text content to embed
   * @param {Object} [metadata={}] - Optional metadata for the embedding
   * @param {string} [metadata.type] - Content type classification
   * @param {string} [metadata.source] - Source system or origin
   * @param {string[]} [metadata.tags] - Associated tags
   * @param {string} [metadata.language='en'] - Content language code
   * @returns {Promise<EmbeddingResult>} Generated embedding with metadata
   * 
   * @example
   * ```typescript
   * // Generate embedding for document content
   * const embedding = await generateEmbedding(
   *   'Machine learning is a subset of artificial intelligence...',
   *   {
   *     type: 'article',
   *     source: 'user_upload',
   *     tags: ['ml', 'ai', 'technology'],
   *     language: 'en'
   *   }
   * )
   * 
   * console.log(`Generated ${embedding.dimensions}D vector`)
   * ```
   * 
   * @throws {Error} When content is empty or only whitespace
   * @throws {Error} When content exceeds token limits
   * @throws {Error} When AI embedding service is unavailable
   */
  const generateEmbedding = async (
    content: string,
    metadata: {
      type?: string
      source?: string
      tags?: string[]
      language?: string
    } = {}
  ): Promise<EmbeddingResult> => {
    if (!content.trim()) {
      throw new Error('Content cannot be empty')
    }

    try {
      const response = await $fetch('/api/ai/embed', {
        method: 'POST',
        body: {
          content,
          ...metadata
        }
      })

      if (!response.success) {
        throw new Error(response.message || 'Embedding generation failed')
      }

      return response.data

    } catch (error) {
      console.error('🔥 [VectorSearch] Embedding generation failed:', error)
      const appError = ErrorHandler.handle(error)
      throw appError
    }
  }

  /**
   * Generate embeddings for multiple content items in batch
   * 
   * Efficiently processes multiple text items to generate embeddings
   * in a single API call. Optimized for bulk operations and content
   * ingestion workflows.
   * 
   * @async
   * @function batchGenerateEmbeddings
   * @param {Array<Object>} items - Array of content items to embed
   * @param {string} items[].content - Text content to embed
   * @param {string} [items[].type] - Content type classification
   * @param {string} [items[].source] - Source system identifier
   * @param {string[]} [items[].tags] - Associated tags
   * @param {string} [items[].language] - Content language code
   * @returns {Promise<EmbeddingResult[]>} Array of generated embeddings
   * 
   * @example
   * ```typescript
   * // Batch process multiple documents
   * const embeddings = await batchGenerateEmbeddings([
   *   {
   *     content: 'First document content...',
   *     type: 'article',
   *     tags: ['tech', 'ai']
   *   },
   *   {
   *     content: 'Second document content...',
   *     type: 'research',
   *     tags: ['science', 'ml']
   *   }
   * ])
   * 
   * console.log(`Generated ${embeddings.length} embeddings`)
   * ```
   * 
   * @throws {Error} When items array is empty
   * @throws {Error} When batch size exceeds service limits
   * @throws {Error} When batch processing fails
   */
  const batchGenerateEmbeddings = async (
    items: Array<{
      content: string
      type?: string
      source?: string
      tags?: string[]
      language?: string
    }>
  ): Promise<EmbeddingResult[]> => {
    if (!items.length) {
      throw new Error('Items array cannot be empty')
    }

    try {
      const response = await $fetch('/api/ai/batch-embed', {
        method: 'POST',
        body: {
          items
        }
      })

      if (!response.success) {
        throw new Error(response.message || 'Batch embedding generation failed')
      }

      return response.data.results

    } catch (error) {
      console.error('🔥 [VectorSearch] Batch embedding generation failed:', error)
      const appError = ErrorHandler.handle(error)
      throw appError
    }
  }

  /**
   * Retrieve search analytics and performance metrics
   * 
   * Provides insights into search usage patterns, performance metrics,
   * and user behavior analytics for the current workspace.
   * 
   * @async
   * @function getSearchAnalytics
   * @param {Object} [dateRange] - Optional date range for analytics
   * @param {string} dateRange.start - Start date in ISO format
   * @param {string} dateRange.end - End date in ISO format
   * @returns {Promise<Object>} Search analytics data
   * 
   * @example
   * ```typescript
   * // Get analytics for last 30 days
   * const analytics = await getSearchAnalytics({
   *   start: '2024-01-01T00:00:00Z',
   *   end: '2024-01-31T23:59:59Z'
   * })
   * 
   * console.log(`Total searches: ${analytics.totalSearches}`)
   * console.log(`Average response time: ${analytics.avgResponseTime}ms`)
   * ```
   * 
   * @throws {Error} When analytics service is unavailable
   * @throws {Error} When date range is invalid
   */
  const getSearchAnalytics = async (dateRange?: { start: string; end: string }) => {
    try {
      const params = new URLSearchParams()
      if (dateRange) {
        params.append('startDate', dateRange.start)
        params.append('endDate', dateRange.end)
      }

      const response = await $fetch(`/api/ai/analytics?${params}`)

      if (!response.success) {
        throw new Error(response.message || 'Failed to get analytics')
      }

      return response.data

    } catch (error) {
      console.error('🔥 [VectorSearch] Analytics failed:', error)
      const appError = ErrorHandler.handle(error)
      throw appError
    }
  }

  // Refresh embeddings
  const refreshEmbeddings = async (embeddingIds: string[]) => {
    if (!embeddingIds.length) {
      throw new Error('Embedding IDs array cannot be empty')
    }

    try {
      const response = await $fetch('/api/ai/refresh', {
        method: 'POST',
        body: {
          embeddingIds
        }
      })

      if (!response.success) {
        throw new Error(response.message || 'Failed to refresh embeddings')
      }

      return response.data

    } catch (error) {
      console.error('🔥 [VectorSearch] Refresh failed:', error)
      const appError = ErrorHandler.handle(error)
      throw appError
    }
  }

  // Clear search state
  const clearSearch = () => {
    state.value = {
      query: '',
      results: [],
      isLoading: false,
      error: null,
      hasSearched: false,
      searchType: 'vector',
      executionTime: 0,
      total: 0
    }
  }

  // Clear search history
  const clearSearchHistory = () => {
    searchHistory.value = []
  }

  // Process search results for display
  const processResults = (results: VectorSearchResult[]) => {
    return results.map(result => ({
      ...result,
      summary: result.content.length > 200 
        ? result.content.substring(0, 200) + '...' 
        : result.content,
      formattedScore: Math.round(result.score * 100),
      relativeDate: formatRelativeTime(result.metadata.createdAt)
    }))
  }

  // Format relative time
  const formatRelativeTime = (dateString: string): string => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

    if (diffInSeconds < 60) return 'just now'
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
    if (diffInSeconds < 2592000) return `${Math.floor(diffInSeconds / 86400)}d ago`
    return date.toLocaleDateString()
  }

  // Watch for query changes to provide suggestions
  const querySuggestions = ref<string[]>([])
  
  watch(() => state.value.query, async (newQuery) => {
    if (newQuery.length > 2) {
      // Simple suggestion logic based on search history
      querySuggestions.value = searchHistory.value
        .filter(item => item.query.toLowerCase().includes(newQuery.toLowerCase()))
        .map(item => item.query)
        .slice(0, 5)
    } else {
      querySuggestions.value = []
    }
  })

  return {
    // State
    state: readonly(state),
    searchHistory: readonly(searchHistory),
    querySuggestions: readonly(querySuggestions),
    
    // Computed
    hasResults,
    isEmpty,
    isReady,
    
    // Methods
    search,
    hybridSearch,
    findSimilar,
    generateEmbedding,
    batchGenerateEmbeddings,
    getSearchAnalytics,
    refreshEmbeddings,
    clearSearch,
    clearSearchHistory,
    processResults,
    formatRelativeTime
  }
}

/**
 * Utility functions for vector search operations
 * 
 * Collection of helper functions for text processing, highlighting,
 * relevance scoring, and search result enhancement.
 * 
 * @namespace vectorSearchUtils
 * @example
 * ```typescript
 * import { vectorSearchUtils } from './useVectorSearch'
 * 
 * // Highlight search terms
 * const highlighted = vectorSearchUtils.highlightText(content, query)
 * 
 * // Extract key phrases
 * const phrases = vectorSearchUtils.extractKeyPhrases(content, 5)
 * 
 * // Get relevance information
 * const label = vectorSearchUtils.getRelevanceLabel(0.85)
 * const color = vectorSearchUtils.getRelevanceColor(0.85)
 * ```
 */
export const vectorSearchUtils = {
  /**
   * Highlight query matches in text content
   * 
   * Wraps matching text segments with HTML mark tags for visual highlighting.
   * Case-insensitive matching with support for partial word matches.
   * 
   * @function highlightText
   * @param {string} text - The text content to highlight
   * @param {string} query - The search query to highlight
   * @returns {string} HTML string with highlighted matches
   * 
   * @example
   * ```typescript
   * const highlighted = vectorSearchUtils.highlightText(
   *   'Machine learning algorithms are powerful tools',
   *   'machine learning'
   * )
   * // Returns: '<mark>Machine learning</mark> algorithms are powerful tools'
   * ```
   */
  highlightText: (text: string, query: string): string => {
    if (!query.trim()) return text
    
    const regex = new RegExp(`(${query})`, 'gi')
    return text.replace(regex, '<mark>$1</mark>')
  },

  /**
   * Extract key phrases from text using frequency analysis
   * 
   * Identifies the most frequently occurring meaningful words in text
   * content, filtering out common words and short terms.
   * 
   * @function extractKeyPhrases
   * @param {string} text - The text to analyze
   * @param {number} [maxPhrases=5] - Maximum number of phrases to return
   * @returns {string[]} Array of key phrases sorted by frequency
   * 
   * @example
   * ```typescript
   * const phrases = vectorSearchUtils.extractKeyPhrases(
   *   'Machine learning algorithms enable computers to learn patterns...',
   *   3
   * )
   * // Returns: ['machine', 'learning', 'algorithms']
   * ```
   */
  extractKeyPhrases: (text: string, maxPhrases: number = 5): string[] => {
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3)

    // Simple frequency analysis
    const wordCounts: Record<string, number> = {}
    for (const word of words) {
      wordCounts[word] = (wordCounts[word] || 0) + 1
    }

    return Object.entries(wordCounts)
      .sort(([,a], [,b]) => b - a)
      .slice(0, maxPhrases)
      .map(([word]) => word)
  },

  /**
   * Get human-readable relevance label for similarity scores
   * 
   * Converts numeric similarity scores into descriptive labels
   * for better user understanding of search result quality.
   * 
   * @function getRelevanceLabel
   * @param {number} score - Similarity score between 0 and 1
   * @returns {string} Descriptive relevance label
   * 
   * @example
   * ```typescript
   * const label1 = vectorSearchUtils.getRelevanceLabel(0.9)  // 'Highly Relevant'
   * const label2 = vectorSearchUtils.getRelevanceLabel(0.5)  // 'Somewhat Relevant'
   * const label3 = vectorSearchUtils.getRelevanceLabel(0.2)  // 'Low Relevance'
   * ```
   */
  getRelevanceLabel: (score: number): string => {
    if (score >= 0.8) return 'Highly Relevant'
    if (score >= 0.6) return 'Relevant'
    if (score >= 0.4) return 'Somewhat Relevant'
    return 'Low Relevance'
  },

  /**
   * Get color code for relevance score visualization
   * 
   * Returns appropriate color codes for displaying similarity scores
   * in UI components with consistent visual feedback.
   * 
   * @function getRelevanceColor
   * @param {number} score - Similarity score between 0 and 1
   * @returns {string} Color identifier for UI styling
   * 
   * @example
   * ```typescript
   * const color1 = vectorSearchUtils.getRelevanceColor(0.9)  // 'success'
   * const color2 = vectorSearchUtils.getRelevanceColor(0.6)  // 'info'
   * const color3 = vectorSearchUtils.getRelevanceColor(0.3)  // 'warning'
   * ```
   */
  getRelevanceColor: (score: number): string => {
    if (score >= 0.8) return 'success'
    if (score >= 0.6) return 'info'
    if (score >= 0.4) return 'warning'
    return 'muted'
  }
}

// Export types for external use
export type {
  VectorSearchOptions,
  VectorSearchFilters,
  VectorSearchResult,
  SearchState,
  EmbeddingResult
}